"""
Helper functions for AI integration.
This module contains pure utility functions that support the main AI integration module.
These functions do not directly interact with the Google AI API.
"""

import os
import json
import time
import re
import uuid
import logging
import logging.handlers
import traceback
import inspect
import sys
from typing import Dict, Any, List, Callable
from datetime import datetime, timedelta
from functools import wraps
import streamlit as st

# Configure logging
logger = logging.getLogger("ScriptWeaver.core.ai_helpers")

# Define paths for metrics logging
AI_LOGS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ai_logs")
AI_LOGS_METRICS_DIR = os.path.join(AI_LOGS_DIR, "metrics")

# Ensure metrics directory exists
os.makedirs(AI_LOGS_METRICS_DIR, exist_ok=True)


class LoggingManager:
    """
    Centralized logging configuration manager.
    Handles setup of file and console logging, directory creation, and log file paths.
    """

    _instance = None

    @classmethod
    def get_instance(cls):
        """Get or create the singleton instance of LoggingManager."""
        if cls._instance is None:
            cls._instance = LoggingManager()
        return cls._instance

    def __init__(self):
        # Define base directories
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.log_dir = os.path.join(self.base_dir, "logs")
        self.ai_logs_dir = os.path.join(self.base_dir, "ai_logs")

        # Define subdirectories
        self.requests_dir = os.path.join(self.ai_logs_dir, "requests")
        self.errors_dir = os.path.join(self.ai_logs_dir, "errors")
        self.metrics_dir = os.path.join(self.ai_logs_dir, "metrics")

        # Create all required directories
        self._create_log_directories()

        # Configure the logger
        self.logger = self._setup_logger()

    def _create_log_directories(self):
        """Create all necessary log directories."""
        for directory in [self.log_dir, self.ai_logs_dir,
                         self.requests_dir, self.errors_dir, self.metrics_dir]:
            os.makedirs(directory, exist_ok=True)

    def _setup_logger(self):
        """Set up and configure the logger with file and console handlers."""
        # Create a rotating file handler
        log_file = os.path.join(self.log_dir, "scriptweaver_ai.log")
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))

        # Configure console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))

        # Get logger and add handlers
        logger = logging.getLogger("ScriptWeaver.core.ai")
        logger.setLevel(logging.INFO)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def get_log_path(self, category, function_name, request_id, is_error=False):
        """
        Generate a log file path based on category, function name, and request ID.

        Args:
            category (str): Category of the log (e.g., "script_generation")
            function_name (str): Name of the function being logged
            request_id (str): Unique request ID
            is_error (bool): Whether this is an error log

        Returns:
            str: Full path to the log file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{category}_{function_name}_{timestamp}_{request_id[:8]}.txt"
        target_dir = self.errors_dir if is_error else self.requests_dir
        return os.path.join(target_dir, filename)


class RequestTracker:
    """
    Tracks request contexts and manages request relationships.
    Replaces global request_contexts dictionary with encapsulated state.
    """

    _instance = None

    @classmethod
    def get_instance(cls):
        """Get or create the singleton instance of RequestTracker."""
        if cls._instance is None:
            cls._instance = RequestTracker()
        return cls._instance

    def __init__(self):
        self.request_contexts = {}

    def add_request(self, request_id, context_data):
        """Add a new request context."""
        self.request_contexts[request_id] = context_data

    def get_request(self, request_id):
        """Get a request context by ID."""
        return self.request_contexts.get(request_id)

    def link_requests(self, parent_id, child_id):
        """Link a child request to its parent."""
        if parent_id and parent_id in self.request_contexts:
            if 'child_request_ids' not in self.request_contexts[parent_id]:
                self.request_contexts[parent_id]['child_request_ids'] = []
            self.request_contexts[parent_id]['child_request_ids'].append(child_id)

    def update_parent_log(self, parent_id, child_id, child_filepath):
        """Update the parent log file with child request information."""
        try:
            if parent_id and parent_id in self.request_contexts:
                parent_filepath = self.request_contexts[parent_id].get('filepath')
                if parent_filepath and os.path.exists(parent_filepath):
                    with open(parent_filepath, 'a', encoding='utf-8') as f:
                        f.write(f"\n\nCHILD REQUEST: {child_id} - {child_filepath}\n")
        except Exception as e:
            logger.warning(f"Failed to update parent log file with child request ID: {e}")


class TokenUsageTracker:
    """
    Tracks token usage across API calls.
    Replaces global token usage tracking with encapsulated state.
    """

    _instance = None

    @classmethod
    def get_instance(cls):
        """Get or create the singleton instance of TokenUsageTracker."""
        if cls._instance is None:
            cls._instance = TokenUsageTracker()
        return cls._instance

    def __init__(self):
        self.token_usage = []

    def add_usage(self, tokens):
        """Add a token usage record."""
        self.token_usage.append((time.time(), tokens))

    def get_recent_usage(self, minutes=60):
        """Get token usage in the last N minutes."""
        cutoff_time = time.time() - (minutes * 60)
        recent_usage = [usage for timestamp, usage in self.token_usage if timestamp >= cutoff_time]
        return sum(recent_usage)

    def get_total_usage(self):
        """Get total token usage."""
        return sum(usage for _, usage in self.token_usage)


def error_handler(func=None, default_value=None):
    """
    Decorator for standardized error handling across functions.
    Logs errors and returns a default value on failure.

    Args:
        func (callable, optional): The function to wrap with error handling
        default_value (Any, optional): Default value to return on error

    Returns:
        callable: The wrapped function with error handling
    """
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            try:
                return f(*args, **kwargs)
            except Exception as e:
                # Get function name for better error messages
                func_name = f.__name__

                # Log the error with traceback
                logger.error(f"Error in {func_name}: {e}")
                logger.debug(f"Traceback: {traceback.format_exc()}")

                # Return appropriate default values based on function or provided default
                if default_value is not None:
                    return default_value

                # Function-specific defaults
                if func_name == 'initialize_ai_client':
                    return False
                elif func_name == 'generate_llm_response':
                    return ""
                elif func_name == 'convert_test_case_to_step_table':
                    return f"Error: {str(e)}", []
                elif func_name == 'generate_test_script':
                    return f"Error: {str(e)}", f"Error: {str(e)}"
                else:
                    # Generic default: return None
                    return None
        return wrapper

    # Handle both @error_handler and @error_handler(default_value=...)
    if func is None:
        return decorator
    return decorator(func)

def capture_call_stack(skip_frames=1, is_prompt_generation=False) -> Dict[str, Any]:
    """
    Capture detailed information about the current call stack.

    Args:
        skip_frames (int): Number of frames to skip from the top of the stack
                          (to avoid including this function and its caller)
        is_prompt_generation (bool): Whether this is for prompt generation tracing
                                    (affects how arguments are processed)

    Returns:
        Dict[str, Any]: Dictionary containing call stack information
    """
    stack_info = []
    prompt_variables = {}
    template_data = {}

    # Get the current call stack
    stack = inspect.stack()[skip_frames:]

    # Process each frame in the stack
    for frame_info in stack:
        frame = frame_info.frame
        code = frame_info.code_context[0].strip() if frame_info.code_context else "Unknown code context"

        # Get function arguments
        args_info = {}
        try:
            args, varargs, keywords, local_vars = inspect.getargvalues(frame)

            # Check if this is a prompt generation function
            is_prompt_func = any(name in frame_info.function for name in [
                'prompt', 'generate_test_script_prompt', 'generate_test_data_prompt',
                'convert_test_case_to_step_table', 'merge_scripts_with_ai'
            ])

            # For prompt generation functions, capture more detailed information
            if is_prompt_generation and is_prompt_func:
                # Look for template variables in the local scope
                for var_name, var_value in local_vars.items():
                    # Skip function arguments, we'll handle those separately
                    if var_name in args or var_name == varargs or var_name == keywords:
                        continue

                    # Look for variables that might be used in template construction
                    if isinstance(var_value, (str, int, float, bool)) and not var_name.startswith('_'):
                        prompt_variables[var_name] = var_value
                    elif isinstance(var_value, dict) and len(var_value) < 50:
                        # For smaller dictionaries, include the full content
                        template_data[var_name] = var_value

            # Add all arguments to the args_info dictionary
            for arg in args:
                # Skip 'self' for class methods
                if arg == 'self':
                    continue

                # Get the argument value
                value = local_vars[arg]

                # Handle special cases for common error types
                if isinstance(value, list) and 'index' in code and 'out of range' in code:
                    # For list index out of range errors, include the list and attempted index
                    args_info[arg] = {
                        'type': type(value).__name__,
                        'length': len(value),
                        'value': str(value)[:1000] if len(str(value)) > 1000 else str(value),
                        'attempted_index': None  # Will be filled later if possible
                    }
                elif isinstance(value, (str, int, float, bool, type(None))):
                    # For simple types, include the full value
                    args_info[arg] = value
                elif isinstance(value, dict):
                    # For dictionaries, include more details for prompt generation
                    if is_prompt_generation and is_prompt_func and len(value) < 30:
                        # For prompt generation, include more complete dictionary content
                        try:
                            # Try to convert to JSON to ensure it's serializable
                            json_str = json.dumps(value)
                            # If successful, include the full dictionary
                            args_info[arg] = value
                        except:
                            # If not serializable, fall back to summary
                            args_info[arg] = {
                                'type': 'dict',
                                'length': len(value),
                                'keys': list(value.keys())[:20] if len(value) > 20 else list(value.keys())
                            }
                    else:
                        # For regular logging, include a summary
                        args_info[arg] = {
                            'type': 'dict',
                            'length': len(value),
                            'keys': list(value.keys())[:20] if len(value) > 20 else list(value.keys())
                        }
                elif isinstance(value, (list, tuple)):
                    # For lists and tuples, include more details for prompt generation
                    if is_prompt_generation and is_prompt_func and len(value) < 20:
                        # For smaller lists in prompt generation, include full content
                        try:
                            # Try to convert to JSON to ensure it's serializable
                            json_str = json.dumps(value)
                            # If successful, include the full list
                            args_info[arg] = value
                        except:
                            # If not serializable, fall back to summary
                            args_info[arg] = {
                                'type': type(value).__name__,
                                'length': len(value),
                                'sample': str(value[:5])[:1000] + ('...' if len(value) > 5 else '')
                            }
                    else:
                        # For regular logging or larger lists, include a summary
                        args_info[arg] = {
                            'type': type(value).__name__,
                            'length': len(value),
                            'sample': str(value[:5])[:1000] + ('...' if len(value) > 5 else '')
                        }
                else:
                    # For other types, include type information
                    args_info[arg] = {
                        'type': type(value).__name__,
                        'repr': str(value)[:1000] if len(str(value)) > 1000 else str(value)
                    }

                    # For prompt generation functions, try to extract more information
                    if is_prompt_generation and is_prompt_func:
                        # Check if this is a test case object
                        if arg in ['test_case', 'test_cases'] or 'test_case' in arg:
                            # Try to extract key information from test case objects
                            try:
                                if hasattr(value, 'get'):
                                    test_case_info = {
                                        'id': value.get('Test Case ID', value.get('id', 'unknown')),
                                        'objective': value.get('Test Case Objective', value.get('objective', 'unknown')),
                                        'steps_count': len(value.get('Steps', value.get('steps', [])))
                                    }
                                    args_info[arg] = test_case_info
                            except:
                                pass

            # Add varargs if present
            if varargs and varargs in local_vars and local_vars[varargs]:
                args_info['*args'] = {
                    'type': 'tuple',
                    'length': len(local_vars[varargs]),
                    'values': str(local_vars[varargs])[:1000]
                }

            # Add kwargs if present
            if keywords and keywords in local_vars and local_vars[keywords]:
                kwargs_dict = local_vars[keywords]

                # For prompt generation, include more complete kwargs
                if is_prompt_generation and is_prompt_func and len(kwargs_dict) < 20:
                    try:
                        # Try to convert to JSON to ensure it's serializable
                        json_str = json.dumps(kwargs_dict)
                        # If successful, include the full kwargs
                        args_info['**kwargs'] = kwargs_dict
                    except:
                        # If not serializable, fall back to summary
                        args_info['**kwargs'] = {
                            'type': 'dict',
                            'length': len(kwargs_dict),
                            'keys': list(kwargs_dict.keys())
                        }
                else:
                    # For regular logging, include a summary
                    args_info['**kwargs'] = {
                        'type': 'dict',
                        'length': len(kwargs_dict),
                        'keys': list(kwargs_dict.keys())
                    }
        except Exception as e:
            args_info['error_capturing_args'] = str(e)

        # Add frame information to the stack
        stack_info.append({
            'filename': frame_info.filename,
            'lineno': frame_info.lineno,
            'function': frame_info.function,
            'code': code,
            'args': args_info
        })

    # Try to extract index information for list index errors
    if sys.exc_info()[0] is not None:
        exc_type, exc_value, exc_traceback = sys.exc_info()
        if exc_type is IndexError:
            # Extract the index from the error message
            error_msg = str(exc_value)
            if 'index' in error_msg:
                try:
                    # Try to parse the index from the error message
                    index_str = error_msg.split('index')[1].strip()
                    if index_str.startswith('out of range'):
                        # Handle "index out of range" without explicit index
                        pass
                    else:
                        # Extract the index number
                        import re
                        index_match = re.search(r'-?\d+', index_str)
                        if index_match:
                            index = int(index_match.group())

                            # Update the args_info for any list arguments
                            for frame_data in stack_info:
                                for arg_name, arg_info in frame_data['args'].items():
                                    if isinstance(arg_info, dict) and arg_info.get('type') in ('list', 'tuple'):
                                        arg_info['attempted_index'] = index
                except Exception:
                    pass

    result = {
        'stack': stack_info,
        'exception': {
            'type': sys.exc_info()[0].__name__ if sys.exc_info()[0] else None,
            'message': str(sys.exc_info()[1]) if sys.exc_info()[1] else None,
            'traceback': traceback.format_exc() if sys.exc_info()[0] else None
        }
    }

    # Add prompt generation specific information if applicable
    if is_prompt_generation:
        result['prompt_variables'] = prompt_variables
        result['template_data'] = template_data

    return result

def log_metrics_to_csv(request_id, function_name, category, model_name,
                      input_tokens, output_tokens, latency_ms, is_error):
    """
    Log metrics to a CSV file for later analysis.

    Args:
        request_id (str): The unique request ID
        function_name (str): Name of the function making the API call
        category (str): Category of the interaction
        model_name (str): The AI model used
        input_tokens (int): Number of input tokens
        output_tokens (int): Number of output tokens
        latency_ms (float): Request latency in milliseconds
        is_error (bool): Whether the request resulted in an error
    """
    try:
        # Create a daily metrics file
        today = datetime.now().strftime("%Y%m%d")
        metrics_file = os.path.join(AI_LOGS_METRICS_DIR, f"ai_metrics_{today}.csv")

        # Check if file exists to determine if we need to write headers
        file_exists = os.path.isfile(metrics_file)

        with open(metrics_file, 'a', encoding='utf-8') as f:
            # Write headers if file is new
            if not file_exists:
                f.write("timestamp,request_id,function_name,category,model_name,input_tokens,output_tokens,total_tokens,latency_ms,is_error\n")

            # Write the metrics row
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            total_tokens = (input_tokens or 0) + (output_tokens or 0) if input_tokens is not None and output_tokens is not None else ""
            f.write(f"{timestamp},{request_id},{function_name},{category},{model_name},{input_tokens or ''},{output_tokens or ''},{total_tokens},{latency_ms or ''},{is_error}\n")
    except Exception as e:
        logger.error(f"Error logging metrics to CSV: {e}")

def get_daily_usage_summary(date_str=None):
    """
    Generate a summary of AI usage for the specified day or current day if not specified.

    Args:
        date_str (str, optional): Date in YYYYMMDD format. Defaults to today.

    Returns:
        dict: Summary statistics including total requests, tokens, and errors
    """
    try:
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")

        metrics_file = os.path.join(AI_LOGS_METRICS_DIR, f"ai_metrics_{date_str}.csv")

        if not os.path.isfile(metrics_file):
            return {
                "date": date_str,
                "total_requests": 0,
                "total_input_tokens": 0,
                "total_output_tokens": 0,
                "total_tokens": 0,
                "error_count": 0,
                "avg_latency_ms": 0,
                "requests_by_category": {},
                "requests_by_model": {},
                "requests_by_function": {},
                "latency_percentiles": {},
                "token_usage_by_hour": {}
            }

        # Parse the CSV file
        requests = []
        with open(metrics_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            headers = lines[0].strip().split(',')

            for line in lines[1:]:
                values = line.strip().split(',')
                request = {headers[i]: values[i] for i in range(len(headers))}
                requests.append(request)

        # Calculate summary statistics
        total_requests = len(requests)
        total_input_tokens = sum(int(r['input_tokens']) for r in requests if r['input_tokens'])
        total_output_tokens = sum(int(r['output_tokens']) for r in requests if r['output_tokens'])
        total_tokens = total_input_tokens + total_output_tokens
        error_count = sum(1 for r in requests if r['is_error'] == 'True')

        # Calculate average latency
        latencies = [float(r['latency_ms']) for r in requests if r['latency_ms']]
        avg_latency_ms = sum(latencies) / len(latencies) if latencies else 0

        # Calculate latency percentiles
        latency_percentiles = {}
        if latencies:
            latencies.sort()
            latency_percentiles = {
                "p50": latencies[int(len(latencies) * 0.5)] if latencies else 0,
                "p90": latencies[int(len(latencies) * 0.9)] if latencies else 0,
                "p95": latencies[int(len(latencies) * 0.95)] if latencies else 0,
                "p99": latencies[int(len(latencies) * 0.99)] if latencies else 0,
                "min": latencies[0] if latencies else 0,
                "max": latencies[-1] if latencies else 0
            }

        # Group by category
        categories = {}
        for r in requests:
            category = r['category']
            if category not in categories:
                categories[category] = 0
            categories[category] += 1

        # Group by model
        models = {}
        for r in requests:
            model = r['model_name']
            if model not in models:
                models[model] = 0
            models[model] += 1

        # Group by function
        functions = {}
        for r in requests:
            function = r['function_name']
            if function not in functions:
                functions[function] = 0
            functions[function] += 1

        # Group token usage by hour
        token_usage_by_hour = {}
        for r in requests:
            if not r['timestamp']:
                continue

            try:
                # Parse timestamp (format: YYYY-MM-DD HH:MM:SS)
                dt = datetime.strptime(r['timestamp'], "%Y-%m-%d %H:%M:%S")
                hour = dt.hour

                if hour not in token_usage_by_hour:
                    token_usage_by_hour[hour] = {
                        "input_tokens": 0,
                        "output_tokens": 0,
                        "total_tokens": 0,
                        "request_count": 0
                    }

                # Add token counts
                input_tokens = int(r['input_tokens']) if r['input_tokens'] else 0
                output_tokens = int(r['output_tokens']) if r['output_tokens'] else 0

                token_usage_by_hour[hour]["input_tokens"] += input_tokens
                token_usage_by_hour[hour]["output_tokens"] += output_tokens
                token_usage_by_hour[hour]["total_tokens"] += input_tokens + output_tokens
                token_usage_by_hour[hour]["request_count"] += 1
            except (ValueError, KeyError):
                # Skip entries with invalid timestamps
                continue

        # Calculate estimated cost (based on approximate pricing)
        # These are rough estimates and should be adjusted based on actual pricing
        cost_estimates = {
            "gemini-1.5-flash": {
                "input_per_1k": 0.0001,  # $0.0001 per 1K input tokens
                "output_per_1k": 0.0005,  # $0.0005 per 1K output tokens
            },
            "gemini-1.5-pro": {
                "input_per_1k": 0.0005,  # $0.0005 per 1K input tokens
                "output_per_1k": 0.0015,  # $0.0015 per 1K output tokens
            }
        }

        # Calculate cost by model
        cost_by_model = {}
        total_cost = 0

        for r in requests:
            model = r['model_name']
            input_tokens = int(r['input_tokens']) if r['input_tokens'] else 0
            output_tokens = int(r['output_tokens']) if r['output_tokens'] else 0

            # Skip if model not in our pricing table
            if model not in cost_estimates:
                continue

            # Calculate cost for this request
            input_cost = (input_tokens / 1000) * cost_estimates[model]["input_per_1k"]
            output_cost = (output_tokens / 1000) * cost_estimates[model]["output_per_1k"]
            request_cost = input_cost + output_cost

            # Add to model-specific cost
            if model not in cost_by_model:
                cost_by_model[model] = 0
            cost_by_model[model] += request_cost

            # Add to total cost
            total_cost += request_cost

        return {
            "date": date_str,
            "total_requests": total_requests,
            "total_input_tokens": total_input_tokens,
            "total_output_tokens": total_output_tokens,
            "total_tokens": total_tokens,
            "error_count": error_count,
            "avg_latency_ms": avg_latency_ms,
            "requests_by_category": categories,
            "requests_by_model": models,
            "requests_by_function": functions,
            "latency_percentiles": latency_percentiles,
            "token_usage_by_hour": token_usage_by_hour,
            "cost_by_model": cost_by_model,
            "total_estimated_cost": total_cost
        }
    except Exception as e:
        logger.error(f"Error generating usage summary: {e}")
        return {"error": str(e)}

def generate_usage_report(start_date=None, end_date=None, output_format="text"):
    """
    Generate a comprehensive usage report for a date range.

    Args:
        start_date (str, optional): Start date in YYYYMMDD format. Defaults to 7 days ago.
        end_date (str, optional): End date in YYYYMMDD format. Defaults to today.
        output_format (str, optional): Output format ('text', 'json', 'html'). Defaults to 'text'.

    Returns:
        str or dict: The usage report in the specified format
    """
    try:
        # Set default date range if not specified
        if end_date is None:
            end_date = datetime.now().strftime("%Y%m%d")

        if start_date is None:
            # Default to 7 days ago
            start_date = (datetime.now() - timedelta(days=7)).strftime("%Y%m%d")

        # Convert dates to datetime objects for iteration
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        end_dt = datetime.strptime(end_date, "%Y%m%d")

        # Collect daily summaries
        daily_summaries = []
        current_dt = start_dt

        while current_dt <= end_dt:
            date_str = current_dt.strftime("%Y%m%d")
            summary = get_daily_usage_summary(date_str)
            daily_summaries.append(summary)
            current_dt += timedelta(days=1)

        # Calculate aggregate statistics
        total_requests = sum(s["total_requests"] for s in daily_summaries)
        total_input_tokens = sum(s["total_input_tokens"] for s in daily_summaries)
        total_output_tokens = sum(s["total_output_tokens"] for s in daily_summaries)
        total_tokens = total_input_tokens + total_output_tokens
        total_errors = sum(s["error_count"] for s in daily_summaries)
        total_cost = sum(s.get("total_estimated_cost", 0) for s in daily_summaries)

        # Combine categories across all days
        all_categories = {}
        for summary in daily_summaries:
            for category, count in summary.get("requests_by_category", {}).items():
                if category not in all_categories:
                    all_categories[category] = 0
                all_categories[category] += count

        # Combine models across all days
        all_models = {}
        for summary in daily_summaries:
            for model, count in summary.get("requests_by_model", {}).items():
                if model not in all_models:
                    all_models[model] = 0
                all_models[model] += count

        # Combine functions across all days
        all_functions = {}
        for summary in daily_summaries:
            for function, count in summary.get("requests_by_function", {}).items():
                if function not in all_functions:
                    all_functions[function] = 0
                all_functions[function] += count

        # Create the aggregate report
        report = {
            "start_date": start_date,
            "end_date": end_date,
            "days_covered": len(daily_summaries),
            "total_requests": total_requests,
            "total_input_tokens": total_input_tokens,
            "total_output_tokens": total_output_tokens,
            "total_tokens": total_tokens,
            "total_errors": total_errors,
            "error_rate": (total_errors / total_requests) if total_requests > 0 else 0,
            "total_estimated_cost": total_cost,
            "daily_summaries": daily_summaries,
            "requests_by_category": all_categories,
            "requests_by_model": all_models,
            "requests_by_function": all_functions
        }

        # Return in the requested format
        if output_format == "json":
            return report
        elif output_format == "html":
            return _generate_html_report(report)
        else:  # Default to text
            return _generate_text_report(report)

    except Exception as e:
        logger.error(f"Error generating usage report: {e}")
        return {"error": str(e)} if output_format == "json" else f"Error generating usage report: {e}"

def _generate_text_report(report):
    """
    Generate a text-based report from the report data.

    Args:
        report (dict): The report data

    Returns:
        str: The formatted text report
    """
    text = []

    # Header
    text.append("=" * 80)
    text.append(f"AI USAGE REPORT: {report['start_date']} to {report['end_date']} ({report['days_covered']} days)")
    text.append("=" * 80)

    # Summary statistics
    text.append("\nSUMMARY STATISTICS:")
    text.append("-" * 50)
    text.append(f"Total Requests:      {report['total_requests']:,}")
    text.append(f"Total Input Tokens:  {report['total_input_tokens']:,}")
    text.append(f"Total Output Tokens: {report['total_output_tokens']:,}")
    text.append(f"Total Tokens:        {report['total_tokens']:,}")
    text.append(f"Total Errors:        {report['total_errors']:,}")
    text.append(f"Error Rate:          {report['error_rate']:.2%}")
    text.append(f"Estimated Cost:      ${report['total_estimated_cost']:.4f}")

    # Requests by category
    text.append("\nREQUESTS BY CATEGORY:")
    text.append("-" * 50)
    categories = sorted(report['requests_by_category'].items(), key=lambda x: x[1], reverse=True)
    for category, count in categories:
        text.append(f"{category:<30} {count:,} ({count/report['total_requests']:.2%})")

    # Requests by model
    text.append("\nREQUESTS BY MODEL:")
    text.append("-" * 50)
    models = sorted(report['requests_by_model'].items(), key=lambda x: x[1], reverse=True)
    for model, count in models:
        text.append(f"{model:<30} {count:,} ({count/report['total_requests']:.2%})")

    # Requests by function
    text.append("\nREQUESTS BY FUNCTION:")
    text.append("-" * 50)
    functions = sorted(report['requests_by_function'].items(), key=lambda x: x[1], reverse=True)
    for function, count in functions:
        text.append(f"{function:<30} {count:,} ({count/report['total_requests']:.2%})")

    # Daily breakdown
    text.append("\nDAILY BREAKDOWN:")
    text.append("-" * 80)
    text.append(f"{'Date':<12} {'Requests':<10} {'Tokens':<12} {'Errors':<8} {'Cost':<8}")
    text.append("-" * 80)

    for day in report['daily_summaries']:
        date = day['date']
        requests = day['total_requests']
        tokens = day['total_tokens']
        errors = day['error_count']
        cost = day.get('total_estimated_cost', 0)

        text.append(f"{date:<12} {requests:<10,} {tokens:<12,} {errors:<8,} ${cost:<8.4f}")

    return "\n".join(text)

def _generate_html_report(report):
    """
    Generate an HTML report from the report data.

    Args:
        report (dict): The report data

    Returns:
        str: The HTML report
    """
    html = []

    # HTML header
    html.append("""<!DOCTYPE html>
<html>
<head>
    <title>AI Usage Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #4A6FE3; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .summary { background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .chart-container { height: 300px; margin-bottom: 30px; }
    </style>
</head>
<body>
    """)

    # Report header
    html.append(f"<h1>AI Usage Report: {report['start_date']} to {report['end_date']} ({report['days_covered']} days)</h1>")

    # Summary statistics
    html.append('<div class="summary">')
    html.append("<h2>Summary Statistics</h2>")
    html.append("<table>")
    html.append("<tr><th>Metric</th><th>Value</th></tr>")
    html.append(f"<tr><td>Total Requests</td><td>{report['total_requests']:,}</td></tr>")
    html.append(f"<tr><td>Total Input Tokens</td><td>{report['total_input_tokens']:,}</td></tr>")
    html.append(f"<tr><td>Total Output Tokens</td><td>{report['total_output_tokens']:,}</td></tr>")
    html.append(f"<tr><td>Total Tokens</td><td>{report['total_tokens']:,}</td></tr>")
    html.append(f"<tr><td>Total Errors</td><td>{report['total_errors']:,}</td></tr>")
    html.append(f"<tr><td>Error Rate</td><td>{report['error_rate']:.2%}</td></tr>")
    html.append(f"<tr><td>Estimated Cost</td><td>${report['total_estimated_cost']:.4f}</td></tr>")
    html.append("</table>")
    html.append('</div>')

    # Requests by category
    html.append("<h2>Requests by Category</h2>")
    html.append("<table>")
    html.append("<tr><th>Category</th><th>Count</th><th>Percentage</th></tr>")
    categories = sorted(report['requests_by_category'].items(), key=lambda x: x[1], reverse=True)
    for category, count in categories:
        html.append(f"<tr><td>{category}</td><td>{count:,}</td><td>{count/report['total_requests']:.2%}</td></tr>")
    html.append("</table>")

    # Requests by model
    html.append("<h2>Requests by Model</h2>")
    html.append("<table>")
    html.append("<tr><th>Model</th><th>Count</th><th>Percentage</th></tr>")
    models = sorted(report['requests_by_model'].items(), key=lambda x: x[1], reverse=True)
    for model, count in models:
        html.append(f"<tr><td>{model}</td><td>{count:,}</td><td>{count/report['total_requests']:.2%}</td></tr>")
    html.append("</table>")

    # Requests by function
    html.append("<h2>Requests by Function</h2>")
    html.append("<table>")
    html.append("<tr><th>Function</th><th>Count</th><th>Percentage</th></tr>")
    functions = sorted(report['requests_by_function'].items(), key=lambda x: x[1], reverse=True)
    for function, count in functions:
        html.append(f"<tr><td>{function}</td><td>{count:,}</td><td>{count/report['total_requests']:.2%}</td></tr>")
    html.append("</table>")

    # Daily breakdown
    html.append("<h2>Daily Breakdown</h2>")
    html.append("<table>")
    html.append("<tr><th>Date</th><th>Requests</th><th>Tokens</th><th>Errors</th><th>Cost</th></tr>")

    for day in report['daily_summaries']:
        date = day['date']
        requests = day['total_requests']
        tokens = day['total_tokens']
        errors = day['error_count']
        cost = day.get('total_estimated_cost', 0)

        html.append(f"<tr><td>{date}</td><td>{requests:,}</td><td>{tokens:,}</td><td>{errors:,}</td><td>${cost:.4f}</td></tr>")

    html.append("</table>")

    # HTML footer
    html.append("""
</body>
</html>
    """)

    return "\n".join(html)

def load_config(config_path='config.json'):
    """
    Load configuration from config.json file.

    Args:
        config_path (str): Path to config file

    Returns:
        dict: Configuration dictionary
    """
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
            return config
        else:
            logger.info(f"Config file not found at {config_path}")
            return {}
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def clean_llm_response(response_text, expected_format="json"):
    """
    Clean the LLM response by removing markdown code blocks and extracting the desired format.

    Args:
        response_text (str): The raw response from the LLM
        expected_format (str, optional): The expected format of the response. Defaults to "json"

    Returns:
        str: The cleaned response
    """
    if not response_text or not isinstance(response_text, str):
        return ""

    cleaned_response = response_text.strip()

    # Check for markdown code blocks with triple backticks
    if "```" in cleaned_response:
        # Extract content between code blocks
        code_block_pattern = f"```{expected_format}"
        if code_block_pattern in cleaned_response.lower():
            # If the expected format is found, extract content between that specific code block
            start_idx = cleaned_response.lower().find(code_block_pattern) + len(code_block_pattern)
            end_idx = cleaned_response.find("```", start_idx)
            if end_idx != -1:
                cleaned_response = cleaned_response[start_idx:end_idx].strip()
        else:
            # If no specific format is found, extract content from the first code block
            start_idx = cleaned_response.find("```") + 3
            # Skip language identifier if present
            if cleaned_response[start_idx:].strip() and cleaned_response[start_idx:].strip()[0].isalpha():
                # There's a language identifier, find the end of it
                newline_idx = cleaned_response.find("\n", start_idx)
                if newline_idx != -1:
                    start_idx = newline_idx + 1

            end_idx = cleaned_response.find("```", start_idx)
            if end_idx != -1:
                cleaned_response = cleaned_response[start_idx:end_idx].strip()

    return cleaned_response

def update_google_usage(tokens_used):
    """
    Update Google AI Studio usage stats in state manager after each API call.
    """
    # Get the state manager from the session state
    if "state" in st.session_state:
        state = st.session_state["state"]

        # Initialize the lists if they don't exist
        if not hasattr(state, 'google_request_timestamps'):
            state.google_request_timestamps = []
        if not hasattr(state, 'google_token_usage'):
            state.google_token_usage = []

        # Update the usage stats
        now = datetime.now()
        state.google_request_timestamps.append(now)
        state.google_token_usage.append((now, tokens_used))
    else:
        # Fallback to session state if state manager is not available
        if "google_request_timestamps" not in st.session_state:
            st.session_state.google_request_timestamps = []
        if "google_token_usage" not in st.session_state:
            st.session_state.google_token_usage = []
        now = datetime.now()
        st.session_state.google_request_timestamps.append(now)
        st.session_state.google_token_usage.append((now, tokens_used))

def markdown_table_to_json(markdown_table):
    """
    Convert a markdown table to a JSON array of objects.

    Args:
        markdown_table (str): The markdown table to convert

    Returns:
        list: A list of dictionaries representing the table rows
    """
    try:
        # Split the table into rows
        rows = markdown_table.strip().split('\n')

        # Need at least header and separator rows
        if len(rows) < 3:
            return []

        # Get the header row to extract column names
        header_row = rows[0]
        headers = [h.strip().lower().replace(' ', '_') for h in header_row.split('|')[1:-1]]

        # Skip the header and separator rows
        data_rows = rows[2:]

        # Parse each data row
        result = []
        for row in data_rows:
            cells = [cell.strip() for cell in row.split('|')[1:-1]]

            # Skip rows with incorrect number of cells
            if len(cells) != len(headers):
                continue

            # Create a dictionary for this row
            row_dict = {}
            for i, header in enumerate(headers):
                row_dict[header] = cells[i]

            result.append(row_dict)

        return result
    except Exception as e:
        logger.error(f"Error converting markdown table to JSON: {e}")
        return []

def json_to_markdown_table(json_data):
    """
    Convert a JSON array of objects to a markdown table.

    Args:
        json_data (list): A list of dictionaries representing table rows

    Returns:
        str: A markdown table representation of the JSON data
    """
    try:
        if not json_data or not isinstance(json_data, list) or not json_data[0]:
            return ""

        # Extract headers from the first object
        headers = []
        for key in json_data[0].keys():
            # Convert snake_case to Title Case
            header = key.replace('_', ' ').title()
            headers.append(header)

        # Create the header row
        header_row = "| " + " | ".join(headers) + " |"

        # Create the separator row
        separator_row = "| " + " | ".join(["---"] * len(headers)) + " |"

        # Create data rows
        data_rows = []
        for item in json_data:
            row_values = []
            for key in item.keys():
                row_values.append(str(item[key]))
            data_rows.append("| " + " | ".join(row_values) + " |")

        # Combine all rows
        markdown_table = header_row + "\n" + separator_row + "\n" + "\n".join(data_rows)

        return markdown_table
    except Exception as e:
        logger.error(f"Error converting JSON to markdown table: {e}")
        return ""

def analyze_step_table(step_table):
    """
    Analyze a step table to determine if UI element detection is needed.

    Args:
        step_table (tuple): A tuple containing (markdown_table, json_table)

    Returns:
        dict: Analysis results with the following keys:
            - requires_ui_elements (bool): Whether UI element detection is needed
            - reason (str): Reason for the recommendation
            - actions (list): List of actions detected in the step table
            - locator_strategies (list): List of locator strategies detected in the step table
    """
    try:
        # Unpack the step table
        markdown_table, json_table = step_table

        # Initialize analysis results
        analysis = {
            "requires_ui_elements": False,
            "reason": "No UI interactions detected in test steps",
            "actions": [],
            "locator_strategies": []
        }

        # If no JSON table, return default analysis
        if not json_table or not isinstance(json_table, list):
            return analysis

        # Extract actions and locator strategies from the JSON table
        actions = []
        locator_strategies = []
        ui_actions = ["click", "type", "select", "upload_file", "wait_for_element", "hover", "drag", "drop"]

        for step in json_table:
            action = step.get("action", "").lower()
            locator_strategy = step.get("locator_strategy", "").lower()

            if action:
                actions.append(action)

            if locator_strategy:
                locator_strategies.append(locator_strategy)

            # Check if this is a UI action
            if action in ui_actions or action.startswith("click_") or action.startswith("type_"):
                analysis["requires_ui_elements"] = True
                analysis["reason"] = f"UI interaction '{action}' detected in test steps"

        # If we have locator strategies but no UI actions, still recommend UI element detection
        if locator_strategies and not analysis["requires_ui_elements"]:
            analysis["requires_ui_elements"] = True
            analysis["reason"] = f"Locator strategies {', '.join(locator_strategies)} detected in test steps"

        # Store the extracted actions and locator strategies
        analysis["actions"] = actions
        analysis["locator_strategies"] = locator_strategies

        # Add debug information
        logger.debug(f"Actions detected: {actions}")
        logger.debug(f"Locator strategies detected: {locator_strategies}")
        logger.debug(f"Requires UI elements: {analysis['requires_ui_elements']}")
        logger.debug(f"Reason: {analysis['reason']}")

        return analysis
    except Exception as e:
        logger.error(f"Error analyzing step table: {e}")
        return {
            "requires_ui_elements": True,  # Default to True in case of error
            "reason": f"Error analyzing step table: {str(e)}",
            "actions": [],
            "locator_strategies": []
        }

def get_by_pos(lst, pos, default=None):
    """
    Safely get an item from a list by position.

    Args:
        lst (list): The list to get an item from
        pos (int): The position to get
        default: The default value to return if position is out of range

    Returns:
        The item at the position or the default value
    """
    try:
        if not lst or not isinstance(lst, list):
            return default
        if not isinstance(pos, int):
            try:
                pos = int(pos)
            except (TypeError, ValueError):
                return default
        return lst[pos] if 0 <= pos < len(lst) else default
    except Exception:
        return default

def safe_int(x, default=None):
    """
    Safely convert a value to an integer.

    Args:
        x: The value to convert
        default: The default value to return if conversion fails

    Returns:
        int or default: The converted integer or the default value
    """
    if x is None:
        return default

    try:
        # Handle string representations with whitespace
        if isinstance(x, str):
            x = x.strip()
            if not x:  # Empty string
                return default
        return int(x)
    except (TypeError, ValueError, OverflowError):
        return default
    except Exception:
        # Catch any other unexpected errors
        return default


def extract_json_from_response(response_text, request_id=None):
    """
    Extract JSON data from LLM response text.

    Args:
        response_text (str): The response text from the LLM
        request_id (str, optional): Request ID for logging

    Returns:
        list: Extracted JSON table or empty list if not found
    """
    json_table = []

    # Extract JSON from code block
    json_start = response_text.find("```json")
    if json_start != -1:
        # We found a ```json block
        json_end = response_text.find("```", json_start + 7)
        if json_end != -1:
            json_str = response_text[json_start + 7:json_end].strip()
            try:
                json_table = json.loads(json_str)
                if request_id:
                    logger.info(f"Found JSON code block with {len(json_table)} steps [Request ID: {request_id}]")
                return json_table
            except json.JSONDecodeError as e:
                if request_id:
                    logger.warning(f"JSON decode error for code block: {e} [Request ID: {request_id}]")

    # If no JSON block found, try to find JSON array pattern
    json_start = response_text.find("[{")
    if json_start != -1:
        # Find the matching closing bracket
        bracket_count = 1
        json_end = json_start + 2
        while json_end < len(response_text) and bracket_count > 0:
            if response_text[json_end] == "[":
                bracket_count += 1
            elif response_text[json_end] == "]":
                bracket_count -= 1
            json_end += 1

        if bracket_count == 0:
            json_str = response_text[json_start:json_end].strip()
            try:
                json_table = json.loads(json_str)
                if request_id:
                    logger.info(f"Found JSON array pattern with {len(json_table)} steps [Request ID: {request_id}]")
                return json_table
            except json.JSONDecodeError as e:
                if request_id:
                    logger.warning(f"JSON decode error for array pattern: {e} [Request ID: {request_id}]")

    return json_table


def extract_markdown_from_response(response_text, request_id=None):
    """
    Extract markdown table from LLM response text.

    Args:
        response_text (str): The response text from the LLM
        request_id (str, optional): Request ID for logging

    Returns:
        str: Extracted markdown table or empty string if not found
    """
    # Try to find markdown code block
    markdown_start = response_text.find("```markdown")
    if markdown_start == -1:
        # Try without the language specifier
        markdown_start = response_text.find("```")
        # Skip the first code block if it's JSON
        if markdown_start == response_text.find("```json"):
            markdown_start = response_text.find("```", markdown_start + 3)

    markdown_end = -1
    if markdown_start != -1:
        # Find the next ``` after the start
        markdown_end = response_text.find("```", markdown_start + 3)
        if markdown_start == response_text.find("```markdown"):
            # If it was ```markdown, adjust the start position
            markdown_start += 10
        else:
            # If it was just ```, adjust the start position
            markdown_start += 3

    if markdown_start != -1 and markdown_end != -1:
        markdown_table = response_text[markdown_start:markdown_end].strip()
        if request_id:
            logger.info(f"Found markdown table [Request ID: {request_id}]")
        return markdown_table

    # If no markdown block found, look for a table pattern
    table_start = response_text.find("| Step No |")
    if table_start != -1:
        # Find the end of the table (empty line or another code block)
        table_end = response_text.find("\n\n", table_start)
        if table_end == -1:
            table_end = response_text.find("```", table_start)
        if table_end == -1:
            table_end = len(response_text)
        markdown_table = response_text[table_start:table_end].strip()
        if request_id:
            logger.info(f"Found table pattern [Request ID: {request_id}]")
        return markdown_table

    return ""


def extract_token_counts(response, prompt):
    """
    Extract token counts from the response metadata.

    Args:
        response: The response object from the LLM
        prompt (str): The original prompt text

    Returns:
        tuple: (input_tokens, output_tokens)
    """
    input_tokens = None
    output_tokens = None

    # Extract token usage if available
    if hasattr(response, 'usage_metadata') and response.usage_metadata:
        if hasattr(response.usage_metadata, 'prompt_token_count'):
            input_tokens = response.usage_metadata.prompt_token_count
        if hasattr(response.usage_metadata, 'candidates_token_count'):
            output_tokens = response.usage_metadata.candidates_token_count
        if hasattr(response.usage_metadata, 'total_token_count'):
            total_tokens = response.usage_metadata.total_token_count
            # If we have total but not input/output, estimate input tokens
            if input_tokens is None and output_tokens is None:
                input_tokens = len(prompt.split())
                output_tokens = total_tokens - input_tokens

    # Fallback token counting if metadata not available
    if input_tokens is None:
        input_tokens = len(prompt.split())
    if output_tokens is None and response.text:
        output_tokens = len(response.text.split())

    return input_tokens, output_tokens


def format_call_stack_section(call_stack_info, function_name, is_prompt_generation):
    """
    Format the call stack section of the log file.

    Args:
        call_stack_info (dict): Call stack information from capture_call_stack
        function_name (str): Name of the function being logged
        is_prompt_generation (bool): Whether this is a prompt generation log

    Returns:
        str: Formatted call stack section
    """
    content = ""

    # Check if this is a prompt generation log
    is_prompt_gen = is_prompt_generation or any(name in function_name for name in [
        'prompt', 'generate_test_script_prompt', 'generate_test_data_prompt',
        'convert_test_case_to_step_table', 'merge_scripts_with_ai'
    ])

    if is_prompt_gen:
        # Add prompt generation trace section
        content += f"""
==============================================================================
PROMPT GENERATION TRACE:
==============================================================================
"""
        # Add prompt variables if available
        if 'prompt_variables' in call_stack_info and call_stack_info['prompt_variables']:
            content += f"""TEMPLATE VARIABLES:
{json.dumps(call_stack_info['prompt_variables'], indent=2, default=str)}

"""

        # Add template data if available
        if 'template_data' in call_stack_info and call_stack_info['template_data']:
            content += f"""TEMPLATE DATA:
{json.dumps(call_stack_info['template_data'], indent=2, default=str)}

"""

        # Add stack trace information with focus on prompt generation
        content += "PROMPT GENERATION CALL STACK:\n"
        for i, frame in enumerate(call_stack_info['stack']):
            # Check if this is a prompt-related function
            is_prompt_func = any(name in frame['function'] for name in [
                'prompt', 'generate_test_script_prompt', 'generate_test_data_prompt',
                'convert_test_case_to_step_table', 'merge_scripts_with_ai'
            ])

            # Add more detailed information for prompt-related functions
            if is_prompt_func:
                content += f"""
FRAME {i}: {frame['filename']}:{frame['lineno']} - {frame['function']} (PROMPT GENERATOR)
CODE: {frame['code']}
ARGUMENTS:
{json.dumps(frame['args'], indent=2, default=str)}
"""
            else:
                # Add basic information for other functions
                content += f"""FRAME {i}: {frame['filename']}:{frame['lineno']} - {frame['function']}
"""

    # Add standard function trace section
    content += f"""
==============================================================================
FUNCTION TRACE:
==============================================================================
"""
    # Add exception information if available
    if call_stack_info['exception']['type']:
        content += f"""EXCEPTION TYPE: {call_stack_info['exception']['type']}
EXCEPTION MESSAGE: {call_stack_info['exception']['message']}

"""

    # Add stack trace information
    for i, frame in enumerate(call_stack_info['stack']):
        content += f"""FRAME {i}: {frame['filename']}:{frame['lineno']} - {frame['function']}
CODE: {frame['code']}
ARGUMENTS:
{json.dumps(frame['args'], indent=2, default=str)}

"""

    # Add full traceback if available
    if call_stack_info['exception']['traceback']:
        content += f"""
FULL TRACEBACK:
{call_stack_info['exception']['traceback']}
"""

    return content


def format_metadata_section(request_id, function_name, category, model_name,
                           parent_request_id, related_request_ids, input_tokens,
                           output_tokens, latency_ms, error, context):
    """
    Format the metadata section of the log file.

    Args:
        request_id (str): Unique ID for this request
        function_name (str): Name of the function making the API call
        category (str): Category of the interaction
        model_name (str): The AI model used
        parent_request_id (str): ID of a parent request if this is a follow-up
        related_request_ids (list): List of related request IDs
        input_tokens (int): Number of input tokens used
        output_tokens (int): Number of output tokens generated
        latency_ms (float): Request latency in milliseconds
        error (Exception): Exception if the request failed
        context (dict): Additional context information

    Returns:
        str: Formatted metadata section
    """
    return f"""==============================================================================
REQUEST METADATA:
==============================================================================
REQUEST ID: {request_id}
FUNCTION: {function_name}
CATEGORY: {category}
TIMESTAMP: {datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]}
MODEL: {model_name}
PARENT REQUEST ID: {parent_request_id or "N/A"}
RELATED REQUEST IDs: {', '.join(related_request_ids) if related_request_ids else "N/A"}
INPUT TOKENS: {input_tokens or "N/A"}
OUTPUT TOKENS: {output_tokens or "N/A"}
TOTAL TOKENS: {(input_tokens or 0) + (output_tokens or 0) if input_tokens is not None and output_tokens is not None else "N/A"}
LATENCY: {f"{latency_ms:.2f}ms" if latency_ms is not None else "N/A"}
ERROR: {str(error) if error else "None"}

==============================================================================
CONTEXT:
==============================================================================
{json.dumps(context or {}, indent=2)}
"""
