#!/usr/bin/env python3

import pandas as pd
import json
import os
import sys
import sqlite3
from jira import JIRA
from ollama import chat, ChatResponse
import re
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.views import Pane
import requests
from datetime import datetime
from google import genai
import streamlit as st
# Import Zephyr integration if available
ZEPHYR_AVAILABLE = False
upload_test_cases_to_jira = None

# Function to dynamically load the zephyr_integration module
def load_zephyr_integration():
    global ZEPHYR_AVAILABLE, upload_test_cases_to_jira
    try:
        import importlib.util

        # Get the full path to the zephyr_integration.py file
        module_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'zephyr_integration.py')

        if os.path.exists(module_path):
            # Load the module from the file path
            spec = importlib.util.spec_from_file_location('zephyr_integration', module_path)
            zephyr_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(zephyr_module)

            # Get the upload_test_cases_to_jira function from the module
            upload_test_cases_to_jira = zephyr_module.upload_test_cases_to_jira
            ZEPHYR_AVAILABLE = True
            return True
        else:
            return False
    except Exception as e:
        return False

# Try to load the Zephyr integration
load_zephyr_integration()

def get_latest_test_case_file(jira_id=None, test_type=None):
    """
    Find the latest generated test case Excel file based on timestamp in the filename.
    Only returns timestamped Excel files (with format YYYYMMDD_HHMMSS), not the main files.
    If database is available, it will try to get the latest test run from the database first.

    Args:
        jira_id: Optional JIRA ID to filter by (e.g., "TP-1")
        test_type: Optional test type to filter by (e.g., "positive", "negative", "all")

    Returns:
        Tuple of (file_path, file_name) or (None, None) if no files found
    """
    try:
        # First, try to get the latest test run from the database
        try:
            import Test_case_db_helper as db
            if jira_id:
                # Get the latest test run from the database
                test_run = db.get_latest_test_run(db.DATABASE_PATH, jira_id, test_type)
                if test_run:
                    # Get test cases from the database
                    df = db.get_test_cases_from_database(db.DATABASE_PATH, jira_id, test_type)
                    if not df.empty:
                        # Create a temporary Excel file with the test cases
                        test_cases_folder = "Test_cases"
                        os.makedirs(test_cases_folder, exist_ok=True)
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        if test_type and test_type.lower() != "all":
                            temp_file = f"Latest_testcases_{jira_id}_{test_type}_{timestamp}.xlsx"
                        else:
                            temp_file = f"Latest_testcases_{jira_id}_ALL_{timestamp}.xlsx"
                        temp_file_path = os.path.join(test_cases_folder, temp_file)

                        # Save to Excel only when explicitly requested
                        create_formatted_excel_from_scenarios(
                            df,
                            temp_file_path,
                            is_dataframe=True,
                            save_to_db=False,  # Don't save back to database
                            create_excel=True  # Explicitly create Excel file for download
                        )

                        print(f"Created temporary Excel file from database: {temp_file_path}")
                        return temp_file_path, temp_file
        except Exception as db_error:
            print(f"Error getting latest test run from database: {str(db_error)}")
            print("Falling back to Excel files...")

        # If database lookup fails, fall back to Excel files
        # Check the Test_cases folder for Excel files
        test_cases_folder = "Test_cases"
        if not os.path.exists(test_cases_folder):
            return None, None

        # Get all Excel files in the folder
        excel_files = []
        for f in os.listdir(test_cases_folder):
            # Skip temporary Excel files and main files (without timestamps)
            if f.endswith(".xlsx") and not f.startswith("~$") and f.startswith("Latest_testcases_"):
                # Filter by JIRA ID if provided
                if jira_id and f"_{jira_id}_" not in f:
                    continue

                # Filter by test type if provided
                if test_type:
                    if test_type == "all":
                        # For "all" test type, look for files with "_ALL_" in the name
                        if "_ALL_" not in f:
                            continue
                    else:
                        # For specific test types, either look for "_ALL_test_type_" or "_test_type_"
                        if f"_ALL_{test_type}_" not in f and f"_{test_type}_" not in f:
                            continue

                # Check if the file has a timestamp pattern (8 digits followed by 6 digits)
                parts = f.split('_')
                for i in range(len(parts) - 1):
                    if (i < len(parts) - 1 and
                        parts[i].isdigit() and len(parts[i]) == 8 and
                        parts[i+1].split('.')[0].isdigit() and len(parts[i+1].split('.')[0]) == 6):
                        excel_files.append(f)
                        break

        if not excel_files:
            # If no timestamped files found, return None
            # We don't want to show main files in the Latest Test Cases tab
            return None, None

        # Sort by modification time (newest first)
        excel_files.sort(key=lambda x: os.path.getmtime(os.path.join(test_cases_folder, x)), reverse=True)

        # Get the newest file
        latest_file = excel_files[0]
        latest_file_path = os.path.join(test_cases_folder, latest_file)

        return latest_file_path, latest_file
    except Exception as e:
        print(f"Error finding latest test case file: {str(e)}")
        return None, None


def get_existing_test_cases(issue_key):
    """
    Read existing test cases from the database or main file for a given issue key.

    Args:
        issue_key: The JIRA issue key (e.g., "TP-1")

    Returns:
        A list of dictionaries containing test case objectives and steps
    """
    try:
        # First, try to get test cases from the database
        try:
            import Test_case_db_helper as db
            # Get test cases from the database
            df = db.get_test_cases_from_database(db.DATABASE_PATH, issue_key)
            if not df.empty:
                # Extract test case objectives and steps
                existing_test_cases = []

                # Get unique test case IDs
                test_case_ids = df["Test Case ID"].dropna().unique()

                for tc_id in test_case_ids:
                    # Get rows for this test case ID
                    tc_rows = df[df["Test Case ID"] == tc_id]

                    # Get the objective (should be in the first row)
                    objective = tc_rows["Test Case Objective"].iloc[0] if not pd.isna(tc_rows["Test Case Objective"].iloc[0]) else ""

                    # Get all steps for this test case
                    steps = []
                    for _, row in tc_rows.iterrows():
                        if not pd.isna(row["Test Steps"]) and not pd.isna(row["Expected Result"]):
                            steps.append({
                                "action": row["Test Steps"],
                                "expected_result": row["Expected Result"]
                            })

                    existing_test_cases.append({
                        "test_case_id": tc_id,
                        "objective": objective,
                        "steps": steps
                    })

                print(f"Found {len(existing_test_cases)} existing test cases in database for {issue_key}")
                return existing_test_cases
        except Exception as db_error:
            print(f"Error getting test cases from database: {str(db_error)}")
            print("Falling back to Excel files...")

        # If database lookup fails, fall back to Excel files
        # Check if main file exists
        test_cases_dir = "Test_cases"
        main_file = os.path.join(test_cases_dir, f"Complete_testcases_{issue_key}_ALL.xlsx")

        if not os.path.exists(main_file):
            # Try the old naming convention
            old_main_file = os.path.join(test_cases_dir, f"test_scenarios_{issue_key}_ALL.xlsx")
            if os.path.exists(old_main_file):
                main_file = old_main_file
            else:
                return []

        # Read the Excel file
        df = pd.read_excel(main_file)

        # Extract test case objectives and steps
        existing_test_cases = []

        # Get unique test case IDs
        test_case_ids = df["Test Case ID"].dropna().unique()

        for tc_id in test_case_ids:
            # Get rows for this test case ID
            tc_rows = df[df["Test Case ID"] == tc_id]

            # Get the objective (should be in the first row)
            objective = tc_rows["Test Case Objective"].iloc[0] if not pd.isna(tc_rows["Test Case Objective"].iloc[0]) else ""

            # Get all steps for this test case
            steps = []
            for _, row in tc_rows.iterrows():
                if not pd.isna(row["Test Steps"]) and not pd.isna(row["Expected Result"]):
                    steps.append({
                        "action": row["Test Steps"],
                        "expected_result": row["Expected Result"]
                    })

            existing_test_cases.append({
                "test_case_id": tc_id,
                "objective": objective,
                "steps": steps
            })

        return existing_test_cases
    except Exception as e:
        print(f"Error reading existing test cases: {str(e)}")
        return []

def extract_test_info_from_issue(issue, test_type, num_scenarios=5):
    # Define valid test types
    valid_test_types = ["positive", "negative", "security", "performance", "mixed"]

    if test_type not in valid_test_types:
        raise ValueError(
            f"Invalid test_type: '{test_type}'. Must be one of: {', '.join(valid_test_types)}"
        )

    test_case = issue.fields.summary.strip()
    acceptance_criteria = issue.fields.description.strip()

    # Get existing test cases to avoid duplication
    existing_test_cases = get_existing_test_cases(issue.key)

    # Create a summary of existing test cases to include in the prompt
    existing_test_cases_summary = ""
    if existing_test_cases:
        existing_test_cases_summary = "\nEXISTING TEST CASES (DO NOT DUPLICATE THESE):\n"
        for i, tc in enumerate(existing_test_cases, 1):
            existing_test_cases_summary += f"{i}. {tc['test_case_id']}: {tc['objective']}\n"
            for j, step in enumerate(tc['steps'], 1):
                existing_test_cases_summary += f"   {j}. {step['action']} -> {step['expected_result']}\n"
            existing_test_cases_summary += "\n"

    # Clearly defined instructions for each test type
    test_instructions = {
         "negative": (
            "- Emphasize invalid inputs, boundary conditions, or error handling.\n"
            "- Do not include any successful or valid user paths.\n"
        ),
        "positive": (
        "Generate only POSITIVE test scenarios demonstrating successful and expected system behavior under ideal conditions.\n"
        "- Do not include any errors, exceptions, or invalid inputs.\n"
        "- All test steps must simulate valid actions that a typical user would perform.\n"
        "- Each scenario should include a clear objective, user-friendly prerequisite, 3–5 test steps, and corresponding expected results using future tense ('should')."
        ),
        "security": (
            "Generate only SECURITY test scenarios focusing explicitly on input validation, authentication, authorization, and data protection. "
            "Scenarios should never include generic functional tests."
        ),
        "performance": (
            "Generate only PERFORMANCE test scenarios clearly specifying load parameters, response times, resource usage, and throughput expectations. "
            "Scenarios should never include generic functionality tests."
        ),
        "mixed": (
            "Generate a MIXED set of POSITIVE and NEGATIVE test scenarios. "
            "Each scenario must explicitly state its type ('positive' or 'negative') in the 'type' field."
        ),
    }

    # JSON scenario structure template (for instructional clarity)
    scenario_template = (
        '{\n'
        '  "scenario_name": "<Short, unique, descriptive scenario name>",\n'
        '  "type": "<positive|negative|security|performance>",\n'
        '  "prerequisites": "<User should have necessary access and permissions for the feature being tested. Write from user perspective.>",\n'
        '  "Test Case Objective": "<The objective should concisely summarize the test steps and expected results. Must start with \'Verify\'>",\n'
        '  "steps": [\n'
        '    {"action": "<Begin with Verify followed by the specific action to test. Be clear and precise>", "expected_result": "<Specific singular expected result in future tense >"},\n'
        '    {"action": "<Begin with Verify followed by the specific action to test. Be clear and precise>", "expected_result": "<Specific singular expected result in future tense >"}\n'
        '  ]\n'
        '}'
    )

    # Compose final instruction text
    instruction = (
        "You are an experienced manual QA test engineer tasked with creating detailed test scenarios deligating for Junior QA engineering.\n\n"
        f"Given the following Test Case and Acceptance Criteria, generate exactly {num_scenarios} test scenarios as a JSON array.\n\n"
        f"TEST TYPE INSTRUCTIONS:\n{test_instructions[test_type]}\n\n"
        "EACH SCENARIO MUST FOLLOW THIS EXACT JSON STRUCTURE:\n"
        f"{scenario_template}\n\n"
        "STRICT OUTPUT REQUIREMENTS:\n"
        "- Output must ONLY be a JSON array containing exactly the requested scenarios.\n"
        "- Do not use markdown, code fences, or any additional explanatory text.\n"
        "- Every scenario object must contain all specified keys exactly as provided, no additional keys or comments are allowed.\n"
         "- Include exactly 3 to 5 detailed steps.\n"
         "- test_case_objective must begin with 'Verify', 'Validate', or 'Check'.\n"
         "- Each step must begin with 'Verify if user is able to' and focus on an invalid or error-inducing scenario.\n"
         "- expected_results must be written in future tense using 'should' and clearly indicate the error or system behavior (e.g., 'Error message should be displayed').\n"
         "- Prerequisite must be from the user's perspective (e.g., 'User should have access to the login page').\n"
         "- DO NOT duplicate any of the existing test cases listed below. Create entirely new test scenarios with different objectives and steps.\n"
    )

    combined_text = (
        f"{instruction}\n"
        f"TEST CASE:\n{test_case}\n\n"
        f"ACCEPTANCE CRITERIA:\n{acceptance_criteria}\n"
        f"{existing_test_cases_summary}"
    )

    return combined_text


def generate_gemini_test_gen_prompt(issue, test_type, num_scenarios=5, attachment_path=None):
    """
    Generates a prompt optimized for Gemini to create detailed manual test scenarios
    for execution by junior QA engineers.

    Args:
        issue: An object with 'fields.summary' and 'fields.description' attributes (strings).
        test_type (str): The type of test scenarios to generate.
                         Must be one of: "positive", "negative", "security", "performance", "mixed".
        num_scenarios (int): The exact number of scenarios to generate.
        attachment_path (str, optional): Path to an attachment (screenshot) to include in the prompt.

    Returns:
        str: The generated prompt string, or a tuple of (image, prompt) if attachment_path is provided.

    Raises:
        ValueError: If test_type is invalid or issue structure is incorrect.
    """
    # --- Validation ---
    valid_test_types = ["positive", "negative", "security", "performance", "mixed"]
    if test_type not in valid_test_types:
        raise ValueError(f"Invalid test_type: '{test_type}'. Must be one of: {', '.join(valid_test_types)}")

    # --- Input Data Extraction ---
    try:
        test_case_summary = issue.fields.summary.strip()
        acceptance_criteria = issue.fields.description.strip()
        if not isinstance(test_case_summary, str) or not isinstance(acceptance_criteria, str):
            raise ValueError("issue.fields.summary and issue.fields.description must be strings.")
    except AttributeError:
        raise ValueError("Input 'issue' object must have 'fields.summary' and 'fields.description' attributes.")
    except Exception as e:  # Catch other potential errors during access
        raise ValueError(f"Error accessing issue fields: {e}")

    # Get existing test cases to avoid duplication
    existing_test_cases = get_existing_test_cases(issue.key)

    # Create a summary of existing test cases to include in the prompt
    existing_test_cases_summary = ""
    if existing_test_cases:
        existing_test_cases_summary = "\nEXISTING TEST CASES (DO NOT DUPLICATE THESE):\n"
        for i, tc in enumerate(existing_test_cases, 1):
            existing_test_cases_summary += f"{i}. {tc['test_case_id']}: {tc['objective']}\n"
            for j, step in enumerate(tc['steps'], 1):
                existing_test_cases_summary += f"   {j}. {step['action']} -> {step['expected_result']}\n"
            existing_test_cases_summary += "\n"

    # Check if there are attachments in the issue
    has_attachments = hasattr(issue.fields, 'attachment') and issue.fields.attachment
    attachment_info = ""
    if has_attachments:
        attachment_info = "\nATTACHMENTS INFORMATION:\n"
        for i, att in enumerate(issue.fields.attachment, 1):
            # Use only the filename attribute which is guaranteed to exist
            attachment_info += f"{i}. {att.filename}\n"

    # --- Test Type Specific Instructions ---
    test_instructions = {
       "positive": (
        "Create only POSITIVE test cases that demonstrate expected system behavior under normal, ideal conditions. "
        "Assume all inputs are valid and all systems are functioning as intended. "
        "Focus on happy paths and typical user actions. "
        "Do not include error handling, edge cases, or invalid data. "
        "Each test case should include 3 to 5 detailed, logically ordered steps to fully validate the functional flow."
    ),
    "negative": (
        "Create only NEGATIVE test cases that explore how the system handles invalid inputs, unexpected user behavior, and edge cases. "
        "Include meaningful validations, error messages, and system safeguards. "
        "Do not include any successful or expected behavior flows. "
        "Each test case should include 3 to 5 detailed, clearly defined steps to verify the robustness of error handling."
    ),
    "security": (
        "Create only SECURITY-focused test cases targeting aspects such as authentication, authorization, input sanitization, and data confidentiality. "
        "Test for vulnerabilities, access control issues, and improper handling of sensitive information. "
        "Avoid including general functional or UI test scenarios. "
        "Each test case should include 3 to 5 steps that focus specifically on verifying security mechanisms and protective controls."
    ),
    "performance": (
        "Create only PERFORMANCE-oriented test cases designed to measure the system’s behavior under load, stress, or high concurrency. "
        "Specify clear metrics such as load size, expected response time, and system resource usage. "
        "Avoid generic functionality or UI validation steps. "
        "Each test case should include 3 to 5 technical steps that assess system performance under defined parameters."
    ),
    "mixed": (
        "Create a MIXED set of test cases that includes both POSITIVE and NEGATIVE scenarios. "
        "Clearly label each test case with a 'type' field indicating whether it is 'positive' or 'negative'. "
        "Ensure a good balance between successful workflows and error-handling tests. "
        "Each test case must include 3 to 5 realistic, clearly sequenced steps to thoroughly test the functionality and its robustness."
    ),
}

    # --- JSON Output Structure Definition ---
    # For 'mixed', allow either 'positive' or 'negative' types in the output.
    scenario_structure = (
        '{\n'
        '  "scenario_name": "<Short, unique, descriptive scenario name>",\n'
        f'  "type": "{test_type if test_type != "mixed" else "<positive|negative>"}",\n'
        '  "prerequisites": "<User should have necessary access and permissions for the feature being tested. Write from user perspective.>",\n'
        '  "Test Case Objective": "<The objective should concisely summarize the test steps and expected results. Must start with \'Verify\'>",\n'
        '  "steps": [\n'
        '    {"action": "<Begin with \'Verify if user is able to\' followed by the specific action to test. Be clear and precise>", "expected_result": "<Specific singular expected result in future tense >"},\n'
        '    {"action": "<Begin with \'Verify if user is able to\' followed by the specific action to test. Be clear and precise>", "expected_result": "<Specific singular expected result in future tense >"}\n'
        '    /* Additional detailed steps encouraged */\n'
        '  ]\n'
        '}'


    )


    # Convert dict to a formatted JSON string for the prompt instructions
    scenario_structure_string = json.dumps(scenario_structure, indent=2)
    # Remove quotes around the placeholder markers for clarity in the prompt
    scenario_structure_string = scenario_structure_string.replace('"<', '<').replace('>"', '>')
    if test_type != "mixed":
        # Ensure the fixed type value is correctly displayed without extra quotes
        scenario_structure_string = scenario_structure_string.replace(f"\"<Must be exactly '{test_type}'>\"", f"\"'{test_type}'\"")

    # --- Core Prompt Construction ---    # Get additional context from session state if it exists
    additional_context = st.session_state.get("context_prompt", "")
    context_section = f"\nADDITIONAL CONTEXT FROM QA ENGINEER:\n{additional_context}\n\n" if additional_context else ""

    prompt = (
        "You are an experienced manual QA engineer tasked with creating detailed test cases that will be executed by another QA engineer.\n\n"
        f"Given the following Test Scenario Summary and Acceptance Criteria, generate exactly {num_scenarios} detailed test cases as a JSON array.\n\n"
        f"TEST TYPE INSTRUCTIONS:\n{test_instructions[test_type]}\n\n"
        "EACH TEST CASE MUST FOLLOW THIS EXACT JSON STRUCTURE:\n"
        f"{scenario_structure_string}\n\n"
        f"{context_section}"
        "STRICT OUTPUT REQUIREMENTS:\n"
        "- Output must ONLY be a JSON array containing exactly the requested test cases.\n"
        "- Do not use markdown, code fences, or any additional explanatory text.\n"
        "- Every test case object must contain all specified keys exactly as provided, with no additional keys or comments.\n"
        "- The Test Case Objective MUST start with 'Verify', 'Validate', or 'Check' and concisely summarize the test steps and expected results.\n"
        "- Each test step MUST begin with 'Verify if user is able to' followed by the specific action to test for consistency and clarity.\n"
        "- Expected Results should be written in future tense using 'should' (e.g., 'Login page should be displayed').\n"
        "- IMPORTANT: Each test case MUST include 3-5 detailed steps to thoroughly test the functionality.\n"
        "- Prerequisites should be written from the user's perspective (e.g., 'User should have valid credentials for the test environment').\n"
        "- Each step should be atomic, precise, and focus on a single testable action.\n"
        "- DO NOT duplicate any of the existing test cases listed below. Create entirely new test scenarios with different objectives and steps.\n\n"
        f"TEST SCENARIO SUMMARY:\n{test_case_summary}\n\n"
        f"ACCEPTANCE CRITERIA:\n{acceptance_criteria}\n"
        f"{attachment_info}\n"
        f"{existing_test_cases_summary}"
    )

    # If an attachment path is provided, return both the image and the prompt
    if attachment_path and os.path.exists(attachment_path):
        try:
            from PIL import Image
            image = Image.open(attachment_path)

            # Add information about the attachment to the prompt
            prompt += f"\n\nNOTE: A screenshot is attached to this prompt. Please analyze the screenshot and incorporate relevant UI elements, workflows, and functionality shown in the image into your test cases. The test cases should reflect the actual UI and functionality shown in the screenshot."

            return image, prompt
        except Exception as e:
            print(f"Error loading attachment image: {e}")
            # If there's an error loading the image, just return the text prompt
            return prompt
    else:
        return prompt



def run_ollama_with_chat(prompt: str, model: str = "mistral", temperature: float = None) -> str:
    """
    Run Ollama to generate content.

    Args:
        prompt: The prompt to send to Ollama
        model: The model to use (default: "mistral")
        temperature: Temperature setting (0.1 to 1.0) to control creativity (optional)
                    Note: If the Ollama version doesn't support temperature, it will be included in the prompt

    Returns:
        The response text from Ollama
    """
    try:
        # If temperature is provided, try to use it with the API
        if temperature is not None:
            try:
                response: ChatResponse = chat(
                    model=model,
                    messages=[
                        {
                            'role': 'user',
                            'content': prompt,
                        },
                    ],
                    temperature=temperature
                )
                return response.message.content
            except TypeError:
                # If temperature parameter is not supported, include it in the prompt instead
                temp_prompt = f"Use a temperature setting of {temperature} (on a scale of 0.1 to 1.0) for this response.\n\n{prompt}"
                response: ChatResponse = chat(
                    model=model,
                    messages=[
                        {
                            'role': 'user',
                            'content': temp_prompt,
                        },
                    ]
                )
                return response.message.content
        else:
            # If no temperature is provided, just use the standard API call
            response: ChatResponse = chat(
                model=model,
                messages=[
                    {
                        'role': 'user',
                        'content': prompt,
                    },
                ]
            )
            return response.message.content
    except Exception as e:
        error_msg = f"Error from Ollama: {str(e)}"
        print(error_msg)
        return error_msg

def run_google_ai_studio(prompt, api_key: str, model: str = "gemini-2.0-flash", temperature: float = None):
    """
    Run Google AI Studio to generate content.

    Args:
        prompt: Either a string prompt or a tuple of (image, prompt) for multimodal input
        api_key: The Google AI API key
        model: The model to use (default: "gemini-2.0-flash")
        temperature: Temperature setting (0.1 to 1.0) to control creativity (optional)

    Returns:
        A tuple of (response_text, token_count)
    """
    try:
        client = genai.Client(api_key=api_key)

        # Check if prompt is a tuple containing an image and text
        if isinstance(prompt, tuple) and len(prompt) == 2:
            image, text_prompt = prompt
            # Use multimodal input (image + text)
            response = client.models.generate_content(
                model=model,
                contents=[image, text_prompt]
            )
        else:
            # Use text-only input
            response = client.models.generate_content(
                model=model,
                contents=prompt
            )

        # Get token count if available
        token_count = None
        if hasattr(response, 'usage_metadata') and response.usage_metadata:
            token_count = response.usage_metadata.total_token_count

        return response.text, token_count

    except Exception as e:
        error_msg = f"Error from Google AI Studio: {str(e)}"
        print(error_msg)
        return error_msg, 0

# Global counter for test case IDs to ensure continuous numbering
_test_case_counter = 0
_last_jira_id = None

def reset_test_case_counter():
    """Reset the global test case counter to 0."""
    global _test_case_counter
    _test_case_counter = 0
    print(f"Test case counter reset to 0.")

def set_test_case_counter(value):
    """Set the test case counter to a specific value.

    Args:
        value: The value to set the counter to
    """
    global _test_case_counter
    _test_case_counter = value

def get_test_case_counter():
    """Get the current test case counter value.

    Returns:
        The current value of the test case counter
    """
    global _test_case_counter
    return _test_case_counter

def get_highest_test_case_id(jira_id, test_type=None):
    """
    Find the highest test case ID in the database for a given JIRA ID and test type.
    Also checks for gaps in the sequence and returns the first gap if found.

    Args:
        jira_id: The JIRA ID (e.g., "TP-1")
        test_type: The test type (e.g., "positive", "negative", "all")
                  If provided, only look for test cases with this test type
                  If None, look across all test types

    Returns:
        The highest test case ID number, or 0 if no test cases found
    """
    try:
        # First, try to get the highest test case ID from the database
        try:
            import Test_case_db_helper as db

            # Always get the highest ID across all test types
            print(f"Getting highest test case ID for {jira_id} across all test types")
            highest_id = db.get_highest_test_case_id_number(db.DATABASE_PATH, jira_id)
            if highest_id > 0:
                print(f"Found highest test case ID {highest_id} in database for {jira_id} across all test types")
                return highest_id
            else:
                # If no test cases found, return 0 to start from TC_001
                print(f"No test cases found in database for {jira_id}. Starting from TC_001.")
                return 0
        except Exception as db_error:
            print(f"Error getting highest test case ID from database: {str(db_error)}")
            # Don't fall back to Excel files anymore, just return 0
            return 0
    except Exception as e:
        print(f"Error finding highest test case ID: {str(e)}")
        return 0

def parse_test_scenarios_json(response_text: str, issue=None, start_id_from=None, continue_numbering=False, dashboard_test_type=None) -> pd.DataFrame:
    """Parse JSON response from AI into a DataFrame of test scenarios.

    Args:
        response_text: The JSON response from the AI model
        issue: The JIRA issue object
        start_id_from: Optional starting ID number for test cases (to continue numbering)
        continue_numbering: Whether to continue numbering from the previous test type
        dashboard_test_type: The test type selected in the dashboard (e.g., "positive", "negative", "all")
    """
    global _test_case_counter, _last_jira_id

    # Always check for the highest test case ID in the main file
    current_jira_id = issue.key if issue else None

    # If the JIRA ID has changed, reset the counter
    if current_jira_id and _last_jira_id and current_jira_id != _last_jira_id:
        print(f"JIRA ID changed from {_last_jira_id} to {current_jira_id}. Resetting counter.")
        reset_test_case_counter()
        _last_jira_id = current_jira_id

    # Extract test type from the first scenario if available
    current_test_type = None
    try:
        # Try to parse the JSON to extract the test type
        scenarios = json.loads(response_text)
        if isinstance(scenarios, list) and len(scenarios) > 0:
            # Get the test type from the first scenario
            current_test_type = scenarios[0].get("type", "").lower()
    except:
        # If parsing fails, we'll proceed without a test type
        pass

    # If continue_numbering is True, we don't need to check the main file or reset the counter
    # We'll just use the current counter value or the provided start_id_from
    if not continue_numbering and current_jira_id is not None:
        # For specific test types, check if a main file exists for this specific test type
        if current_test_type and current_test_type != 'all':
            # Check if a main file exists for this specific test type
            main_file_path = os.path.join("Test_cases", f"Complete_testcases_{current_jira_id}_{current_test_type}.xlsx")
            if not os.path.exists(main_file_path):
                # If no main file exists for this specific test type, reset the counter
                reset_test_case_counter()
                print(f"No main file found for {current_jira_id} ({current_test_type}). Starting from TC_001")
                _last_jira_id = current_jira_id
                # Skip the rest of the checks since we've already reset the counter
                # We'll continue with the function and let it create a new DataFrame

        # Find the highest test case ID in the main file for this JIRA ID across all test types
        highest_id = get_highest_test_case_id(current_jira_id)
        if highest_id > 0:
            # If we found existing test cases, continue from the highest ID
            _test_case_counter = highest_id
            print(f"Found existing test cases for {current_jira_id}. Continuing from TC_{highest_id+1:03d}")
        else:
            # If no existing test cases, reset the counter
            reset_test_case_counter()
            print(f"No existing test cases found for {current_jira_id}. Starting from TC_001")

        _last_jira_id = current_jira_id

    # If a starting ID is provided, update the counter
    if start_id_from is not None:
        _test_case_counter = start_id_from
        print(f"Setting test case counter to {start_id_from} based on provided start_id_from")

    # Generate timestamp for the test cases
    current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Remove potential markdown code block markers if present
    response_text = response_text.strip()
    if not response_text:
        raise ValueError("Empty response received from AI model")

    # If response is wrapped in markdown code blocks, clean it
    if response_text.startswith("```") and response_text.endswith("```"):
        response_text = "\n".join(response_text.splitlines()[1:-1])
    elif response_text.startswith("```json") or response_text.startswith("```JSON"):
        # Handle case where it's a code block with language specifier
        lines = response_text.splitlines()
        response_text = "\n".join(lines[1:-1] if lines[-1] == "```" else lines[1:])

    # Try to parse the JSON, with extensive error handling
    try:
        # Try parsing with json.loads
        scenarios = json.loads(response_text)
    except json.JSONDecodeError as e:
        # If the response starts with "Error from Google AI Studio" or other error message
        if response_text.startswith("Error from"):
            raise ValueError(f"AI model returned an error: {response_text}")

        # Print debug information
        print(f"JSON parse error at position {e.pos}, line: {e.lineno}, column: {e.colno}")
        print(f"Error message: {e.msg}")
        print(f"First 100 chars of response: {response_text[:100]}")

        # Save the raw response to a file for debugging
        try:
            raw_folder = "raw_responses"
            os.makedirs(raw_folder, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            raw_file_path = os.path.join(raw_folder, f"raw_response_{timestamp}.txt")
            with open(raw_file_path, "w", encoding="utf-8") as f:
                f.write(response_text)
            print(f"Saved raw response to {raw_file_path}")
        except Exception as save_error:
            print(f"Error saving raw response: {save_error}")

        # Try to fix invalid escape characters
        try:
            # First, try to fix broken lines
            fixed_text = re.sub(r'([a-zA-Z0-9.,;:\'"])\n([a-zA-Z0-9])', r'\1\2', response_text)

            # Then, try to fix invalid escape sequences
            # This pattern looks for backslashes that aren't followed by valid escape characters
            fixed_text = re.sub(r'\\(?!["\\\\bfnrt/]|u[0-9a-fA-F]{4})', r'\\\\', fixed_text)

            # Also specifically handle the case of \{ and \} which are common in the responses
            fixed_text = fixed_text.replace('\\{', '\\\\{').replace('\\}', '\\\\}')

            # Handle other common problematic escape sequences
            for char in [',', '/', ':', ';', '\'', '?', '>', '<']:
                fixed_text = fixed_text.replace('\\' + char, '\\\\' + char)

            # Try to parse the fixed text
            scenarios = json.loads(fixed_text)
            print("Successfully parsed JSON after fixing invalid escape characters.")
        except Exception as escape_error:
            print(f"Failed to fix escape characters: {escape_error}")

            # Try to extract JSON if it's embedded in other text
            try:
                # Try to find JSON array in the response using regex
                import re
                json_match = re.search(r'\[\s*{.*}\s*\]', response_text, re.DOTALL)
                if json_match:
                    scenarios = json.loads(json_match.group(0))
                    print("Successfully extracted JSON using regex pattern.")
                else:
                    # Try another approach - look for the first [ and last ]
                    start_idx = response_text.find('[')
                    end_idx = response_text.rfind(']')
                    if (start_idx != -1 and end_idx != -1 and start_idx < end_idx):
                        json_text = response_text[start_idx:end_idx+1]
                        try:
                            scenarios = json.loads(json_text)
                            print("Successfully extracted JSON using bracket positions.")
                        except Exception:
                            raise ValueError("Could not extract JSON from response")
                    else:
                        raise ValueError("Could not extract JSON from response")
            except Exception:
                # If all parsing attempts fail, provide detailed error
                error_msg = (
                    f"Invalid JSON output: {str(e)}.\n"
                    f"Response does not contain valid JSON. First 100 characters: {response_text[:100]}..."
                )
                # Save the raw response to a file for debugging
                try:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    raw_file_path = os.path.join("Test_cases", f"Latest_testcases_{current_jira_id}_{dashboard_test_type}_{timestamp}_raw.txt")
                    with open(raw_file_path, "w", encoding="utf-8") as f:
                        f.write(response_text)
                    print(f"Saved raw response to {raw_file_path}")
                except Exception as save_error:
                    print(f"Error saving raw response: {save_error}")

                raise ValueError(error_msg)

    # Ensure the output is a list of scenarios
    if not isinstance(scenarios, list):
        raise ValueError(f"Expected a JSON array of scenarios, but got {type(scenarios).__name__}")

    if len(scenarios) == 0:
        raise ValueError("JSON array is empty - no scenarios found")

    # Get the highest test case ID from the database for each test type in the scenarios
    try:
        import Test_case_db_helper as db
        # Get the JIRA ID from the issue
        jira_id = issue.key if issue else ""

        # Get all the test types from the scenarios
        scenario_test_types = set()
        for s in scenarios:
            if "type" in s and s["type"]:
                scenario_test_types.add(s["type"].lower())

        # Process each test type separately
        for test_type in scenario_test_types:
            print(f"Processing test type: {test_type}")

            # Get the highest test case ID for this test type
            highest_id = db.get_highest_test_case_id_number(db.DATABASE_PATH, jira_id, test_type)
            if highest_id > 0:
                # Store the highest ID for this test type
                print(f"Found highest test case ID {highest_id} for {jira_id} with test_type={test_type}")
                # We don't set the counter here, as it will be set for each scenario individually
            else:
                print(f"No existing test cases found for {jira_id} with test_type={test_type}, will start from TC_001")

        # If no test types found or no JIRA ID, reset the counter
        if not scenario_test_types or not jira_id:
            reset_test_case_counter()
            print(f"No test types found in scenarios or no JIRA ID provided, starting from TC_001")
    except Exception as e:
        print(f"Error getting highest test case ID from database: {e}")
        # Reset the counter if there's an error
        reset_test_case_counter()
        print("Resetting counter due to error")

    # Extract JIRA metadata if available
    project = issue.raw['fields']['project']['name'] if issue else ""
    issue_key = issue.key if issue else ""
    test_objective = issue.fields.summary if issue else ""

    # Clean up the feature name by removing any prefix pattern
    feature_name = test_objective
    if feature_name and isinstance(feature_name, str):
        # Process feature name to remove prefixes

        # Try a simpler approach - if it contains "TS 001" or similar pattern, split and take the rest
        if "TS " in feature_name and any(c.isdigit() for c in feature_name):
            # Split by space, find the part that has TS and numbers, and remove everything up to that
            parts = feature_name.split()
            for i, part in enumerate(parts):
                if part == "TS" and i+1 < len(parts) and parts[i+1].isdigit():
                    feature_name = " ".join(parts[i+2:])
                    break

        # If the above didn't work, try regex as a fallback
        if feature_name == test_objective:  # If no change was made
            # Remove common prefix patterns like "TS 001", "FEAT-123", "ID_456", etc.
            import re
            feature_name = re.sub(r'^[A-Za-z0-9]+[\s\-_]*\d+\s*', '', feature_name)

        # Feature name has been processed

        # If we accidentally removed everything, use the original
        if not feature_name.strip():
            feature_name = test_objective

    priority = issue.raw['fields']['priority']['name'] if issue else ""

    parsed_data = []
    for _, scenario in enumerate(scenarios, start=1):
        # Get scenario name (not used but kept for future reference)
        _ = scenario.get("scenario_name", "").strip()
        prerequisite = scenario.get("prerequisites", "").strip()
        steps = scenario.get("steps", [])

        # We don't need to get the highest test case ID for each scenario
        # The counter is already set to the highest ID across all test types

        # Increment the global counter for each scenario
        _test_case_counter += 1

        # Create Test Case ID with proper formatting (e.g., TC_001, TC_002)
        # Ensure there are no extra spaces in the Test Case ID
        test_case_id = f"TC_{_test_case_counter:03d}".strip()  # Format as TC_001, TC_002, etc.

        # We don't need to check for duplicate test case IDs
        # The counter is already set to the highest ID across all test types

        for step_idx, step in enumerate(steps, start=1):
            instruction = step.get("action", "").strip()
            expected_result = step.get("expected_result", "").strip()

            # Only include Project and Feature for the first step of each test case
            # For subsequent steps, leave these fields blank
            current_project = project if step_idx == 1 else ""
            current_feature = feature_name if step_idx == 1 else ""  # Use cleaned feature name without TS prefix

            # Only include Test Case ID and User Story ID for the first step
            current_test_case_id = test_case_id if step_idx == 1 else ""
            current_user_story_id = issue_key if step_idx == 1 else ""

            # Only include Test Case Objective and Prerequisite for the first step
            # Ensure there are no extra spaces in the Test Case Objective
            # Use the Test Case Objective from the scenario if it's provided, otherwise use the issue summary
            scenario_test_objective = scenario.get("Test Case Objective", "").strip()
            current_test_objective = scenario_test_objective if step_idx == 1 and scenario_test_objective else (test_objective.strip() if step_idx == 1 and test_objective else "")
            current_prerequisite = prerequisite if step_idx == 1 else ""  # Only include for first step

            # Get test type from scenario if available, otherwise use empty string
            test_type = scenario.get("type", "").strip().upper() if scenario.get("type") else ""

            # Determine the group number based on the test type
            group_number = 1  # Default group number
            if test_type == "NEGATIVE":
                group_number = 2
            elif test_type == "SECURITY":
                group_number = 3
            elif test_type == "PERFORMANCE":
                group_number = 4

            # Only include Timestamp in the first step of each test case, like other fields
            current_timestamp_value = current_timestamp if step_idx == 1 else ""

            parsed_data.append({
                "Timestamp": current_timestamp_value,  # Add timestamp as first column
                "Project": current_project,
                "Feature": current_feature,  # Use issue summary as Feature
                "User Story ID": str(current_user_story_id),  # Add User Story ID column with JIRA issue key as string
                "Test Case ID": current_test_case_id,
                "Test Case Objective": current_test_objective,
                "Prerequisite": current_prerequisite,  # Only include prerequisite for first step, otherwise empty string
                "Step No": step_idx,
                "Test Steps": instruction,
                "Expected Result": expected_result,
                "Actual Result": "",
                "Test Status": "",
                "Priority": priority if step_idx == 1 else "",  # Only include Priority in the first step
                "Defect ID": "",
                "Comments": "",
                "Test Type": test_type if step_idx == 1 else "",  # Only include Test Type in the first step
                "Test Group": f"Group {group_number}: {test_type}" if step_idx == 1 else ""  # Include Test Group with test type and group number
            })

    return pd.DataFrame(parsed_data)

def create_formatted_excel_from_scenarios(response_text_or_df, output_file_path: str, issue=None, is_dataframe=False, start_id_from=None, continue_numbering=False, save_to_db=True, test_type=None, create_excel=False) -> None:
    """Create a formatted Excel file from test scenarios.

    Args:
        response_text_or_df: Either the raw response text from the AI or a DataFrame of test scenarios
        output_file_path: Path to save the Excel file
        issue: The JIRA issue object
        is_dataframe: Whether response_text_or_df is a DataFrame
        start_id_from: Optional starting ID number for test cases
        continue_numbering: Whether to continue numbering from the previous test type
        save_to_db: Whether to save the test cases to the database
        test_type: The test type (e.g., "positive", "negative", "all")
        create_excel: Whether to create an Excel file (default: False)
                     IMPORTANT: Excel files are ONLY created when this is explicitly set to True
    """
    try:
        # Parse scenarios and create DataFrame
        if is_dataframe:
            df = response_text_or_df  # Use the DataFrame directly
        else:
            try:
                # Parse from response text
                df = parse_test_scenarios_json(response_text_or_df, issue, start_id_from=start_id_from, continue_numbering=continue_numbering, dashboard_test_type=test_type)
            except ValueError as e:
                # If parsing fails, create a minimal dataframe with error message and standard columns
                print(f"Error parsing test scenarios: {str(e)}")

                # Create a DataFrame with both error info and standard columns to maintain compatibility
                current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                df = pd.DataFrame({
                    "Timestamp": [current_timestamp],  # Add timestamp as first column
                    "Project": ["ERROR"],
                    "Feature": [""],
                    "User Story ID": ["ERROR"],  # Add User Story ID column as string
                    "Test Case ID": ["ERROR-1"],
                    "Test Case Objective": ["Failed to parse AI response"],
                    "Prerequisite": [""],
                    "Step No": [1],
                    "Test Steps": ["Check the AI response for formatting issues"],
                    "Expected Result": ["Properly formatted JSON response"],
                    "Actual Result": [""],
                    "Test Status": ["Failed"],
                    "Priority": ["High"],
                    "Defect ID": [""],
                    "Comments": [str(e)],
                    "Test Type": [""],
                    "Test Group": [""],
                    "Error": ["Failed to parse AI response as JSON. See raw output for details."],
                    "Raw Response Preview": [response_text_or_df[:500] + "..." if len(response_text_or_df) > 500 else response_text_or_df]
                })

        # Save to database if requested and we have a valid issue
        if save_to_db and issue and not df.empty and "Test Case ID" in df.columns:
            try:
                import Test_case_db_helper as db
                # Extract the test type from the output file path if not provided
                if not test_type and output_file_path:
                    file_name = os.path.basename(output_file_path)
                    parts = file_name.split('_')
                    if len(parts) >= 4:
                        # Check if it's an ALL file with a specific test type
                        if "ALL" in parts and len(parts) >= 5:
                            # Format: Latest_testcases_TP-1_ALL_positive_timestamp.xlsx
                            test_type = parts[4]
                        else:
                            # Format: Latest_testcases_TP-1_positive_timestamp.xlsx
                            test_type = parts[3]

                # If we still don't have a test type, default to "all"
                if not test_type:
                    test_type = "all"

                # Save to database
                print(f"Saving test cases to database for {issue.key} with test type {test_type}...")
                db.save_test_cases_to_database(db.DATABASE_PATH, issue.key, df, test_type)
                print(f"Test cases saved to database successfully.")
            except Exception as db_error:
                print(f"Error saving test cases to database: {str(db_error)}")
                if create_excel:
                    print("Continuing with Excel file creation...")
                else:
                    print("Skipping Excel file creation as requested.")
                    return

        # Skip Excel file creation if not requested
        if not create_excel:
            print(f"Skipping Excel file creation as create_excel=False")
            return

        # Ensure the output directory exists before creating Excel file
        output_dir = os.path.dirname(output_file_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # Create Excel file
        # Create a new workbook and select the active sheets
        wb = Workbook()
        ws = wb.active

        # Define styles
        header_font = Font(name='Calibri', size=11, bold=True)
        data_font = Font(name='Calibri', size=11)
        header_fill = PatternFill(start_color='B3E5FC', end_color='B3E5FC', fill_type='solid')  # Light blue
        center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

        # Define border style
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Add header row with formatting
        for c, column_name in enumerate(df.columns, start=1):
            cell = ws.cell(row=1, column=c, value=column_name)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = center_alignment
            cell.border = thin_border

        # Write DataFrame to worksheet with formatting
        for r, (_, row) in enumerate(df.iterrows(), start=2):
            for c, value in enumerate(row.values, start=1):
                cell = ws.cell(row=r, column=c, value=value)
                cell.font = data_font
                cell.alignment = center_alignment
                cell.border = thin_border

        # We don't need to merge cells - the data is already structured with empty cells
        # for step detail rows. The parsed_data in parse_test_scenarios_json already handles this
        # by only including Project, Feature, Test Case ID, etc. in rows with a Test Case ID
        # and leaving these fields blank in step detail rows.

        # Re-apply formatting to all cells to ensure it's not lost during merging
        for row in ws.iter_rows(min_row=2):  # Skip header row
            for cell in row:
                cell.font = data_font
                cell.alignment = center_alignment
                cell.border = thin_border

        # Alignment and formatting already applied during cell creation

        # Adjust column widths based on the longest cell in each column
        for col in ws.columns:
            max_length = 0
            col_letter = get_column_letter(col[0].column)
            for cell in col:
                try:
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # Add padding, cap at 50
            ws.column_dimensions[col_letter].width = adjusted_width

        # Freeze the header row (row 1) so it remains visible when scrolling
        # This is equivalent to using Excel's built-in "Freeze Top Row" option

        # Create a sheet view with a frozen pane
        ws.sheet_view.pane = Pane(ySplit=1, state='frozen', activePane='bottomLeft', topLeftCell='A2')
        ws.sheet_view.selection[0].activeCell = 'A2'
        ws.sheet_view.selection[0].sqref = 'A2'

        # Save the workbook
        wb.save(output_file_path)
        print(f"Formatted Excel file saved to: {output_file_path}")
    except Exception as e:
        print(f"Error creating Excel file: {str(e)}")

def save_scenarios_to_excel(parsed_data, filename="test_scenarios.xlsx"):
    df = pd.DataFrame(parsed_data)
    df.to_excel(filename, index=False)
    print(f"✅ Test scenarios saved successfully to {filename}")

def is_ollama_running():
    try:
        response = requests.get("http://localhost:11434/api/version", timeout=2)
        return response.status_code == 200, response.json().get("version", "unknown")
    except:
        return False, "not available"

def upload_edited_excel(uploaded_file, current_user=None):
    """
    Process an uploaded Excel file containing edited test cases.
    Saves the file to an 'edited_excel' folder with a timestamp.
    Converts the Excel file to CSV format.
    Saves the edited test cases to the database with the is_edited flag set to True.
    Uploads the CSV file to the corresponding Jira project using credentials from config.json.
    Links the test cases to the specified User Story ID within Jira.

    Args:
        uploaded_file: The uploaded file object from Streamlit
        current_user: The currently logged-in user (if None, defaults to "admin")

    Returns:
        Tuple of (success_flag, message, saved_file_path)
    """
    # If no user is provided, default to admin
    if not current_user:
        current_user = "admin"
    try:
        # Create edited_excel folder if it doesn't exist
        edited_folder = "edited_excel"
        os.makedirs(edited_folder, exist_ok=True)

        # Create csv_exports folder if it doesn't exist
        csv_folder = "csv_exports"
        os.makedirs(csv_folder, exist_ok=True)

        # Read the Excel file to validate it
        try:
            df = pd.read_excel(uploaded_file)

            # Basic validation - check for required columns
            required_columns = [
                "Timestamp", "Project", "Feature", "User Story ID", "Test Case ID",
                "Test Case Objective", "Prerequisite", "Step No", "Test Steps",
                "Expected Result", "Actual Result", "Test Status", "Priority",
                "Defect ID", "Comments", "Test Type", "Test Group"
            ]
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                return False, f"Uploaded file is missing required columns: {', '.join(missing_columns)}", None

        except Exception as e:
            return False, f"Error reading Excel file: {str(e)}", None

        # Generate timestamp for the file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = uploaded_file.name
        base_name, extension = os.path.splitext(file_name)

        # Create new file name with timestamp
        new_excel_name = f"{base_name}_{timestamp}{extension}"
        new_excel_path = os.path.join(edited_folder, new_excel_name)

        # Create CSV file name with timestamp
        new_csv_name = f"{base_name}_{timestamp}.csv"
        new_csv_path = os.path.join(csv_folder, new_csv_name)

        # Save the Excel file to the edited_excel folder
        with open(new_excel_path, "wb") as f:
            f.write(uploaded_file.getbuffer())

        # Convert Excel to CSV
        df.to_csv(new_csv_path, index=False)

        # Save the edited test cases to the database
        try:
            import Test_case_db_helper as db

            # Extract unique JIRA IDs from the dataframe
            jira_ids = df['User Story ID'].unique().tolist()
            jira_ids = [str(id) for id in jira_ids if pd.notna(id)]

            if jira_ids:
                # Get the test type from the dataframe
                test_types = df['Test Type'].unique().tolist()
                test_types = [str(tt).lower() for tt in test_types if pd.notna(tt)]

                # Use the first test type if available, otherwise default to "all"
                test_type = test_types[0] if test_types else "all"

                # For each JIRA ID, save the test cases to the database with is_edited=True
                for jira_id in jira_ids:
                    # Filter the dataframe to only include rows for this JIRA ID
                    jira_df = df[df['User Story ID'] == jira_id]

                    if not jira_df.empty:
                        # Save to database with is_edited=True
                        db_success = db.save_test_cases_to_database(
                            db.DATABASE_PATH,
                            jira_id,
                            jira_df,
                            test_type,
                            user_name=current_user,  # Use the current logged-in user
                            is_edited=True
                        )

                        if db_success:
                            print(f"Successfully saved edited test cases for {jira_id} to database")
                        else:
                            print(f"Failed to save edited test cases for {jira_id} to database")
        except Exception as db_error:
            print(f"Error saving edited test cases to database: {str(db_error)}")

        # Try to read Jira credentials from config.json
        try:
            import json
            with open('config.json', 'r') as config_file:
                config = json.load(config_file)
                jira_url = config.get('jira_server')
                jira_username = config.get('jira_user')
                jira_password = config.get('jira_password')  # Use the password you added to config.json

                # If Jira credentials are provided in config, upload to Jira
                if jira_url and jira_username and jira_password:
                    try:
                        # Extract unique User Story IDs from the dataframe and convert to strings
                        user_story_ids = df['User Story ID'].unique().tolist()
                        user_story_ids = [str(id) for id in user_story_ids if pd.notna(id)]

                        # Check if Zephyr integration is available
                        if not ZEPHYR_AVAILABLE:
                            # Try to load it again
                            if not load_zephyr_integration():
                                # If still not available, return a message
                                return True, f"Successfully uploaded edited test cases to {new_excel_path}, converted to CSV at {new_csv_path}, and saved to database. Zephyr integration is not available.", new_excel_path

                        # We don't need to check if zephyr-python-api is installed here
                        # If it's not installed, the upload_test_cases_to_jira function will raise an ImportError
                        # which will be caught in the except block below

                        # Upload the test cases to Jira
                        try:
                            success, message = upload_test_cases_to_jira(
                                csv_file_path=new_csv_path,
                                jira_url=jira_url,
                                jira_username=jira_username,
                                jira_password=jira_password,
                                user_story_ids=user_story_ids
                            )

                            if success:
                                return True, f"Successfully uploaded edited test cases to {new_excel_path}, converted to CSV at {new_csv_path}, saved to database, and {message}", new_excel_path
                            else:
                                return False, f"Successfully saved files but failed to upload to Jira: {message}", new_excel_path
                        except Exception as e:
                            # If there's an error using the module, return a message
                            return True, f"Successfully uploaded edited test cases to {new_excel_path}, converted to CSV at {new_csv_path}, and saved to database. Error with Zephyr integration: {str(e)}", new_excel_path
                    except Exception as e:
                        return False, f"Error uploading to Jira: {str(e)}", new_excel_path
                else:
                    return True, f"Successfully uploaded edited test cases to {new_excel_path}, converted to CSV at {new_csv_path}, and saved to database. No Jira credentials found in config.json.", new_excel_path
        except Exception as e:
            # If there's an error reading the config file, just return a message
            return True, f"Successfully uploaded edited test cases to {new_excel_path}, converted to CSV at {new_csv_path}, and saved to database. Could not read config.json: {str(e)}", new_excel_path

    except Exception as e:
        return False, f"Error processing edited test cases: {str(e)}", None

def upload_test_cases_to_excel_and_jira(test_cases_df, jira_id, test_type, jira_url=None, jira_username=None, jira_password=None, is_edited=False, user_name=None):
    """
    Upload test cases to Excel and optionally to Jira Zephyr Scale.

    Args:
        test_cases_df: DataFrame containing test cases
        jira_id: JIRA ID (e.g., "TP-1")
        test_type: Test type (e.g., "positive", "negative", "all")
        jira_url: Jira URL (optional)
        jira_username: Jira username (optional)
        jira_password: Jira password/token (optional)
        is_edited: Whether these test cases are edited versions of existing test cases
        user_name: Name of the user who created the test cases

    Returns:
        Tuple of (success, message, excel_path)
    """
    try:
        # Create Test_cases folder if it doesn't exist
        test_cases_dir = "Test_cases"
        os.makedirs(test_cases_dir, exist_ok=True)

        # Generate timestamp for the file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create file names
        if test_type.lower() == "all":
            excel_filename = f"Latest_testcases_{jira_id}_ALL_{timestamp}.xlsx"
        else:
            excel_filename = f"Latest_testcases_{jira_id}_{test_type}_{timestamp}.xlsx"

        excel_path = os.path.join(test_cases_dir, excel_filename)

        # Save to Excel
        create_formatted_excel_from_scenarios(
            test_cases_df,
            excel_path,
            is_dataframe=True,
            save_to_db=True,
            jira_id=jira_id,
            test_type=test_type,
            user_name=user_name,
            is_edited=is_edited
        )

        # If Jira credentials are not provided, just return success for Excel
        if not jira_url or not jira_username or not jira_password:
            return True, f"Successfully saved test cases to {excel_path} and database. Jira upload skipped (credentials not provided).", excel_path

        # Check if Zephyr integration is available
        if not ZEPHYR_AVAILABLE:
            # Try to load it again
            if not load_zephyr_integration():
                # If still not available, return a message
                return True, f"Successfully saved test cases to {excel_path} and database. Zephyr integration is not available.", excel_path

        # Upload directly to Jira using the REST API
        try:
            # Get user story IDs (split by comma if multiple)
            user_story_ids = [jira_id.strip()] if jira_id else []

            # Call the upload function with the DataFrame directly
            success, message = upload_test_cases_to_jira(
                dataframe=test_cases_df,  # Pass DataFrame directly instead of CSV
                jira_url=jira_url,
                jira_username=jira_username,
                jira_password=jira_password,
                user_story_ids=user_story_ids
            )

            if success:
                return True, f"Successfully saved test cases to {excel_path}, database, and uploaded to Jira Zephyr Scale.", excel_path
            else:
                return False, f"Saved test cases to {excel_path} and database, but failed to upload to Jira: {message}", excel_path

        except Exception as e:
            return False, f"Saved test cases to {excel_path} and database, but error uploading to Jira: {str(e)}", excel_path

    except Exception as e:
        return False, f"Error saving test cases: {str(e)}", None

def generate_test_scenarios(case_id, test_type, num_scenarios, selected_model, jira_client, ai_provider, google_api_key, is_all_test_types=False, continue_numbering=False, test_run_id=None):
    # Start timestamp for performance tracking
    start_time = datetime.now()

    # First get the issue from JIRA
    issue = jira_client.issue(case_id)

    # Check if we should use enhanced description
    use_enhanced = st.session_state.get("use_enhanced_description", False)
    if use_enhanced:
        # Try to get enhanced description from session state or database
        enhanced_desc = st.session_state.get("enhanced_description")
        if enhanced_desc:
            # Temporarily modify the issue's description
            original_desc = issue.fields.description
            issue.fields.description = enhanced_desc
            # Generate test cases with enhanced description
            try:
                response_text, token_count = _generate_test_scenarios_internal(
                    case_id, test_type, num_scenarios, selected_model,
                    ai_provider, google_api_key, issue
                )
            finally:
                # Restore original description
                issue.fields.description = original_desc
            return issue, response_text, None, (datetime.now() - start_time).total_seconds(), token_count

    # If not using enhanced or if enhanced description not available, use original
    response_text, token_count = _generate_test_scenarios_internal(
        case_id, test_type, num_scenarios, selected_model,
        ai_provider, google_api_key, issue
    )
    return issue, response_text, None, (datetime.now() - start_time).total_seconds(), token_count

def _generate_test_scenarios_internal(case_id, test_type, num_scenarios, selected_model, ai_provider, google_api_key, issue):
    """Internal function to handle the actual test scenario generation.
    Extracted to avoid code duplication between enhanced and original description paths."""

    if ai_provider == "Local":
        combined_text = extract_test_info_from_issue(issue, test_type=test_type, num_scenarios=num_scenarios)
        response_text = run_ollama_with_chat(combined_text, model=selected_model)
        return response_text, None

    elif ai_provider == "Google AI Studio":
        if not google_api_key:
            raise ValueError("Google AI Studio API Key is required.")

        attachment_path = None
        # Check for image attachments that might be useful for test generation
        if hasattr(issue.fields, 'attachment') and issue.fields.attachment:
            for attachment in issue.fields.attachment:
                # Look for image files
                if any(attachment.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg']):
                    # Download and save the image temporarily
                    image_data = attachment.get()
                    temp_dir = "temp_attachments"
                    os.makedirs(temp_dir, exist_ok=True)
                    attachment_path = os.path.join(temp_dir, attachment.filename)
                    with open(attachment_path, "wb") as f:
                        f.write(image_data)
                    break  # Just use the first image found

        # Generate the prompt with or without image
        combined_text = generate_gemini_test_gen_prompt(issue, test_type=test_type, num_scenarios=num_scenarios, attachment_path=attachment_path)

        # Handle the case where combined_text is a tuple (image, prompt) from generate_gemini_test_gen_prompt
        result = run_google_ai_studio(combined_text, api_key=google_api_key, model=selected_model)

        # Clean up temporary attachment file if it was created
        if attachment_path and os.path.exists(attachment_path):
            try:
                os.remove(attachment_path)
            except Exception as e:
                print(f"Warning: Could not remove temporary file {attachment_path}: {e}")

        # Check if the result is a tuple (text, token_count)
        if isinstance(result, tuple) and len(result) == 2:
            response_text, token_count = result
        else:
            response_text = str(result)
            token_count = None

        return response_text, token_count

    else:
        raise ValueError("Invalid AI provider selected.")