"""
AI-powered script optimization functionality.

This module handles the optimization of combined PyTest scripts using AI,
transforming merged scripts into well-structured, cohesive test modules.
"""

import logging
import uuid
import time
from typing import Optional, Dict, Any

# Get logger
logger = logging.getLogger("ScriptWeaver.core.ai_optimization")

# Import debug utility
from debug_utils import debug


def _build_optimization_prompt(
    combined_script: str,
    custom_instructions: Optional[str] = None
) -> tuple[str, str]:
    """
    Build a single‐shot prompt that asks the LLM to consolidate multiple
    step‐level PyTest scripts into one cohesive test case function.

    Args:
        combined_script: The script containing multiple step-level PyTest code snippets
        custom_instructions: Optional custom user instructions for optimization

    Returns:
        tuple: (system_prompt, user_prompt)
    """
    debug(f"Building consolidation prompt - script_length: {len(combined_script)}")
    debug("Using single-shot consolidation approach for test case function creation")

    # System prompt stays very simple:
    system_prompt = (
        "You are a meticulous senior QA‐automation engineer. "
        "Your job is to take several step‐level PyTest code snippets and combine them into a single, "
        "self‐contained test case function that runs end‐to‐end while preserving every assertion and import."
    )

    # Base user instructions for “one test case” consolidation:
    base_user = f"""
Please consolidate all of the following PyTest code snippets into one single test case function. Make sure to:
1. Name the function clearly (e.g. `def test_full_flow(browser):`) and use `browser` as your WebDriver fixture everywhere.
2. Keep exactly one `import pytest` at the top and preserve any other imports.
3. Reference module‐level `test_data` (or `load_test_data()`) instead of passing test data in as a fixture.
4. Preserve all assertions, markup, and logic from the original steps in the correct sequence.
5. Do not add or remove steps—just combine them into one function.
6. Return only the final consolidated test function (no explanations, no markdown fences).

Here is the combined step‐level script you need to merge:
```python
{combined_script}
```
"""
    # If the caller provided any extra instructions, prepend them:
    if custom_instructions:
        debug(f"Adding custom instructions - length: {len(custom_instructions)}")
        user_prompt = f"{custom_instructions}\n\n{base_user}"
    else:
        user_prompt = base_user

    debug("Consolidation prompt built successfully")
    return system_prompt, user_prompt

def _execute_optimization_ai_call(
    system_prompt: str,
    user_prompt: str,
    model_name: str,
    api_key: Optional[str],
    request_id: str,
    optimization_context: Dict[str, Any]
) -> str:
    """
    Execute the AI call for script consolidation through the centralized generate_llm_response function.

    Args:
        system_prompt: The system prompt for the AI
        user_prompt: The user prompt for the AI
        model_name: The AI model to use
        api_key: API key for Google AI
        request_id: Unique request ID for tracking
        optimization_context: Context information for logging

    Returns:
        str: The AI response
    """
    # Import here to avoid circular imports
    from .ai import generate_llm_response, log_ai_interaction

    debug(f"Executing AI consolidation call - model: {model_name}, request_id: {request_id}")

    # Log the prompt generation
    log_ai_interaction(
        function_name="optimize_script_with_ai",
        prompt=user_prompt,
        response="<prompt generation>",
        model_name=model_name,
        request_id=request_id,
        context=optimization_context,
        category="script_consolidation_prompt",
        is_prompt_generation=True
    )

    # Call the LLM to consolidate the script through centralized function
    debug("Calling generate_llm_response for script consolidation")
    response = generate_llm_response(
        system_prompt=system_prompt,
        user_prompt=user_prompt,
        model_name=model_name,
        api_key=api_key,
        function_name="optimize_script_with_ai",
        parent_request_id=request_id,
        context=optimization_context,
        category="script_consolidation"
    )

    debug(f"AI consolidation call completed - response length: {len(response) if response else 0}")
    return response


def _log_optimization_process(
    operation: str,
    request_id: str,
    optimization_context: Dict[str, Any],
    combined_script: str = "",
    optimized_script: str = "",
    model_name: str = "N/A",
    total_time_ms: float = 0,
    error: Optional[Exception] = None
) -> None:
    """
    Log the consolidation process using the custom debug function and AI interaction logging.

    Args:
        operation: The type of operation being logged
        request_id: Unique request ID for tracking
        optimization_context: Context information for logging
        combined_script: The original script (for length calculation)
        optimized_script: The consolidated script (for length calculation)
        model_name: The AI model used
        total_time_ms: Total processing time in milliseconds
        error: Exception if an error occurred
    """
    # Import here to avoid circular imports
    from .ai import log_ai_interaction

    debug(f"Logging consolidation process - operation: {operation}, request_id: {request_id}")

    if operation == "start":
        logger.info(f"Starting script consolidation operation [Request ID: {request_id}]")
        debug(f"Consolidation started - script_length: {len(combined_script)}")

    elif operation == "empty_script":
        logger.warning(f"Empty script provided for consolidation [Request ID: {request_id}]")
        debug("Empty script provided - returning empty result")

        # Log the trivial case
        log_ai_interaction(
            function_name="optimize_script_with_ai",
            prompt="Empty script provided",
            response="Returning empty script",
            model_name="N/A",
            request_id=request_id,
            context=optimization_context,
            category="script_consolidation_trivial"
        )

    elif operation == "size_warning":
        original_script_length = len(combined_script)
        logger.warning(f"Script very large ({original_script_length} chars) - consolidation may take longer [Request ID: {request_id}]")
        debug(f"Script size warning - length: {original_script_length} chars")

        # Log the warning
        log_ai_interaction(
            function_name="optimize_script_with_ai",
            prompt=f"Script very large ({original_script_length} chars)",
            response="Script is large, consolidation may take longer",
            model_name="N/A",
            request_id=request_id,
            context=optimization_context,
            category="script_consolidation_warning"
        )

    elif operation == "success":
        logger.info(f"Script consolidation completed successfully [Request ID: {request_id}]")
        debug(f"Consolidation successful - original: {len(combined_script)} chars, consolidated: {len(optimized_script)} chars, time: {total_time_ms:.2f}ms")

        # Log the successful consolidation
        log_ai_interaction(
            function_name="optimize_script_with_ai",
            prompt=f"Script length: {len(combined_script)} chars",
            response=f"Consolidated script length: {len(optimized_script)} chars",
            model_name=model_name,
            request_id=request_id,
            context={
                **optimization_context,
                'original_script_length': len(combined_script),
                'consolidated_script_length': len(optimized_script),
                'total_processing_time_ms': total_time_ms
            },
            latency_ms=total_time_ms,
            category="script_consolidation_success"
        )

    elif operation == "error":
        logger.error(f"Script consolidation failed: {error} [Request ID: {request_id}]")
        debug(f"Consolidation failed - error: {type(error).__name__}: {str(error)}")

        # Log the error with detailed information
        error_context = optimization_context.copy()
        error_context.update({
            'error_type': type(error).__name__,
            'total_processing_time_ms': total_time_ms
        })

        log_ai_interaction(
            function_name="optimize_script_with_ai",
            prompt=f"Script length: {len(combined_script)} chars",
            response=f"ERROR: {str(error)}",
            model_name="N/A",
            request_id=request_id,
            context=error_context,
            error=error,
            latency_ms=total_time_ms,
            category="script_consolidation_error"
        )


def optimize_script_with_ai(
    combined_script: str,
    *,
    model_name: str = "gemini-2.0-flash",  # Using Gemini 2.0 Flash model
    api_key: Optional[str] = None,
    max_chars: int = 30_000,          # larger budget for optimization
    context: Optional[Dict[str, Any]] = None,
    custom_instructions: Optional[str] = None  # custom user instructions for optimization
) -> str:
    """
    Use an LLM to consolidate a combined PyTest script into a single test case function.

    This function takes a combined script that was created by merging multiple step scripts
    and consolidates it into a single, cohesive test case function that runs end-to-end.

    Args:
        combined_script (str): The combined script to consolidate
        model_name (str): The AI model to use
        api_key (Optional[str]): API key for Google AI
        max_chars (int): Maximum characters allowed for the script
        context (Optional[Dict[str, Any]]): Additional context information
        custom_instructions (Optional[str]): Custom user instructions for consolidation

    Returns:
        str: The consolidated script with a single test case function
    """
    # Generate a unique request ID for this consolidation operation
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Create context information if not provided
    optimization_context = context or {}
    optimization_context.update({
        'script_length': len(combined_script),
        'operation': 'script_consolidation',
        'has_custom_instructions': bool(custom_instructions),
        'custom_instructions_length': len(custom_instructions) if custom_instructions else 0
    })

    # Log the start of consolidation process
    _log_optimization_process("start", request_id, optimization_context, combined_script)

    # ── trivial cases ────────────────────────────────────────────────────
    if not combined_script:
        _log_optimization_process("empty_script", request_id, optimization_context)
        return ""

    # ── token-budget guard: warn if script is very large ───────────────
    original_script_length = len(combined_script)
    if original_script_length > max_chars:
        _log_optimization_process("size_warning", request_id, optimization_context, combined_script)

    try:
        # Build the consolidation prompts using the dedicated function
        system_prompt, user_prompt = _build_optimization_prompt(
            combined_script=combined_script,
            custom_instructions=custom_instructions
        )

        # Execute the AI call using the dedicated function
        response = _execute_optimization_ai_call(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model_name=model_name,
            api_key=api_key,
            request_id=request_id,
            optimization_context=optimization_context
        )

        # Clean the response to remove markdown code blocks
        from .ai_helpers import clean_llm_response
        consolidated_script = clean_llm_response(response, "python")

        # Calculate total time
        end_time = time.time()
        total_time_ms = (end_time - start_time) * 1000

        # Log the successful consolidation using the dedicated function
        _log_optimization_process(
            operation="success",
            request_id=request_id,
            optimization_context=optimization_context,
            combined_script=combined_script,
            optimized_script=consolidated_script,
            model_name=model_name,
            total_time_ms=total_time_ms
        )

        return consolidated_script

    except Exception as exc:
        # Calculate total time even for errors
        end_time = time.time()
        total_time_ms = (end_time - start_time) * 1000

        # Log the error using the dedicated function
        _log_optimization_process(
            operation="error",
            request_id=request_id,
            optimization_context=optimization_context,
            combined_script=combined_script,
            model_name=model_name,
            total_time_ms=total_time_ms,
            error=exc
        )

        # Return the original script if consolidation fails
        return combined_script
