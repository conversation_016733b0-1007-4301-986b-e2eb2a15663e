"""
Element detection module for GretahAI ScriptWeaver.

This module provides functions for detecting UI elements on a webpage.
It is a wrapper around the detect_elements function in core.py to maintain
backward compatibility with the new modular structure.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.core.element_detection")

# Add the parent directory to the path so we can import from parent modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Import the detect_elements function from detect.py and interactive_selector module
try:
    # Import directly from detect.py module
    try:
        from .detect import detect_elements as _detect_elements
        from .detect import detect_elements_advanced as _detect_elements_advanced
        logger.info("Successfully imported detect_elements functions from detect.py module")
    except ImportError as detect_error:
        logger.error(f"Error importing detect_elements functions from detect.py module: {detect_error}")
        _detect_elements = None
        _detect_elements_advanced = None

    # Import interactive selector directly from the module
    try:
        # Try relative import first (preferred)
        from .interactive_selector import select_element_interactively as _select_element_interactively
        logger.info("Successfully imported select_element_interactively from interactive_selector module (relative import)")
    except ImportError as rel_error:
        logger.error(f"Error importing select_element_interactively with relative import: {rel_error}")
        # Try absolute import as fallback
        try:
            # Use absolute import to avoid any issues
            import os
            import sys
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from GretahAI_ScriptWeaver.core.interactive_selector import select_element_interactively as _select_element_interactively
            logger.info("Successfully imported select_element_interactively from interactive_selector module (absolute import)")
        except ImportError as selector_error:
            logger.error(f"Error importing select_element_interactively with absolute import: {selector_error}")
            _select_element_interactively = None

    # Create wrapper functions with the same signatures
    def detect_elements(url, browser=None, headless=True, save_to_file=True, elements_dir="detected_elements"):
        """
        Detect UI elements on a webpage.

        This is a wrapper around the detect_elements function in core.py to maintain
        backward compatibility with the new modular structure.

        Args:
            url (str): URL of the webpage to detect elements on
            browser (WebDriver, optional): Selenium WebDriver instance. If None, a new one will be created
            headless (bool, optional): Whether to run the browser in headless mode. Defaults to True
            save_to_file (bool, optional): Whether to save the detected elements to a file. Defaults to True
            elements_dir (str, optional): Directory to save the detected elements to. Defaults to "detected_elements"

        Returns:
            list: List of detected UI elements
        """
        logger.info(f"Detecting UI elements on {url}")
        if _detect_elements is None:
            logger.error(f"Cannot detect UI elements on {url}: detect_elements function not available")
            return []
        return _detect_elements(url, browser, headless, save_to_file, elements_dir)

    def detect_elements_advanced(url, browser=None, headless=True, save_to_file=True, elements_dir="detected_elements"):
        """
        Detect UI elements on a webpage with advanced options.

        This is a wrapper around the detect_elements_advanced function in core.py to maintain
        backward compatibility with the new modular structure.

        Args:
            url (str): URL of the webpage to detect elements on
            browser (WebDriver, optional): Selenium WebDriver instance. If None, a new one will be created
            headless (bool, optional): Whether to run the browser in headless mode. Defaults to True
            save_to_file (bool, optional): Whether to save the detected elements to a file. Defaults to True
            elements_dir (str, optional): Directory to save the detected elements to. Defaults to "detected_elements"

        Returns:
            list: List of detected UI elements
        """
        logger.info(f"Detecting UI elements (advanced) on {url}")
        if _detect_elements_advanced is None:
            logger.error(f"Cannot detect UI elements (advanced) on {url}: detect_elements_advanced function not available")
            return []
        return _detect_elements_advanced(url, browser, headless, save_to_file, elements_dir)

    def select_element_interactively(url, browser=None, headless=False):
        """
        Open a browser window for interactive element selection.

        This is a wrapper around the select_element_interactively function in core.py to maintain
        backward compatibility with the new modular structure.

        Args:
            url (str): URL of the webpage to select elements from
            browser (WebDriver, optional): Selenium WebDriver instance. If None, a new one will be created
            headless (bool, optional): Whether to run the browser in headless mode. Defaults to False

        Returns:
            dict: The selected element information
        """
        logger.info(f"Opening interactive element selector for {url}")
        if _select_element_interactively is None:
            logger.error(f"Cannot open interactive element selector for {url}: select_element_interactively function not available")
            return None
        return _select_element_interactively(url, browser, headless)

    def filter_qa_relevant_elements(elements, locator_strategy=None, locator_value=None):
        """
        Filter elements to keep only those relevant for QA automation.

        Args:
            elements (list): List of elements to filter
            locator_strategy (str, optional): Locator strategy from the step table entry
            locator_value (str, optional): Locator value from the step table entry

        Returns:
            list: Filtered list of elements
        """
        logger.info(f"Filtering {len(elements)} elements for QA relevance")

        # If no elements, return empty list
        if not elements:
            return []

        # Define QA-relevant attributes
        qa_attributes = ['id', 'name', 'class', 'type', 'value', 'placeholder', 'href', 'role', 'aria-label']

        # Define interactive elements
        interactive_tags = ['a', 'button', 'input', 'select', 'textarea', 'option', 'label', 'form']

        # Filter elements
        filtered_elements = []
        for element in elements:
            # Skip elements that are not visible
            if element.get('visible', True) is False:
                continue

            # Prioritize elements with matching locator strategy and value
            if locator_strategy and locator_value:
                if locator_strategy.lower() == 'css' and element.get('selector') and locator_value in element.get('selector'):
                    element['score'] = 100  # Give highest score to matching elements
                    filtered_elements.append(element)
                    continue

                if locator_strategy.lower() == 'xpath' and element.get('xpath') and locator_value in element.get('xpath'):
                    element['score'] = 100  # Give highest score to matching elements
                    filtered_elements.append(element)
                    continue

                if locator_strategy.lower() == 'id' and element.get('attributes', {}).get('id') and locator_value in element.get('attributes', {}).get('id'):
                    element['score'] = 100  # Give highest score to matching elements
                    filtered_elements.append(element)
                    continue

            # Check if element is interactive
            is_interactive = element.get('interactive', False) or element.get('tag', '').lower() in interactive_tags

            # Check if element has QA-relevant attributes
            has_qa_attributes = False
            for attr in qa_attributes:
                if element.get('attributes', {}).get(attr):
                    has_qa_attributes = True
                    break

            # Add element if it's interactive or has QA-relevant attributes
            if is_interactive or has_qa_attributes:
                # Calculate a score based on relevance
                score = 0
                if is_interactive:
                    score += 50
                if has_qa_attributes:
                    score += 30
                if element.get('attributes', {}).get('id'):
                    score += 20
                if element.get('attributes', {}).get('name'):
                    score += 15
                if element.get('attributes', {}).get('role'):
                    score += 10

                element['score'] = score
                filtered_elements.append(element)

        # Sort by score (highest first)
        filtered_elements.sort(key=lambda x: x.get('score', 0), reverse=True)

        # Limit to top 20 elements
        return filtered_elements[:20]

except ImportError as e:
    logger.error(f"Error importing detect_elements from core.py: {e}")

    # Create dummy functions that return empty lists
    def detect_elements(url, browser=None, headless=True, save_to_file=True, elements_dir="detected_elements"):
        """
        Dummy detect_elements function that returns an empty list.

        This function is used when the real detect_elements function cannot be imported.

        Args:
            url (str): URL of the webpage to detect elements on
            browser (WebDriver, optional): Selenium WebDriver instance. If None, a new one will be created
            headless (bool, optional): Whether to run the browser in headless mode. Defaults to True
            save_to_file (bool, optional): Whether to save the detected elements to a file. Defaults to True
            elements_dir (str, optional): Directory to save the detected elements to. Defaults to "detected_elements"

        Returns:
            list: Empty list
        """
        logger.error(f"Cannot detect UI elements on {url}: detect_elements function not available")
        return []

    def detect_elements_advanced(url, browser=None, headless=True, save_to_file=True, elements_dir="detected_elements"):
        """
        Dummy detect_elements_advanced function that returns an empty list.

        This function is used when the real detect_elements_advanced function cannot be imported.

        Args:
            url (str): URL of the webpage to detect elements on
            browser (WebDriver, optional): Selenium WebDriver instance. If None, a new one will be created
            headless (bool, optional): Whether to run the browser in headless mode. Defaults to True
            save_to_file (bool, optional): Whether to save the detected elements to a file. Defaults to True
            elements_dir (str, optional): Directory to save the detected elements to. Defaults to "detected_elements"

        Returns:
            list: Empty list
        """
        logger.error(f"Cannot detect UI elements (advanced) on {url}: detect_elements_advanced function not available")
        return []

    def select_element_interactively(url, browser=None, headless=False):
        """
        Dummy select_element_interactively function that returns None.

        This function is used when the real select_element_interactively function cannot be imported.

        Args:
            url (str): URL of the webpage to select elements from
            browser (WebDriver, optional): Selenium WebDriver instance. If None, a new one will be created
            headless (bool, optional): Whether to run the browser in headless mode. Defaults to False

        Returns:
            None
        """
        logger.error(f"Cannot open interactive element selector for {url}: select_element_interactively function not available")
        return None

    def filter_qa_relevant_elements(elements, locator_strategy=None, locator_value=None):
        """
        Dummy filter_qa_relevant_elements function that returns the input elements.

        This function is used when the real filter_qa_relevant_elements function cannot be imported.

        Args:
            elements (list): List of elements to filter
            locator_strategy (str, optional): Locator strategy from the step table entry
            locator_value (str, optional): Locator value from the step table entry

        Returns:
            list: The input elements (unfiltered)
        """
        logger.error(f"Cannot filter elements for QA relevance: filter_qa_relevant_elements function not available")
        return elements[:20] if elements else []
