"""
Performance monitoring utilities for the Interactive Element Selection feature.
This module provides tools to measure and track performance improvements.
"""

import time
import logging
import psutil
import threading
from contextlib import contextmanager
from typing import Dict, Any, Optional

logger = logging.getLogger("ScriptWeaver.core.performance_monitor")

class PerformanceMonitor:
    """
    Monitor performance metrics for interactive element selection operations.
    """
    
    def __init__(self):
        self.metrics = {}
        self.start_time = None
        self.start_memory = None
        self.start_cpu = None
        
    def start_monitoring(self, operation_name: str):
        """Start monitoring performance for a specific operation."""
        self.start_time = time.time()
        self.start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        self.start_cpu = psutil.cpu_percent(interval=None)
        
        logger.info(f"Performance monitoring started for: {operation_name}")
        logger.info(f"Initial memory usage: {self.start_memory:.2f} MB")
        
    def stop_monitoring(self, operation_name: str) -> Dict[str, Any]:
        """Stop monitoring and return performance metrics."""
        if self.start_time is None:
            logger.warning("Monitoring was not started")
            return {}
            
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        end_cpu = psutil.cpu_percent(interval=None)
        
        metrics = {
            'operation': operation_name,
            'duration_seconds': end_time - self.start_time,
            'memory_start_mb': self.start_memory,
            'memory_end_mb': end_memory,
            'memory_delta_mb': end_memory - self.start_memory,
            'cpu_usage_percent': end_cpu,
            'timestamp': time.time()
        }
        
        self.metrics[operation_name] = metrics
        
        logger.info(f"Performance monitoring completed for: {operation_name}")
        logger.info(f"Duration: {metrics['duration_seconds']:.2f} seconds")
        logger.info(f"Memory usage: {metrics['memory_delta_mb']:.2f} MB delta")
        logger.info(f"CPU usage: {metrics['cpu_usage_percent']:.1f}%")
        
        return metrics
        
    @contextmanager
    def monitor_operation(self, operation_name: str):
        """Context manager for monitoring an operation."""
        self.start_monitoring(operation_name)
        try:
            yield self
        finally:
            self.stop_monitoring(operation_name)
            
    def get_metrics(self) -> Dict[str, Any]:
        """Get all collected metrics."""
        return self.metrics.copy()
        
    def log_metrics_summary(self):
        """Log a summary of all collected metrics."""
        if not self.metrics:
            logger.info("No performance metrics collected")
            return
            
        logger.info("=== Performance Metrics Summary ===")
        for operation, metrics in self.metrics.items():
            logger.info(f"{operation}:")
            logger.info(f"  Duration: {metrics['duration_seconds']:.2f}s")
            logger.info(f"  Memory Delta: {metrics['memory_delta_mb']:.2f}MB")
            logger.info(f"  CPU Usage: {metrics['cpu_usage_percent']:.1f}%")

class BrowserPerformanceMonitor:
    """
    Monitor browser-specific performance metrics during element selection.
    """
    
    def __init__(self, driver):
        self.driver = driver
        self.metrics = {}
        
    def inject_performance_monitoring_script(self):
        """Inject JavaScript to monitor browser performance."""
        script = """
        // Performance monitoring for element selection
        window.gretahPerformance = {
            startTime: performance.now(),
            eventCounts: {
                mouseover: 0,
                mouseout: 0,
                click: 0
            },
            timings: {
                scriptInjection: performance.now(),
                firstHover: null,
                selection: null
            }
        };
        
        // Override event handlers to count events
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (type in window.gretahPerformance.eventCounts) {
                const wrappedListener = function(event) {
                    window.gretahPerformance.eventCounts[type]++;
                    if (type === 'mouseover' && !window.gretahPerformance.timings.firstHover) {
                        window.gretahPerformance.timings.firstHover = performance.now();
                    }
                    return listener.call(this, event);
                };
                return originalAddEventListener.call(this, type, wrappedListener, options);
            }
            return originalAddEventListener.call(this, type, listener, options);
        };
        
        return true;
        """
        
        try:
            result = self.driver.execute_script(script)
            logger.info("Browser performance monitoring script injected successfully")
            return result
        except Exception as e:
            logger.error(f"Failed to inject performance monitoring script: {e}")
            return False
            
    def get_browser_metrics(self) -> Dict[str, Any]:
        """Get performance metrics from the browser."""
        try:
            metrics = self.driver.execute_script("""
                if (!window.gretahPerformance) return null;
                
                return {
                    totalTime: performance.now() - window.gretahPerformance.startTime,
                    eventCounts: window.gretahPerformance.eventCounts,
                    timings: window.gretahPerformance.timings,
                    memoryUsage: performance.memory ? {
                        usedJSHeapSize: performance.memory.usedJSHeapSize,
                        totalJSHeapSize: performance.memory.totalJSHeapSize,
                        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                    } : null
                };
            """)
            
            if metrics:
                logger.info("Browser performance metrics collected:")
                logger.info(f"  Total time: {metrics.get('totalTime', 0):.2f}ms")
                logger.info(f"  Event counts: {metrics.get('eventCounts', {})}")
                if metrics.get('memoryUsage'):
                    memory = metrics['memoryUsage']
                    logger.info(f"  JS Heap used: {memory['usedJSHeapSize'] / 1024 / 1024:.2f}MB")
                    
            return metrics or {}
            
        except Exception as e:
            logger.error(f"Failed to get browser performance metrics: {e}")
            return {}

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

def monitor_interactive_selection(func):
    """Decorator to monitor interactive element selection performance."""
    def wrapper(*args, **kwargs):
        with performance_monitor.monitor_operation(f"interactive_selection_{func.__name__}"):
            return func(*args, **kwargs)
    return wrapper
