#!/usr/bin/env python3
"""
Test script to verify the Stage 10 refactoring works correctly.
"""

def test_imports():
    """Test that all imports work correctly."""
    try:
        print("Testing Stage 10 component imports...")
        
        # Test individual modules
        print("1. Testing template components...")
        from ui_components.stage10_template_components import render_empty_playground_message
        print("   ✅ Template components imported successfully")
        
        print("2. Testing gap analysis components...")
        from ui_components.stage10_gap_analysis_components import render_gap_analysis_interface
        print("   ✅ Gap analysis components imported successfully")
        
        print("3. Testing script generation components...")
        from ui_components.stage10_script_generation_components import render_script_generation_controls
        print("   ✅ Script generation components imported successfully")
        
        print("4. Testing execution components...")
        from ui_components.stage10_execution_components import render_script_execution_section_header
        print("   ✅ Execution components imported successfully")
        
        print("5. Testing failure analysis components...")
        from ui_components.stage10_failure_analysis_components import render_failure_analysis_button
        print("   ✅ Failure analysis components imported successfully")
        
        print("6. Testing navigation components...")
        from ui_components.stage10_navigation_components import render_stage10_footer
        print("   ✅ Navigation components imported successfully")
        
        print("7. Testing aggregator...")
        from ui_components.stage10_components import (
            render_empty_playground_message,
            render_gap_analysis_interface,
            render_script_generation_controls,
            render_script_execution_section_header,
            render_failure_analysis_button,
            render_stage10_footer
        )
        print("   ✅ Aggregator imports successful")
        
        print("\n🎉 All imports successful! Refactoring verification complete.")
        return True
        
    except Exception as e:
        print(f"\n❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_function_availability():
    """Test that functions are available through the aggregator."""
    try:
        print("\nTesting function availability...")
        
        from ui_components.stage10_components import __all__
        print(f"Available functions: {len(__all__)}")
        
        # Test a few key functions
        from ui_components.stage10_components import (
            render_template_selection_interface,
            render_gap_analysis_interface,
            render_script_generation_controls,
            render_execution_results_summary
        )
        
        print("✅ Key functions are accessible through aggregator")
        return True
        
    except Exception as e:
        print(f"❌ Function availability test failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Stage 10 Refactoring Verification Test")
    print("=" * 60)
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test function availability
    if not test_function_availability():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED - Refactoring is successful!")
    else:
        print("❌ TESTS FAILED - Issues detected in refactoring")
    print("=" * 60)
