"""
Element matching module for GretahAI ScriptWeaver.

This module provides functions for matching test steps to UI elements.
It is a wrapper around the match_elements_with_ai function in core.py to maintain
backward compatibility with the new modular structure.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.core.element_matching")

# Add the parent directory to the path so we can import from parent modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Import the match_elements_with_ai function from core.py
try:
    # Try to import from core.py for backward compatibility
    try:
        from core import match_elements_with_ai as _match_elements_with_ai
        logger.info("Successfully imported match_elements_with_ai from core.py")
    except ImportError as core_error:
        logger.error(f"Error importing match_elements_with_ai from core.py: {core_error}")
        # Define a simple implementation as a fallback
        def _match_elements_with_ai(test_case, elements, api_key=None):
            logger.warning(f"Using fallback implementation of match_elements_with_ai")
            # Simple matching logic - match by ID or text content
            result = {}
            test_case_id = test_case.get('Test Case ID', 'unknown')

            # Get steps from the test case
            steps = test_case.get('Steps', [])

            for step in steps:
                step_no = str(step.get('Step No', ''))
                step_text = step.get('Test Steps', '')

                # Initialize matches for this step
                if test_case_id not in result:
                    result[test_case_id] = {}

                result[test_case_id][step_no] = []

                # Simple matching logic
                for element in elements:
                    score = 0

                    # Check if element ID or text matches keywords in step text
                    element_id = element.get('attributes', {}).get('id', '')
                    element_text = element.get('attributes', {}).get('text', '')

                    # Split step text into words and check for matches
                    words = step_text.lower().split()
                    for word in words:
                        if word and len(word) > 3:  # Only consider words with more than 3 characters
                            if word in element_id.lower():
                                score += 30
                            if word in element_text.lower():
                                score += 20

                    # Add element to matches if score is above threshold
                    if score > 0:
                        result[test_case_id][step_no].append({
                            'element': element,
                            'score': score / 100,  # Normalize score to 0-1 range
                            'reasoning': 'Simple keyword matching'
                        })

                # Sort matches by score (highest first)
                if test_case_id in result and step_no in result[test_case_id]:
                    result[test_case_id][step_no].sort(key=lambda x: x.get('score', 0), reverse=True)

            return result

    # Create a wrapper function with the same signature
    def match_elements_with_ai(test_case, elements, api_key=None):
        """
        Match test steps to UI elements using AI.

        This is a wrapper around the match_elements_with_ai function in core.py to maintain
        backward compatibility with the new modular structure.

        Args:
            test_case (dict): Test case to match elements for
            elements (list): List of UI elements to match against
            api_key (str, optional): API key for Google AI. If None, use initialized client

        Returns:
            dict: Dictionary mapping test steps to matched elements
        """
        logger.info(f"Matching elements for test case {test_case.get('Test Case ID', 'unknown')}")
        return _match_elements_with_ai(test_case, elements, api_key)

except ImportError as e:
    logger.error(f"Error importing match_elements_with_ai from core.py: {e}")

    # Create a dummy function that returns an empty dictionary
    def match_elements_with_ai(test_case, elements, api_key=None):
        """
        Dummy match_elements_with_ai function that returns an empty dictionary.

        This function is used when the real match_elements_with_ai function cannot be imported.

        Args:
            test_case (dict): Test case to match elements for
            elements (list): List of UI elements to match against
            api_key (str, optional): API key for Google AI. If None, use initialized client

        Returns:
            dict: Empty dictionary
        """
        logger.error(f"Cannot match elements for test case {test_case.get('Test Case ID', 'unknown')}: match_elements_with_ai function not available")
        return {}
