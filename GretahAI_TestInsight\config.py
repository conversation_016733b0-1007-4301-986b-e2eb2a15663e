import streamlit as st
"""
Configuration module for Gretah AI TestInsight Pro Streamlit application.
This module provides utility functions for configuring the Streamlit application's
appearance, including page setup, CSS loading, and theme management.
Functions:
    load_css(file_name): Loads custom CSS from a file into the Streamlit app.
    setup_page_config(): Sets the Streamlit page configuration and loads custom CSS.
    apply_dark_mode_toggle(): Applies dark or light mode based on user selection.
Note:
    When using apply_dark_mode_toggle(), the closing div tag should be added
    at the end of the page rendering.
"""

def load_css(file_name):
    """Loads CSS from a file into the Streamlit app."""
    try:
        with open(file_name) as f:
            st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.error(f"CSS file not found: {file_name}")

def setup_page_config():
    """Sets the Streamlit page configuration and loads custom CSS."""
    st.set_page_config(page_title="Gretah AI TestInsight Pro", layout="wide")
    load_css("style.css")

def apply_dark_mode_toggle():
    """Applies dark or light mode based on session state."""
    dark_mode = st.sidebar.checkbox("🌙 Dark Mode", value=st.session_state.get("dark_mode", True))
    st.session_state["dark_mode"] = dark_mode

    # Apply Dark or Light Mode CSS class wrapper
    if dark_mode:
        st.markdown('<div class="dark-mode">', unsafe_allow_html=True)
    else:
        st.markdown('<div class="light-mode">', unsafe_allow_html=True)
    # The closing div tag should be added at the very end of the page rendering
