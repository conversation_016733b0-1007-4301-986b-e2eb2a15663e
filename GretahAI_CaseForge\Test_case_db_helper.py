import sqlite3
import os
import json
import time
import random
import shutil
import sys
from datetime import datetime
import threading
import traceback
import pandas as pd
import functools
from pathlib import Path

# Define the database path with absolute path
# Use the directory where this file is located as the base for the database
DATABASE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_cases_v2.db")
print(f"Database path set to: {DATABASE_PATH}")

# Use thread-local storage for database connections
thread_local = threading.local()

def retry_on_db_lock(max_attempts=5, initial_wait=0.1):
    """
    Decorator to retry operations when SQLite database is locked.

    Args:
        max_attempts: Maximum number of retry attempts
        initial_wait: Initial wait time between retries (will be increased exponentially)
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            last_error = None
            wait_time = initial_wait

            while attempt < max_attempts:
                try:
                    return func(*args, **kwargs)
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e):
                        attempt += 1
                        last_error = e
                        if attempt < max_attempts:
                            # Exponential backoff with jitter
                            jitter = random.uniform(0.8, 1.2)
                            sleep_time = wait_time * jitter
                            print(f"Database locked, retrying in {sleep_time:.2f}s (attempt {attempt}/{max_attempts})")
                            time.sleep(sleep_time)
                            wait_time *= 2  # Exponential backoff
                        continue
                    else:
                        # Close connection if it exists in thread local on other operational errors
                        if hasattr(thread_local, 'connection') and thread_local.connection:
                            try:
                                thread_local.connection.close()
                            except Exception:
                                pass
                            thread_local.connection = None
                        raise  # Re-raise if it's a different error
                except Exception as e:
                    # Close connection on any other exception
                    if hasattr(thread_local, 'connection') and thread_local.connection:
                        try:
                            thread_local.connection.close()
                        except Exception:
                            pass
                        thread_local.connection = None
                    raise  # Re-raise any other exception

            # If all attempts failed
            print(f"All {max_attempts} attempts failed due to database locks")
            # Ensure connection is closed before raising the final error
            if hasattr(thread_local, 'connection') and thread_local.connection:
                try:
                    thread_local.connection.close()
                except Exception:
                    pass
                thread_local.connection = None
            raise last_error

        return wrapper
    return decorator

def get_thread_local_connection(database_path, timeout=30):
    """Gets or creates a database connection for the current thread."""
    if not hasattr(thread_local, 'connection') or thread_local.connection is None:
        try:
            # Ensure the database directory exists
            db_dir = os.path.dirname(database_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)

            conn = sqlite3.connect(database_path, timeout=timeout, check_same_thread=False)
            conn.row_factory = sqlite3.Row
            conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
            conn.execute(f"PRAGMA busy_timeout={timeout * 1000}")  # Set busy timeout
            conn.execute("PRAGMA foreign_keys=OFF")  # Disable foreign key constraints to allow deleting records
            thread_local.connection = conn
        except sqlite3.Error as e:
            print(f"Failed to create connection in thread {threading.get_ident()}: {e}")
            thread_local.connection = None
            raise
    return thread_local.connection

def close_thread_local_connection():
    """Closes the connection for the current thread if it exists."""
    if hasattr(thread_local, 'connection') and thread_local.connection:
        try:
            # Check if the connection is open before closing
            if thread_local.connection:
                # Commit any pending transactions
                if thread_local.connection.in_transaction:
                    try:
                        thread_local.connection.commit()
                    except Exception:
                        thread_local.connection.rollback()
                # Close the connection
                thread_local.connection.close()
        except Exception as e:
            print(f"Error closing connection in thread {threading.get_ident()}: {e}")
        finally:
            thread_local.connection = None

@retry_on_db_lock()
def migrate_database(database_path):
    """Migrates the database schema to the latest version."""
    print("Starting database migration...")

    # Use the new comprehensive schema verification and update function
    return verify_and_update_schema(database_path)

@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def init_db(database_path):
    """Initializes the database schema if it doesn't exist."""
    # Create a dedicated connection for this function
    conn = None
    try:
        # Ensure the database directory exists
        db_dir = os.path.dirname(database_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)

        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Enable Foreign Key support
        cursor.execute("PRAGMA foreign_keys = ON;")
        cursor.execute("PRAGMA journal_mode=WAL;")  # Write-Ahead Logging for better concurrency
        cursor.execute("PRAGMA busy_timeout=60000")  # 60 second timeout

        # Find and drop any old per-JIRA tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'test_cases_%'")
        old_tables = cursor.fetchall()
        for table in old_tables:
            print(f"Dropping old table: {table[0]}")
            cursor.execute(f"DROP TABLE IF EXISTS {table[0]}")

        # Test Runs Table - This is now the primary table (first layer)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_runs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                jira_id TEXT NOT NULL,
                test_type TEXT NOT NULL,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                num_test_cases INTEGER,
                status TEXT DEFAULT 'planned',
                user_name TEXT,
                notes TEXT
            )
        ''')

        # JIRA Issues Table - This now references Test Run (second layer)
        cursor.execute('''            CREATE TABLE IF NOT EXISTS jira_issues (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                jira_id TEXT NOT NULL UNIQUE,
                summary TEXT,
                description TEXT,
                enhanced_description TEXT,
                enhanced_timestamp TEXT,
                status TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                test_run_id INTEGER,
                FOREIGN KEY (test_run_id) REFERENCES test_runs(id)
            )
        ''')

        # Test Cases Table - This now references JIRA Issue (third layer)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                jira_id TEXT NOT NULL,
                test_case_id TEXT NOT NULL,
                test_case_objective TEXT,
                prerequisite TEXT,
                priority TEXT,
                test_type TEXT,
                test_group TEXT,
                project TEXT,
                feature TEXT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                is_latest BOOLEAN DEFAULT 1,
                dashboard_test_type TEXT,
                user_name TEXT,
                jira_issue_id INTEGER,
                is_edited BOOLEAN DEFAULT 0,
                test_run_id INTEGER,
                FOREIGN KEY (jira_issue_id) REFERENCES jira_issues(id),
                FOREIGN KEY (test_run_id) REFERENCES test_runs(id)
            )
        ''')

        # Test Steps Table - This references Test Cases (fourth layer)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_steps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_case_id INTEGER NOT NULL,
                step_number INTEGER NOT NULL,
                test_step TEXT NOT NULL,
                expected_result TEXT,
                actual_result TEXT,
                test_status TEXT,
                defect_id TEXT,
                comments TEXT,
                dashboard_test_type TEXT,
                user_name TEXT,
                FOREIGN KEY (test_case_id) REFERENCES test_cases(id)
            )
        ''')

        # Test Case Executions Table - This tracks the execution of individual test cases
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_case_executions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_run_id INTEGER NOT NULL,
                test_case_id INTEGER NOT NULL,
                execution_date TEXT DEFAULT CURRENT_TIMESTAMP,
                executed_by TEXT,
                status TEXT DEFAULT 'not_run',
                defect_id TEXT,
                comments TEXT,
                FOREIGN KEY (test_run_id) REFERENCES test_runs(id),
                FOREIGN KEY (test_case_id) REFERENCES test_cases(id)
            )
        ''')

        # Create indexes for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_runs_jira_id ON test_runs (jira_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_jira_issues_jira_id ON jira_issues (jira_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_jira_issues_test_run_id ON jira_issues (test_run_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_id ON test_cases (jira_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_issue_id ON test_cases (jira_issue_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_test_case_id ON test_cases (test_case_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_test_type ON test_cases (test_type);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_dashboard_test_type ON test_cases (dashboard_test_type);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_steps_test_case_id ON test_steps (test_case_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_steps_dashboard_test_type ON test_steps (dashboard_test_type);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_case_executions_test_run_id ON test_case_executions (test_run_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_case_executions_test_case_id ON test_case_executions (test_case_id);")

        # Add App Configuration Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS app_config (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Initialize default admin password if not exists
        cursor.execute("INSERT OR IGNORE INTO app_config (key, value) VALUES ('admin_password', 'admin123')")

        # Initialize default settings
        cursor.execute("INSERT OR IGNORE INTO app_config (key, value) VALUES ('allow_delete_test_cases', 'true')")
        cursor.execute("INSERT OR IGNORE INTO app_config (key, value) VALUES ('allow_clear_database', 'false')")

        conn.commit()
        print("Test case database initialized successfully.")

        # Close the connection before running schema verification
        conn.close()
        print("Database connection closed for initial setup")

        # Verify and update the database schema
        verify_and_update_schema(database_path)

        # Update existing test cases with default values
        update_existing_test_cases(database_path)

        return True
    except sqlite3.Error as e:
        print(f"Database initialization error: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        # Always close the connection
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock()
def clear_database(database_path, admin_password=None):
    """Clears all tables in the database.

    Args:
        database_path: Path to the SQLite database
        admin_password: Admin password for verification (required if allow_clear_database is false)

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        # Check if clear database operations are allowed
        # Always allow clearing the database with the correct admin password
        if admin_password:
            if not admin_config.verify_admin_password(admin_password):
                print("Invalid admin password")
                return False
        else:
            # Check if clearing database without password is allowed
            allow_clear = admin_config.get_config("allow_clear_database")
            if not allow_clear:
                print("Admin password required to clear the entire database")
                return False
            print("Clearing database without password is allowed")

        # Create a new connection directly (not using thread-local)
        conn = sqlite3.connect(database_path, timeout=60)
        cursor = conn.cursor()
        print("Attempting to clear database...")

        # Disable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = OFF;")

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Delete all data from tables in the correct order
        print("Deleting data from all tables...")

        # Delete from test_steps first
        cursor.execute("DELETE FROM test_steps;")
        print("Deleted all data from test_steps table")

        # Delete from test_cases
        cursor.execute("DELETE FROM test_cases;")
        print("Deleted all data from test_cases table")

        # Delete from test_runs
        cursor.execute("DELETE FROM test_runs;")
        print("Deleted all data from test_runs table")

        # Delete from test_case_executions if it exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_case_executions';")
        if cursor.fetchone():
            cursor.execute("DELETE FROM test_case_executions;")
            print("Deleted all data from test_case_executions table")

        # Delete from jira_issues
        cursor.execute("DELETE FROM jira_issues;")
        print("Deleted all data from jira_issues table")

        # Reset the auto-increment counters
        print("Resetting auto-increment counters...")
        cursor.execute("DELETE FROM sqlite_sequence WHERE name IN ('test_steps', 'test_cases', 'test_runs', 'jira_issues', 'test_case_executions');")

        # Re-enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Commit the transaction
        conn.commit()

        # Verify that all tables are empty
        cursor.execute("SELECT COUNT(*) FROM jira_issues;")
        jira_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM test_runs;")
        runs_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM test_cases;")
        cases_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM test_steps;")
        steps_count = cursor.fetchone()[0]

        print(f"Verification counts - JIRA issues: {jira_count}, Test runs: {runs_count}, Test cases: {cases_count}, Test steps: {steps_count}")

        if jira_count > 0:
            print("Warning: JIRA issues table still has data. Forcing deletion...")
            cursor.execute("DELETE FROM jira_issues;")
            conn.commit()

        print("Database cleared successfully.")
        return True
    except sqlite3.Error as e:
        print(f"Error clearing database: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

# Import the admin configuration module
import admin_config

# Function to get configuration value
def get_app_config(database_path, key):
    """Gets a configuration value from the admin_config module.

    Args:
        database_path: Path to the SQLite database (not used, kept for compatibility)
        key: The configuration key to retrieve

    Returns:
        The configuration value, or None if not found
    """
    return admin_config.get_config(key)

# Function to update configuration value
def update_app_config(database_path, key, value):
    """Updates a configuration value using the admin_config module.

    Args:
        database_path: Path to the SQLite database (not used, kept for compatibility)
        key: The configuration key to update
        value: The new value to set

    Returns:
        True if successful, False otherwise
    """
    return admin_config.update_config(key, value)

# Function to verify admin password
def verify_admin_password(database_path, password):
    """Verifies if the provided password matches the admin password.

    Args:
        database_path: Path to the SQLite database (not used, kept for compatibility)
        password: The password to verify

    Returns:
        True if the password matches, False otherwise
    """
    return admin_config.verify_admin_password(password)

@retry_on_db_lock()
def clear_database_for_jira(database_path, jira_id, test_type=None, admin_password=None):
    """Clears all test cases for a specific JIRA ID and test type.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        test_type: Test type (e.g., "positive", "negative", "all")
        admin_password: Admin password for verification (required if allow_delete_test_cases is false)

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        # Check if delete operations are allowed
        if not admin_config.is_operation_allowed("delete_test_cases", admin_password):
            print("Admin password required to delete test cases")
            return False

        # Create a new connection directly (not using thread-local)
        conn = sqlite3.connect(database_path, timeout=60)
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Disable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = OFF;")

        # Always filter by dashboard_test_type
        if test_type:
            # Get test case IDs to delete with the specified dashboard_test_type
            cursor.execute(
                "SELECT id FROM test_cases WHERE jira_id = ? AND dashboard_test_type = ?",
                (jira_id, test_type.lower())
            )
            test_case_ids = [row[0] for row in cursor.fetchall()]

            if test_case_ids:
                # Delete test steps for these test cases
                placeholders = ", ".join(["?" for _ in test_case_ids])
                cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)

                # Delete the test cases
                cursor.execute(f"DELETE FROM test_cases WHERE id IN ({placeholders})", test_case_ids)

                # Delete test runs for this JIRA ID and test type
                cursor.execute(
                    "DELETE FROM test_runs WHERE jira_id = ? AND test_type = ?",
                    (jira_id, test_type.lower())
                )

                print(f"Deleted test cases for JIRA ID {jira_id} with dashboard_test_type={test_type.lower()}")
            else:
                print(f"No test cases found for JIRA ID {jira_id} with dashboard_test_type={test_type.lower()}")
        else:
            # If no test_type specified, delete all test cases for this JIRA ID
            cursor.execute(
                "SELECT id FROM test_cases WHERE jira_id = ?",
                (jira_id,)
            )
            test_case_ids = [row[0] for row in cursor.fetchall()]

            if test_case_ids:
                # Delete test steps for these test cases
                placeholders = ", ".join(["?" for _ in test_case_ids])
                cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)

                # Delete the test cases
                cursor.execute("DELETE FROM test_cases WHERE jira_id = ?", (jira_id,))

                # Delete test runs for this JIRA ID
                cursor.execute("DELETE FROM test_runs WHERE jira_id = ?", (jira_id,))

                print(f"Deleted all test cases for JIRA ID {jira_id}")

        # Check if there are any test runs left for this JIRA ID
        cursor.execute("SELECT COUNT(*) FROM test_runs WHERE jira_id = ?", (jira_id,))
        count = cursor.fetchone()[0]

        # If there are no test runs left, delete the JIRA issue
        if count == 0:
            cursor.execute("DELETE FROM jira_issues WHERE jira_id = ?", (jira_id,))
            print(f"Deleted JIRA issue: {jira_id}")
        else:
            print(f"Not deleting JIRA issue {jira_id} as there are still {count} test runs associated with it")

        # Re-enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Check if we've deleted all test cases for this JIRA ID
        cursor.execute("SELECT COUNT(*) FROM test_cases WHERE jira_id = ?", (jira_id,))
        remaining_count = cursor.fetchone()[0]

        # If all test cases for this JIRA ID have been deleted, reset the test case counter in helpers.py
        if remaining_count == 0:
            # Import the helper functions to reset the test case counter
            try:
                from helpers import reset_test_case_counter
                reset_test_case_counter()
                print(f"Reset test case counter for JIRA ID: {jira_id}. Next test case will start from TC_001.")
            except ImportError:
                print("Could not import helpers.reset_test_case_counter. Test case counter not reset.")

        conn.commit()
        print(f"Cleared test cases for JIRA ID {jira_id}" + (f" and test type {test_type}" if test_type else ""))
        return True
    except sqlite3.Error as e:
        print(f"Error clearing test cases: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_or_create_jira_issue(database_path, jira_id, summary=None, description=None, status=None, test_run_id=None):
    """Gets or creates a JIRA issue in the database.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        summary: JIRA issue summary
        description: JIRA issue description
        status: JIRA issue status (e.g., "To Do", "In Progress", "Done")
        test_run_id: ID of the test run this JIRA issue is associated with

    Returns:
        The JIRA issue ID in the database
    """
    # Create a dedicated connection for this function
    conn = None
    try:
        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        conn.execute("PRAGMA busy_timeout=60000")  # 60 second timeout
        cursor = conn.cursor()

        # Check if the JIRA issue already exists
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if result:
            # Update the summary, description, status, and test_run_id if provided
            update_fields = []
            update_values = []

            if summary:
                update_fields.append("summary = ?")
                update_values.append(summary)

            if description:
                update_fields.append("description = ?")
                update_values.append(description)

            if status:
                update_fields.append("status = ?")
                update_values.append(status)

            if test_run_id:
                update_fields.append("test_run_id = ?")
                update_values.append(test_run_id)

            if update_fields:
                update_sql = f"UPDATE jira_issues SET {', '.join(update_fields)} WHERE id = ?"
                update_values.append(result[0])
                cursor.execute(update_sql, update_values)
                conn.commit()

            return result[0]
        else:
            # If test_run_id is not provided, try to find it from the test_runs table
            if not test_run_id:
                cursor.execute("SELECT id FROM test_runs WHERE jira_id = ? ORDER BY id DESC LIMIT 1", (jira_id,))
                test_run_result = cursor.fetchone()
                if test_run_result:
                    test_run_id = test_run_result[0]

            # Create a new JIRA issue
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status, test_run_id) VALUES (?, ?, ?, ?, ?)",
                (jira_id, summary or "", description or "", status or "", test_run_id)
            )
            conn.commit()
            return cursor.lastrowid
    except sqlite3.Error as e:
        print(f"Error getting or creating JIRA issue: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        raise
    finally:
        # Always close the connection
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

# The find_gaps_in_test_case_ids function has been removed as it's not needed
# We're now focusing on continuous numbering from the highest existing ID

@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_highest_test_case_id_number(database_path, jira_id, dashboard_test_type=None):
    """Gets the highest test case ID number for a specific JIRA ID.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        dashboard_test_type: Dashboard test type (e.g., "positive", "negative", "all")
                            If "all" or None, returns the highest ID across all test types
                            If a specific type, returns the highest ID for that type only

    Returns:
        The highest test case ID number, or 0 if no test cases found
    """
    # Create a dedicated connection for this function
    conn = None
    try:
        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        conn.execute("PRAGMA busy_timeout=60000")  # 60 second timeout
        cursor = conn.cursor()

        # Get or create the JIRA issue ID from the database
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if not result:
            print(f"No JIRA issue found for {jira_id}, creating one")
            # Create a new JIRA issue
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status) VALUES (?, ?, ?, ?)",
                (jira_id, "", "", "")
            )
            # Commit immediately to ensure the JIRA issue is created
            conn.commit()
            print(f"Committed JIRA issue creation")

            # Get the ID of the newly created JIRA issue
            cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
            result = cursor.fetchone()
            if not result:
                print(f"Failed to create JIRA issue for {jira_id}")
                return 0

        db_jira_id = result[0]
        print(f"Using JIRA issue with ID {db_jira_id} for {jira_id}")

        # Always get the highest test case ID across all test types for this JIRA ID
        # This ensures that test case IDs are continuous across all test types for a given JIRA ID
        print(f"Getting highest test case ID across all test types for {jira_id}")
        cursor.execute(
            """SELECT DISTINCT test_case_id
               FROM test_cases
               WHERE jira_id = ?""",
            (jira_id,)
        )

        test_case_ids = cursor.fetchall()
        if not test_case_ids:
            return 0

        # Extract the highest test case ID number
        highest_id = 0
        print(f"Found {len(test_case_ids)} test case IDs for {jira_id}")
        for row in test_case_ids:
            tc_id = row[0]
            print(f"Processing test case ID: {tc_id}")
            if isinstance(tc_id, str) and tc_id.startswith("TC_"):
                try:
                    id_num = int(tc_id.split("_")[1])
                    highest_id = max(highest_id, id_num)
                    print(f"Extracted ID number: {id_num}, current highest: {highest_id}")
                except (ValueError, IndexError) as e:
                    print(f"Error extracting ID number from {tc_id}: {e}")
                    pass

        # We're no longer checking for gaps in the sequence
        # Just return the highest ID found

        print(f"Highest test case ID number for {jira_id} across all test types: {highest_id}")
        return highest_id
    except sqlite3.Error as e:
        print(f"Error getting highest test case ID: {e}")
        return 0
    finally:
        # Always close the connection
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def save_test_cases_to_database(database_path, jira_id, test_cases_df, test_type, user_name=None, test_run_id=None, is_edited=False):
    """Saves test cases to the database.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        test_cases_df: DataFrame containing test cases
        test_type: Test type (e.g., "positive", "negative", "all")
        user_name: Name of the user who created the test cases
        test_run_id: ID of the test run to associate with these test cases
        is_edited: Whether these test cases are edited versions of existing test cases

    Returns:
        True if successful, False otherwise
    """
    # Initialize the database if it doesn't exist
    if not os.path.exists(database_path):
        init_db(database_path)

    # Create a dedicated connection for this function
    conn = None
    try:
        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        conn.execute("PRAGMA busy_timeout=60000")  # 60 second timeout
        cursor = conn.cursor()

        # Get or create the JIRA issue
        # Use a direct query instead of calling another function that might close the connection
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if result:
            db_jira_id = result[0]
            print(f"Found existing JIRA issue with ID {db_jira_id} for {jira_id}")
        else:
            # Create a new JIRA issue
            print(f"Creating new JIRA issue for {jira_id}")
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status) VALUES (?, ?, ?, ?)",
                (jira_id, "", "", "")
            )
            # Commit immediately to ensure the JIRA issue is created
            conn.commit()
            print(f"Committed JIRA issue creation")

            # Get the ID of the newly created JIRA issue
            cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
            result = cursor.fetchone()
            if result:
                db_jira_id = result[0]
                print(f"Created new JIRA issue with ID {db_jira_id} for {jira_id}")
            else:
                raise ValueError(f"Failed to create JIRA issue for {jira_id}")

        # Check if a transaction is already in progress
        cursor.execute("SELECT * FROM sqlite_master LIMIT 1")
        in_transaction = conn.in_transaction

        # Begin transaction only if not already in a transaction
        if not in_transaction:
            cursor.execute("BEGIN TRANSACTION;")
            print("Started new transaction")
        else:
            print("Using existing transaction")

        # Check if a test run already exists for this JIRA ID and test type with the same timestamp
        current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # If a test_run_id was provided, use it
        if test_run_id:
            # Verify that the test run exists and belongs to this JIRA ID
            cursor.execute(
                "SELECT id FROM test_runs WHERE id = ? AND jira_id = ?",
                (test_run_id, jira_id)  # Use the actual JIRA ID, not the database ID
            )
            existing_test_run = cursor.fetchone()

            if existing_test_run:
                print(f"Using provided test run with ID {test_run_id} for {jira_id} ({test_type})")
                # Update the test run status to in_progress
                cursor.execute(
                    "UPDATE test_runs SET status = ? WHERE id = ?",
                    ("in_progress", test_run_id)
                )
            else:
                # If the provided test_run_id doesn't exist or doesn't belong to this JIRA ID,
                # we'll create a new one
                test_run_id = None
                print(f"Provided test run ID {test_run_id} not found or doesn't belong to {jira_id}, creating a new one")

        # If no test_run_id was provided or the provided one was invalid, check for a recent test run
        if not test_run_id:
            cursor.execute(
                """SELECT id FROM test_runs
                   WHERE jira_id = ? AND test_type = ?
                   AND datetime(timestamp) > datetime('now', '-1 minute')
                   ORDER BY timestamp DESC LIMIT 1""",
                (jira_id, test_type.lower())  # Use the actual JIRA ID, not the database ID
            )
            existing_test_run = cursor.fetchone()

            if existing_test_run:
                test_run_id = existing_test_run[0]
                print(f"Using existing test run with ID {test_run_id} for {jira_id} ({test_type})")
                # Update the test run status to in_progress
                cursor.execute(
                    "UPDATE test_runs SET status = ? WHERE id = ?",
                    ("in_progress", test_run_id)
                )
            else:
                # Create a test run record
                # For the "all" test type, we need to handle it differently
                # We'll set num_test_cases to 20 (5 test cases for each of the 4 test types)
                if test_type.lower() == "all":
                    num_test_cases = 20  # 5 test cases for each of the 4 test types
                else:
                    num_test_cases = len(test_cases_df["Test Case ID"].unique())

                cursor.execute(
                    "INSERT INTO test_runs (jira_id, test_type, timestamp, num_test_cases, status, user_name) VALUES (?, ?, ?, ?, ?, ?)",
                    (jira_id, test_type.lower(), current_timestamp, num_test_cases, "in_progress", user_name)  # Use the actual JIRA ID
                )
                test_run_id = cursor.lastrowid
                print(f"Created new test run with ID {test_run_id} for {jira_id} ({test_type})")

        # Process each test case
        # Group rows by Test Case ID to ensure steps are associated with the correct test case
        # First, create a dictionary to map Test Case IDs to their database IDs
        test_case_db_ids = {}
        current_test_case_id = None

        # First pass: Insert all test cases
        for _, row in test_cases_df.iterrows():
            tc_id = row.get("Test Case ID")

            # If this is a new test case (has a Test Case ID)
            if pd.notna(tc_id) and tc_id and tc_id not in test_case_db_ids:
                # Check if this test case ID already exists in the database
                cursor.execute(
                    "SELECT id FROM test_cases WHERE jira_id = ? AND test_case_id = ? AND dashboard_test_type = ?",
                    (db_jira_id, tc_id, test_type.lower())
                )
                existing_tc = cursor.fetchone()

                if existing_tc and not is_edited:
                    # If this test case ID already exists and we're not editing, skip it
                    print(f"Test case ID {tc_id} already exists in the database. Skipping.")
                    test_case_db_ids[tc_id] = existing_tc[0]
                    continue

                # If we're editing or the test case doesn't exist, insert/update it
                if existing_tc and is_edited:
                    # Update the existing test case
                    cursor.execute('''
                        UPDATE test_cases SET
                            test_case_objective = ?,
                            prerequisite = ?,
                            priority = ?,
                            test_type = ?,
                            test_group = ?,
                            project = ?,
                            feature = ?,
                            timestamp = ?,
                            is_latest = ?,
                            dashboard_test_type = ?,
                            user_name = ?,
                            test_run_id = ?,
                            is_edited = ?
                        WHERE id = ?
                    ''', (
                        row.get("Test Case Objective", ""),
                        row.get("Prerequisite", ""),
                        row.get("Priority", ""),
                        row.get("Test Type", ""),
                        row.get("Test Group", ""),
                        row.get("Project", ""),
                        row.get("Feature", ""),
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        1,
                        test_type.lower(),
                        user_name,
                        test_run_id,
                        is_edited,
                        existing_tc[0]
                    ))
                    test_case_db_ids[tc_id] = existing_tc[0]
                    print(f"Updated existing test case {tc_id} in the database.")
                else:
                    # Insert the test case
                    cursor.execute('''
                        INSERT INTO test_cases (
                            jira_id, test_case_id, test_case_objective, prerequisite,
                            priority, test_type, test_group, project, feature,
                            timestamp, is_latest, dashboard_test_type, user_name, test_run_id, is_edited, jira_issue_id
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        jira_id,  # Use the actual JIRA ID, not the database ID
                        tc_id,
                        row.get("Test Case Objective", ""),
                        row.get("Prerequisite", ""),
                        row.get("Priority", ""),
                        row.get("Test Type", ""),
                        row.get("Test Group", ""),
                        row.get("Project", ""),
                        row.get("Feature", ""),
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        1,
                        # Always use the test_type parameter from the function call
                        test_type.lower(),
                        user_name,
                        test_run_id,
                        is_edited,
                        db_jira_id  # Set the jira_issue_id to reference the jira_issues.id
                    ))

                    # Get the test case ID in the database and store it in our mapping
                    test_case_db_ids[tc_id] = cursor.lastrowid
                    print(f"Inserted new test case {tc_id} into the database.")

        # Second pass: Insert all test steps with the correct test case ID
        for _, row in test_cases_df.iterrows():
            tc_id = row.get("Test Case ID")

            # If this row has a Test Case ID, update our current test case tracking
            if pd.notna(tc_id) and tc_id:
                current_test_case_id = tc_id

            # If this is a test step (has a Step No) and we have a current test case
            if pd.notna(row.get("Step No")) and current_test_case_id and current_test_case_id in test_case_db_ids:
                # Get the database ID for this test case
                test_case_db_id = test_case_db_ids[current_test_case_id]

                # Insert the test step
                cursor.execute('''
                    INSERT INTO test_steps (
                        test_case_id, step_number, test_step, expected_result,
                        actual_result, test_status, defect_id, comments, dashboard_test_type, user_name
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    test_case_db_id,
                    row.get("Step No", 0),
                    row.get("Test Steps", ""),
                    row.get("Expected Result", ""),
                    row.get("Actual Result", ""),
                    row.get("Test Status", ""),
                    row.get("Defect ID", ""),
                    row.get("Comments", ""),
                    test_type.lower(),
                    user_name
                ))

        # Update the test run status to completed
        if test_run_id:
            # Count the actual number of unique test cases that were successfully inserted
            num_test_cases = len(test_case_db_ids)

            # For "all" test type, verify we have the expected number of test cases
            if test_type.lower() == "all":
                # We should have test cases for each test type (positive, negative, security, performance)
                # Count how many of each type we have
                test_type_counts = {}
                for tc_id, db_id in test_case_db_ids.items():
                    cursor.execute("SELECT test_type FROM test_cases WHERE id = ?", (db_id,))
                    result = cursor.fetchone()
                    if result and result[0]:
                        test_type = result[0].lower()
                        if test_type not in test_type_counts:
                            test_type_counts[test_type] = 0
                        test_type_counts[test_type] += 1

                # Log the counts for each test type
                print(f"Test type counts for test run {test_run_id}: {test_type_counts}")

            cursor.execute(
                "UPDATE test_runs SET status = ?, num_test_cases = ? WHERE id = ?",
                ("completed", num_test_cases, test_run_id)
            )
            print(f"Updated test run {test_run_id} status to completed with {num_test_cases} test cases")

        # Commit the transaction only if we started one
        if not in_transaction and conn.in_transaction:
            conn.commit()
            print("Committed transaction")
        elif in_transaction:
            print("Not committing transaction as it was started elsewhere")
        else:
            # If we're not in a transaction at all, commit any pending changes
            conn.commit()
            print("Committed changes (no explicit transaction)")

        print(f"Saved {len(test_cases_df[test_cases_df['Test Case ID'].notna()]['Test Case ID'].unique())} test cases to database")

        # Return the test run ID for reference
        return test_run_id
    except sqlite3.Error as e:
        print(f"Error saving test cases to database: {e}")
        # Only rollback if we started the transaction
        if conn and conn.in_transaction and not in_transaction:
            try:
                conn.rollback()
                print("Rolled back transaction")
            except Exception as rollback_error:
                print(f"Error rolling back transaction: {rollback_error}")
        elif conn and conn.in_transaction and in_transaction:
            print("Not rolling back transaction as it was started elsewhere")
        return None
    except Exception as e:
        print(f"Unexpected error saving test cases to database: {e}")
        # Only rollback if we started the transaction
        if conn and conn.in_transaction and not in_transaction:
            try:
                conn.rollback()
                print("Rolled back transaction")
            except Exception as rollback_error:
                print(f"Error rolling back transaction: {rollback_error}")
        elif conn and conn.in_transaction and in_transaction:
            print("Not rolling back transaction as it was started elsewhere")
        return None
    finally:
        # Always close the connection
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def update_jira_issue_in_database(database_path, jira_id, summary, description, status):
    """Updates the JIRA issue in the database with the actual summary and description from the JIRA API.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        summary: Summary of the JIRA issue
        description: Description of the JIRA issue
        status: Status of the JIRA issue

    Returns:
        True if the update was successful, False otherwise
    """
    # Create a dedicated connection for this function
    conn = None
    try:
        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        conn.execute("PRAGMA busy_timeout=60000")  # 60 second timeout
        cursor = conn.cursor()

        # Check if the JIRA issue exists in the database
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if result:
            # Update the existing JIRA issue
            cursor.execute(
                "UPDATE jira_issues SET summary = ?, description = ?, status = ? WHERE jira_id = ?",
                (summary, description, status, jira_id)
            )
            conn.commit()
            print(f"Updated JIRA issue {jira_id} in database")
            return True
        else:
            # Create a new JIRA issue
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status) VALUES (?, ?, ?, ?)",
                (jira_id, summary, description, status)
            )
            conn.commit()
            print(f"Created new JIRA issue {jira_id} in database")
            return True
    except sqlite3.Error as e:
        print(f"Error updating JIRA issue in database: {e}")
        return False
    finally:
        # Always close the connection
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def update_jira_issue_enhancement(database_path, jira_id, enhanced_description, enhanced_timestamp):
    """Updates the enhanced description and timestamp for a JIRA issue."""
    conn = None
    try:
        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Update enhanced fields
        cursor.execute(
            "UPDATE jira_issues SET enhanced_description = ?, enhanced_timestamp = ? WHERE jira_id = ?",
            (enhanced_description, enhanced_timestamp, jira_id)
        )
        conn.commit()
        print(f"Updated enhanced description for JIRA issue {jira_id}")
        return True
    except sqlite3.Error as e:
        print(f"Error updating enhanced description in database: {e}")
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_test_cases_from_database(database_path, jira_id, dashboard_test_type=None, user_name=None):
    """Gets test cases from the database.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        dashboard_test_type: Test type selected in the dashboard (e.g., "positive", "negative", "all")
        user_name: Name of the user who created the test cases (if None, returns all test cases)

    Returns:
        DataFrame containing test cases, prioritizing edited versions when available
    """
    # Create a dedicated connection for this function
    conn = None
    try:
        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        conn.execute("PRAGMA busy_timeout=60000")  # 60 second timeout
        cursor = conn.cursor()

        # Get or create the JIRA issue ID from the database
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if not result:
            print(f"No JIRA issue found for {jira_id}, creating one")
            # Create a new JIRA issue
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status) VALUES (?, ?, ?, ?)",
                (jira_id, "", "", "")
            )
            # Commit immediately to ensure the JIRA issue is created
            conn.commit()
            print(f"Committed JIRA issue creation")

            # Get the ID of the newly created JIRA issue
            cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
            result = cursor.fetchone()
            if not result:
                print(f"Failed to create JIRA issue for {jira_id}")
                return pd.DataFrame()

        db_jira_id = result[0]
        print(f"Using JIRA issue with ID {db_jira_id} for {jira_id}")

        # Normalize dashboard_test_type to lowercase if it exists
        normalized_test_type = dashboard_test_type.lower() if dashboard_test_type else "all"

        # First, get all test case IDs for this JIRA ID and test type
        if normalized_test_type != "all":
            if user_name:
                cursor.execute(
                    """SELECT DISTINCT test_case_id
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = ? AND user_name = ?
                       ORDER BY test_case_id""",
                    (db_jira_id, normalized_test_type, user_name)
                )
            else:
                cursor.execute(
                    """SELECT DISTINCT test_case_id
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = ?
                       ORDER BY test_case_id""",
                    (db_jira_id, normalized_test_type)
                )
        else:
            if user_name:
                cursor.execute(
                    """SELECT DISTINCT test_case_id
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = 'all' AND user_name = ?
                       ORDER BY test_case_id""",
                    (db_jira_id, user_name)
                )
            else:
                cursor.execute(
                    """SELECT DISTINCT test_case_id
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = 'all'
                       ORDER BY test_case_id""",
                    (db_jira_id,)
                )

        test_case_ids = [row[0] for row in cursor.fetchall()]

        if not test_case_ids:
            print(f"No test cases found for JIRA ID {jira_id}" + (f" and test type {normalized_test_type}" if normalized_test_type else ""))
            return pd.DataFrame()

        print(f"Found {len(test_case_ids)} unique test case IDs")

        # For each test case ID, get the edited version if it exists, otherwise get the original
        all_test_cases = []

        for tc_id in test_case_ids:
            # Check if there's an edited version of this test case
            if normalized_test_type != "all":
                if user_name:
                    cursor.execute(
                        """SELECT tc.*, ji.jira_id as jira_issue_id
                           FROM test_cases tc
                           JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                           WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = ?
                           AND tc.user_name = ? AND tc.test_case_id = ? AND tc.is_edited = 1
                           ORDER BY tc.timestamp DESC LIMIT 1""",
                        (db_jira_id, normalized_test_type, user_name, tc_id)
                    )
                else:
                    cursor.execute(
                        """SELECT tc.*, ji.jira_id as jira_issue_id
                           FROM test_cases tc
                           JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                           WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = ?
                           AND tc.test_case_id = ? AND tc.is_edited = 1
                           ORDER BY tc.timestamp DESC LIMIT 1""",
                        (db_jira_id, normalized_test_type, tc_id)
                    )
            else:
                if user_name:
                    cursor.execute(
                        """SELECT tc.*, ji.jira_id as jira_issue_id
                           FROM test_cases tc
                           JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                           WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = 'all'
                           AND tc.user_name = ? AND tc.test_case_id = ? AND tc.is_edited = 1
                           ORDER BY tc.timestamp DESC LIMIT 1""",
                        (db_jira_id, user_name, tc_id)
                    )
                else:
                    cursor.execute(
                        """SELECT tc.*, ji.jira_id as jira_issue_id
                           FROM test_cases tc
                           JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                           WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = 'all'
                           AND tc.test_case_id = ? AND tc.is_edited = 1
                           ORDER BY tc.timestamp DESC LIMIT 1""",
                        (db_jira_id, tc_id)
                    )

            edited_tc = cursor.fetchone()

            if edited_tc:
                # Use the edited version
                print(f"Using edited version of test case {tc_id}")
                all_test_cases.append(edited_tc)
            else:
                # No edited version, get the original
                if normalized_test_type != "all":
                    if user_name:
                        cursor.execute(
                            """SELECT tc.*, ji.jira_id as jira_issue_id
                               FROM test_cases tc
                               JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                               WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = ?
                               AND tc.user_name = ? AND tc.test_case_id = ? AND tc.is_edited = 0
                               ORDER BY tc.timestamp DESC LIMIT 1""",
                            (db_jira_id, normalized_test_type, user_name, tc_id)
                        )
                    else:
                        cursor.execute(
                            """SELECT tc.*, ji.jira_id as jira_issue_id
                               FROM test_cases tc
                               JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                               WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = ?
                               AND tc.test_case_id = ? AND tc.is_edited = 0
                               ORDER BY tc.timestamp DESC LIMIT 1""",
                            (db_jira_id, normalized_test_type, tc_id)
                        )
                else:
                    if user_name:
                        cursor.execute(
                            """SELECT tc.*, ji.jira_id as jira_issue_id
                               FROM test_cases tc
                               JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                               WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = 'all'
                               AND tc.user_name = ? AND tc.test_case_id = ? AND tc.is_edited = 0
                               ORDER BY tc.timestamp DESC LIMIT 1""",
                            (db_jira_id, user_name, tc_id)
                        )
                    else:
                        cursor.execute(
                            """SELECT tc.*, ji.jira_id as jira_issue_id
                               FROM test_cases tc
                               JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                               WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = 'all'
                               AND tc.test_case_id = ? AND tc.is_edited = 0
                               ORDER BY tc.timestamp DESC LIMIT 1""",
                            (db_jira_id, tc_id)
                        )

                original_tc = cursor.fetchone()
                if original_tc:
                    print(f"Using original version of test case {tc_id}")
                    all_test_cases.append(original_tc)

        if not all_test_cases:
            print(f"No test cases found after filtering for JIRA ID {jira_id}")
            return pd.DataFrame()

        # Convert to list of dictionaries for DataFrame
        test_cases_list = []

        for tc in all_test_cases:
            # Check if this is an edited test case
            is_edited = tc["is_edited"] == 1 if "is_edited" in tc else False
            print(f"Processing test case {tc['test_case_id']} (edited: {is_edited})")

            # Get test steps for this test case
            cursor.execute(
                "SELECT * FROM test_steps WHERE test_case_id = ? ORDER BY step_number",
                (tc["id"],)
            )
            steps = cursor.fetchall()

            # Remove duplicate steps by tracking step numbers we've already seen
            unique_steps = []
            seen_step_numbers = set()

            for step in steps:
                step_num = step["step_number"]
                if step_num not in seen_step_numbers:
                    seen_step_numbers.add(step_num)
                    unique_steps.append(step)
                else:
                    print(f"Skipping duplicate step {step_num} for test case {tc['test_case_id']}")

            # Use the deduplicated steps
            steps = unique_steps
            print(f"Found {len(steps)} unique steps for test case {tc['test_case_id']}")

            # For the first step, combine it with the test case header
            if steps and len(steps) > 0:
                first_step = steps[0]
                test_case_row = {
                    "Timestamp": tc["timestamp"],
                    "Project": tc["project"],
                    "Feature": tc["feature"],
                    "User Story ID": tc["jira_id"],  # Use jira_id instead of jira_issue_id
                    "Test Case ID": tc["test_case_id"],
                    "Test Case Objective": tc["test_case_objective"],
                    "Prerequisite": tc["prerequisite"],
                    "Step No": str(first_step["step_number"]),  # Include first step number
                    "Test Steps": first_step["test_step"],      # Include first test step
                    "Expected Result": first_step["expected_result"],  # Include first expected result
                    "Actual Result": first_step["actual_result"],
                    "Test Status": first_step["test_status"],
                    "Priority": tc["priority"],
                    "Defect ID": first_step["defect_id"],
                    "Comments": first_step["comments"],
                    "Test Type": tc["test_type"],
                    "Test Group": tc["test_group"]
                }
                test_cases_list.append(test_case_row)

                # Create rows for the remaining steps (starting from the second step)
                for step in steps[1:]:
                    test_cases_list.append({
                        "Timestamp": "",
                        "Project": "",
                        "Feature": "",
                        "User Story ID": "",
                        "Test Case ID": "",
                        "Test Case Objective": "",
                        "Prerequisite": "",
                        "Step No": str(step["step_number"]),
                        "Test Steps": step["test_step"],
                        "Expected Result": step["expected_result"],
                        "Actual Result": step["actual_result"],
                        "Test Status": step["test_status"],
                        "Priority": "",
                        "Defect ID": step["defect_id"],
                        "Comments": step["comments"],
                        "Test Type": "",
                        "Test Group": ""
                    })
            else:
                # If there are no steps, just add the test case header
                test_case_row = {
                    "Timestamp": tc["timestamp"],
                    "Project": tc["project"],
                    "Feature": tc["feature"],
                    "User Story ID": tc["jira_id"],  # Use jira_id instead of jira_issue_id
                    "Test Case ID": tc["test_case_id"],
                    "Test Case Objective": tc["test_case_objective"],
                    "Prerequisite": tc["prerequisite"],
                    "Step No": "",
                    "Test Steps": "",
                    "Expected Result": "",
                    "Actual Result": "",
                    "Test Status": "",
                    "Priority": tc["priority"],
                    "Defect ID": "",
                    "Comments": "",
                    "Test Type": tc["test_type"],
                    "Test Group": tc["test_group"]
                }
                test_cases_list.append(test_case_row)

        # Create DataFrame
        df = pd.DataFrame(test_cases_list)
        print(f"Created DataFrame with {len(df)} rows")
        return df
    except sqlite3.Error as e:
        print(f"Error getting test cases from database: {e}")
        return pd.DataFrame()
    finally:
        # Always close the connection
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_latest_test_run(database_path, jira_id, dashboard_test_type=None):
    """Gets the latest test run for a specific JIRA ID and test type.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        dashboard_test_type: Test type selected in the dashboard (e.g., "positive", "negative", "all")

    Returns:
        Dictionary containing test run information
    """
    # Create a dedicated connection for this function
    conn = None
    try:
        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        conn.execute("PRAGMA busy_timeout=60000")  # 60 second timeout
        cursor = conn.cursor()

        # Get or create the JIRA issue ID from the database
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if not result:
            print(f"No JIRA issue found for {jira_id}, creating one")
            # Create a new JIRA issue
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status) VALUES (?, ?, ?, ?)",
                (jira_id, "", "", "")
            )
            # Commit immediately to ensure the JIRA issue is created
            conn.commit()
            print(f"Committed JIRA issue creation")

            # Get the ID of the newly created JIRA issue
            cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
            result = cursor.fetchone()
            if not result:
                print(f"Failed to create JIRA issue for {jira_id}")
                return None

        db_jira_id = result[0]
        print(f"Using JIRA issue with ID {db_jira_id} for {jira_id}")

        # Get the latest test run for this JIRA ID and test type
        if dashboard_test_type and dashboard_test_type.lower() != "all":
            cursor.execute(
                """SELECT tr.*, ji.jira_id as jira_issue_id
                   FROM test_runs tr
                   JOIN jira_issues ji ON tr.jira_id = ji.jira_id
                   WHERE tr.jira_id = ? AND tr.test_type = ?
                   ORDER BY tr.timestamp DESC LIMIT 1""",
                (jira_id, dashboard_test_type.lower())
            )
        else:
            cursor.execute(
                """SELECT tr.*, ji.jira_id as jira_issue_id
                   FROM test_runs tr
                   JOIN jira_issues ji ON tr.jira_id = ji.jira_id
                   WHERE tr.jira_id = ? AND tr.test_type = 'all'
                   ORDER BY tr.timestamp DESC LIMIT 1""",
                (jira_id,)
            )

        test_run = cursor.fetchone()
        if not test_run:
            print(f"No test runs found for JIRA ID {jira_id}" + (f" and test type {dashboard_test_type}" if dashboard_test_type else ""))
            return None

        # Convert to dictionary
        return dict(test_run)
    except sqlite3.Error as e:
        print(f"Error getting latest test run: {e}")
        return None
    finally:
        # Always close the connection
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock()
def get_latest_generated_test_cases(database_path, jira_id, dashboard_test_type=None, user_name=None):
    """Gets only the most recently generated test cases for a specific JIRA ID and test type.
    This function specifically excludes edited test cases, showing only the originally generated ones.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        dashboard_test_type: Test type selected in the dashboard (e.g., "positive", "negative", "all")
        user_name: Name of the user who created the test cases (if None, returns all test cases)

    Returns:
        DataFrame containing only the most recently generated test cases (not edited ones)
    """
    conn = None
    try:
        # Create a dedicated connection for this function
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        conn.execute("PRAGMA busy_timeout=60000")  # 60 second timeout
        cursor = conn.cursor()

        # Get the JIRA issue ID from the database
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if not result:
            print(f"No JIRA issue found for {jira_id}")
            return pd.DataFrame()

        db_jira_id = result[0]
        print(f"Found JIRA issue with ID {db_jira_id} for {jira_id}")

        # Normalize dashboard_test_type to lowercase if it exists
        normalized_test_type = dashboard_test_type.lower() if dashboard_test_type else "all"
        print(f"Using normalized test type: {normalized_test_type}")

        # Get the latest timestamp for original (non-edited) test cases
        if normalized_test_type != "all":
            if user_name:
                # Filter by user_name if provided
                cursor.execute(
                    """SELECT MAX(timestamp) as latest_timestamp
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = ? AND user_name = ? AND is_edited = 0""",
                    (db_jira_id, normalized_test_type, user_name)
                )
            else:
                # Get all test cases if no user_name provided
                cursor.execute(
                    """SELECT MAX(timestamp) as latest_timestamp
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = ? AND is_edited = 0""",
                    (db_jira_id, normalized_test_type)
                )
        else:
            if user_name:
                # Filter by user_name if provided
                cursor.execute(
                    """SELECT MAX(timestamp) as latest_timestamp
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = 'all' AND user_name = ? AND is_edited = 0""",
                    (db_jira_id, user_name)
                )
            else:
                # Get all test cases if no user_name provided
                cursor.execute(
                    """SELECT MAX(timestamp) as latest_timestamp
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = 'all' AND is_edited = 0""",
                    (db_jira_id,)
                )

        result = cursor.fetchone()
        if not result or not result[0]:
            print(f"No original test cases found for {jira_id}" + (f" and test type {normalized_test_type}" if normalized_test_type else ""))
            return pd.DataFrame()

        latest_timestamp = result[0]
        print(f"Latest original test case timestamp: {latest_timestamp}")

        # Get the latest original (non-edited) test cases
        if normalized_test_type != "all":
            if user_name:
                # Filter by user_name if provided
                cursor.execute(
                    """SELECT tc.*, ji.jira_id as jira_issue_id
                       FROM test_cases tc
                       JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                       WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = ? AND tc.user_name = ?
                       AND tc.is_edited = 0 AND tc.timestamp = ?
                       ORDER BY tc.test_case_id""",
                    (db_jira_id, normalized_test_type, user_name, latest_timestamp)
                )
            else:
                # Get all test cases if no user_name provided
                cursor.execute(
                    """SELECT tc.*, ji.jira_id as jira_issue_id
                       FROM test_cases tc
                       JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                       WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = ?
                       AND tc.is_edited = 0 AND tc.timestamp = ?
                       ORDER BY tc.test_case_id""",
                    (db_jira_id, normalized_test_type, latest_timestamp)
                )
        else:
            if user_name:
                # Filter by user_name if provided
                cursor.execute(
                    """SELECT tc.*, ji.jira_id as jira_issue_id
                       FROM test_cases tc
                       JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                       WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = 'all' AND tc.user_name = ?
                       AND tc.is_edited = 0 AND tc.timestamp = ?
                       ORDER BY tc.test_case_id""",
                    (db_jira_id, user_name, latest_timestamp)
                )
            else:
                # Get all test cases if no user_name provided
                cursor.execute(
                    """SELECT tc.*, ji.jira_id as jira_issue_id
                       FROM test_cases tc
                       JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                       WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = 'all'
                       AND tc.is_edited = 0 AND tc.timestamp = ?
                       ORDER BY tc.test_case_id""",
                    (db_jira_id, latest_timestamp)
                )

        test_cases = cursor.fetchall()
        if not test_cases:
            print(f"No test cases found for {jira_id} with the latest timestamp")
            return pd.DataFrame()

        # Get the test case IDs from the latest test run
        test_case_ids = [row["id"] for row in test_cases]
        print(f"Found {len(test_case_ids)} test cases in the latest test run")

        # Get all test cases and steps for these test case IDs
        test_cases_list = []
        for tc in test_cases:
            # Check if this is an edited test case (should always be False in this function)
            is_edited = tc["is_edited"] == 1 if "is_edited" in tc else False
            print(f"Processing test case {tc['test_case_id']} (edited: {is_edited})")

            # Get test steps for this test case
            cursor.execute(
                """SELECT * FROM test_steps WHERE test_case_id = ? ORDER BY step_number""",
                (tc["id"],)
            )
            steps = cursor.fetchall()
            print(f"Found {len(steps)} steps for test case {tc['test_case_id']}")

            # Remove duplicate steps by tracking step numbers we've already seen
            unique_steps = []
            seen_step_numbers = set()

            for step in steps:
                step_num = step["step_number"]
                if step_num not in seen_step_numbers:
                    seen_step_numbers.add(step_num)
                    unique_steps.append(step)
                else:
                    print(f"Skipping duplicate step {step_num} for test case {tc['test_case_id']}")

            # Use the deduplicated steps
            steps = unique_steps
            print(f"Found {len(steps)} unique steps for test case {tc['test_case_id']}")

            # For the first step, combine it with the test case header
            if steps and len(steps) > 0:
                first_step = steps[0]
                test_case_row = {
                    "Timestamp": tc["timestamp"],
                    "Project": tc["project"],
                    "Feature": tc["feature"],
                    "User Story ID": tc["jira_id"],  # Use jira_id from the test_cases table
                    "Test Case ID": tc["test_case_id"],
                    "Test Case Objective": tc["test_case_objective"],
                    "Prerequisite": tc["prerequisite"],
                    "Step No": str(first_step["step_number"]),  # Include first step number
                    "Test Steps": first_step["test_step"],      # Include first test step
                    "Expected Result": first_step["expected_result"],  # Include first expected result
                    "Actual Result": first_step["actual_result"] if first_step["actual_result"] else "",
                    "Test Status": first_step["test_status"] if first_step["test_status"] else "",
                    "Priority": tc["priority"],
                    "Defect ID": first_step["defect_id"] if first_step["defect_id"] else "",
                    "Comments": first_step["comments"] if first_step["comments"] else "",
                    "Test Type": tc["test_type"],
                    "Test Group": tc["test_group"]
                }
                test_cases_list.append(test_case_row)

                # Create rows for the remaining steps (starting from the second step)
                for step in steps[1:]:
                    step_row = {
                        "Timestamp": "",
                        "Project": "",
                        "Feature": "",
                        "User Story ID": "",
                        "Test Case ID": "",
                        "Test Case Objective": "",
                        "Prerequisite": "",
                        "Step No": str(step["step_number"]),
                        "Test Steps": step["test_step"],
                        "Expected Result": step["expected_result"],
                        "Actual Result": step["actual_result"] if step["actual_result"] else "",
                        "Test Status": step["test_status"] if step["test_status"] else "",
                        "Priority": "",
                        "Defect ID": step["defect_id"] if step["defect_id"] else "",
                        "Comments": step["comments"] if step["comments"] else "",
                        "Test Type": "",
                        "Test Group": ""
                    }
                    test_cases_list.append(step_row)
            else:
                # If there are no steps, just add the test case header
                test_case_row = {
                    "Timestamp": tc["timestamp"],
                    "Project": tc["project"],
                    "Feature": tc["feature"],
                    "User Story ID": tc["jira_id"],  # Use jira_id from the test_cases table
                    "Test Case ID": tc["test_case_id"],
                    "Test Case Objective": tc["test_case_objective"],
                    "Prerequisite": tc["prerequisite"],
                    "Step No": "",
                    "Test Steps": "",
                    "Expected Result": "",
                    "Actual Result": "",
                    "Test Status": "",
                    "Priority": tc["priority"],
                    "Defect ID": "",
                    "Comments": "",
                    "Test Type": tc["test_type"],
                    "Test Group": tc["test_group"]
                }
                test_cases_list.append(test_case_row)

        # Create DataFrame
        df = pd.DataFrame(test_cases_list)
        print(f"Created DataFrame with {len(df)} rows")
        return df

    except sqlite3.Error as e:
        print(f"Error getting latest generated test cases: {e}")
        return pd.DataFrame()
    finally:
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock()
def export_test_cases_to_excel(database_path, jira_id, test_type, output_file):
    """Exports test cases from the database to an Excel file.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        test_type: Test type (e.g., "positive", "negative", "all")
        output_file: Path to the output Excel file

    Returns:
        True if successful, False otherwise
    """
    try:
        # Get test cases from the database
        df = get_test_cases_from_database(database_path, jira_id, test_type)
        if df.empty:
            print(f"No test cases found for JIRA ID {jira_id}" + (f" and test type {test_type}" if test_type else ""))
            return False

        # Ensure the output directory exists
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # Save to Excel
        df.to_excel(output_file, index=False)
        print(f"Exported test cases to {output_file}")
        return True
    except Exception as e:
        print(f"Error exporting test cases to Excel: {e}")
        return False

def backup_database(database_path):
    """Creates a backup of the database before migration."""
    if os.path.exists(database_path):
        backup_dir = os.path.join(os.path.dirname(database_path), "backups")
        os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"database_backup_{timestamp}.db")

        # Create the backup
        shutil.copy2(database_path, backup_path)
        print(f"Created database backup at: {backup_path}")
        return backup_path
    return None

def detect_schema_version(database_path):
    """Detects the current schema version of the database."""
    conn = None
    try:
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()

        # Check if the test_runs table has a jira_id column (new schema)
        cursor.execute("PRAGMA table_info(test_runs)")
        columns = [column[1] for column in cursor.fetchall()]

        if "jira_id" in columns:
            print("Detected new schema (direct JIRA ID in test_runs table)")
            return "new"
        else:
            print("Detected old schema (jira_issue_id foreign key in test_runs table)")
            return "old"
    except sqlite3.Error as e:
        print(f"Error detecting schema version: {e}")
        return "unknown"
    finally:
        if conn:
            conn.close()

def migrate_old_to_new_schema(database_path):
    """Migrates the database from the old schema to the new schema."""
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60)
        cursor = conn.cursor()

        # Enable Foreign Key support
        cursor.execute("PRAGMA foreign_keys = OFF;")  # Temporarily disable for migration

        # Start a transaction
        cursor.execute("BEGIN TRANSACTION")

        print("Creating backup tables...")
        # Step 1: Create backup tables
        cursor.execute("CREATE TABLE test_runs_backup AS SELECT * FROM test_runs")
        cursor.execute("CREATE TABLE test_cases_backup AS SELECT * FROM test_cases")

        # Step 2: Get all JIRA issues
        cursor.execute("SELECT id, jira_id FROM jira_issues")
        jira_issues = {row[0]: row[1] for row in cursor.fetchall()}

        # Step 3: Drop and recreate the test_runs table with the new schema
        print("Updating test_runs table schema...")
        cursor.execute("DROP TABLE test_runs")
        cursor.execute('''
        CREATE TABLE test_runs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_type TEXT NOT NULL,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            num_test_cases INTEGER,
            status TEXT DEFAULT 'planned',
            user_name TEXT,
            notes TEXT
        )
        ''')

        # Step 4: Migrate data from the backup table to the new table
        print("Migrating test_runs data...")
        cursor.execute("SELECT * FROM test_runs_backup")
        for row in cursor.fetchall():
            jira_issue_id = row[1]  # Assuming jira_issue_id is the second column
            jira_id = jira_issues.get(jira_issue_id, "UNKNOWN")

            # Extract values from the row, handling potential missing columns
            test_type = row[2] if len(row) > 2 else ""
            timestamp = row[3] if len(row) > 3 else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            num_test_cases = row[4] if len(row) > 4 else 0
            status = row[5] if len(row) > 5 else "planned"
            user_name = row[6] if len(row) > 6 else ""
            notes = row[7] if len(row) > 7 else ""

            cursor.execute('''
            INSERT INTO test_runs (
                id, jira_id, test_type, timestamp, num_test_cases, status, user_name, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                row[0],  # id
                jira_id,
                test_type,
                timestamp,
                num_test_cases,
                status,
                user_name,
                notes
            ))

        # Step 5: Update the test_cases table to use the actual JIRA ID
        print("Updating test_cases table...")
        cursor.execute("ALTER TABLE test_cases ADD COLUMN jira_id TEXT")

        # Step 6: Update the jira_id column in the test_cases table
        for jira_issue_id, jira_id in jira_issues.items():
            cursor.execute(
                "UPDATE test_cases SET jira_id = ? WHERE jira_issue_id = ?",
                (jira_id, jira_issue_id)
            )

        # Step 7: Create new indexes
        print("Creating new indexes...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_runs_jira_id ON test_runs (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_id ON test_cases (jira_id)")

        # Step 8: Drop the backup tables
        print("Cleaning up backup tables...")
        cursor.execute("DROP TABLE test_runs_backup")
        cursor.execute("DROP TABLE test_cases_backup")

        # Re-enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Commit the transaction
        cursor.execute("COMMIT")
        print("Schema migration completed successfully")
        return True
    except sqlite3.Error as e:
        print(f"Schema migration error: {e}")
        if conn:
            try:
                cursor.execute("ROLLBACK")
            except:
                pass
        return False
    finally:
        if conn:
            conn.close()

def verify_schema_completeness(database_path):
    """Verifies that all required tables and columns exist in the database."""
    conn = None
    try:
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()

        # Check for required tables
        required_tables = ["test_runs", "test_cases", "test_steps", "jira_issues"]
        for table in required_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if not cursor.fetchone():
                print(f"Missing required table: {table}")
                return False

        # Check for required columns in test_runs
        cursor.execute("PRAGMA table_info(test_runs)")
        test_runs_columns = [column[1] for column in cursor.fetchall()]
        required_test_runs_columns = ["id", "jira_id", "test_type", "timestamp", "num_test_cases", "status", "user_name", "notes"]
        for column in required_test_runs_columns:
            if column not in test_runs_columns:
                print(f"Missing required column in test_runs: {column}")
                return False

        # Check for required columns in test_cases
        cursor.execute("PRAGMA table_info(test_cases)")
        test_cases_columns = [column[1] for column in cursor.fetchall()]
        required_test_cases_columns = ["id", "jira_id", "test_case_id", "test_case_objective", "dashboard_test_type", "user_name"]
        for column in required_test_cases_columns:
            if column not in test_cases_columns:
                print(f"Missing required column in test_cases: {column}")
                return False

        # Check for required columns in test_steps
        cursor.execute("PRAGMA table_info(test_steps)")
        test_steps_columns = [column[1] for column in cursor.fetchall()]
        required_test_steps_columns = ["id", "test_case_id", "step_number", "test_step", "expected_result"]
        for column in required_test_steps_columns:
            if column not in test_steps_columns:
                print(f"Missing required column in test_steps: {column}")
                return False

        # Check for required columns in jira_issues
        cursor.execute("PRAGMA table_info(jira_issues)")
        jira_issues_columns = [column[1] for column in cursor.fetchall()]
        required_jira_issues_columns = ["id", "jira_id"]
        for column in required_jira_issues_columns:
            if column not in jira_issues_columns:
                print(f"Missing required column in jira_issues: {column}")
                return False

        # Check for required indexes
        required_indexes = [
            "idx_jira_issues_jira_id",
            "idx_test_runs_jira_id",
            "idx_test_cases_jira_id",
            "idx_test_cases_jira_issue_id",
            "idx_test_steps_test_case_id"
        ]

        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'")
        indexes = [row[0] for row in cursor.fetchall()]

        for index in required_indexes:
            if index not in indexes:
                print(f"Missing required index: {index}")
                # Don't fail for missing indexes, just warn
                print(f"Warning: Missing index {index} - this may affect performance but not functionality")

        print("Schema verification complete: All required tables and columns exist")
        return True
    except sqlite3.Error as e:
        print(f"Error verifying schema: {e}")
        return False
    finally:
        if conn:
            conn.close()

def standardize_schema(database_path):
    """Standardizes the database schema to ensure consistency across all installations."""
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60)
        cursor = conn.cursor()

        # Disable foreign keys temporarily
        cursor.execute("PRAGMA foreign_keys = OFF;")
        cursor.execute("BEGIN TRANSACTION")

        print("Creating backup of all tables...")
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]

        # Create backup of all tables
        for table in tables:
            cursor.execute(f"CREATE TABLE IF NOT EXISTS {table}_backup AS SELECT * FROM {table}")

        print("Recreating tables with standardized schema...")

        # Drop and recreate all tables with standardized schema
        # 1. jira_issues table
        cursor.execute("DROP TABLE IF EXISTS jira_issues")
        cursor.execute('''
        CREATE TABLE jira_issues (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT UNIQUE NOT NULL,
            summary TEXT,
            description TEXT,
            status TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            test_run_id INTEGER,
            FOREIGN KEY (test_run_id) REFERENCES test_runs(id)
        )
        ''')

        # 2. test_runs table
        cursor.execute("DROP TABLE IF EXISTS test_runs")
        cursor.execute('''
        CREATE TABLE test_runs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_type TEXT NOT NULL,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            num_test_cases INTEGER,
            status TEXT DEFAULT 'planned',
            user_name TEXT,
            notes TEXT
        )
        ''')

        # 3. test_cases table
        cursor.execute("DROP TABLE IF EXISTS test_cases")
        cursor.execute('''
        CREATE TABLE test_cases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_case_id TEXT NOT NULL,
            test_case_objective TEXT,
            prerequisite TEXT,
            priority TEXT,
            test_type TEXT,
            test_group TEXT,
            project TEXT,
            feature TEXT,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            is_latest BOOLEAN DEFAULT 1,
            dashboard_test_type TEXT,
            user_name TEXT,
            jira_issue_id INTEGER,
            is_edited BOOLEAN DEFAULT 0,
            test_run_id INTEGER,
            FOREIGN KEY (jira_issue_id) REFERENCES jira_issues(id),
            FOREIGN KEY (test_run_id) REFERENCES test_runs(id)
        )
        ''')

        # 4. test_steps table
        cursor.execute("DROP TABLE IF EXISTS test_steps")
        cursor.execute('''
        CREATE TABLE test_steps (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            test_case_id INTEGER NOT NULL,
            step_number INTEGER NOT NULL,
            test_step TEXT,
            expected_result TEXT,
            actual_result TEXT,
            test_status TEXT,
            defect_id TEXT,
            comments TEXT,
            dashboard_test_type TEXT,
            user_name TEXT,
            FOREIGN KEY (test_case_id) REFERENCES test_cases(id)
        )
        ''')

        # 5. test_case_executions table (if it exists)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_case_executions_backup'")
        if cursor.fetchone():
            cursor.execute("DROP TABLE IF EXISTS test_case_executions")
            cursor.execute('''
            CREATE TABLE test_case_executions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_run_id INTEGER NOT NULL,
                test_case_id INTEGER NOT NULL,
                execution_date TEXT DEFAULT CURRENT_TIMESTAMP,
                executed_by TEXT,
                status TEXT,
                defect_id TEXT,
                comments TEXT,
                FOREIGN KEY (test_run_id) REFERENCES test_runs(id),
                FOREIGN KEY (test_case_id) REFERENCES test_cases(id)
            )
            ''')

        # Migrate data from backup tables to new tables
        print("Migrating data to standardized tables...")

        # 1. Migrate jira_issues data
        cursor.execute("SELECT * FROM jira_issues_backup")
        for row in cursor.fetchall():
            # Adjust column count based on the actual schema
            columns = ["id", "jira_id", "summary", "description", "status", "created_at", "test_run_id"]
            values = []
            for i, col in enumerate(columns):
                if i < len(row):
                    values.append(row[i])
                else:
                    values.append(None)

            placeholders = ", ".join(["?"] * len(values))
            cursor.execute(f"INSERT OR IGNORE INTO jira_issues VALUES ({placeholders})", values)

        # 2. Migrate test_runs data
        cursor.execute("SELECT * FROM test_runs_backup")
        for row in cursor.fetchall():
            # Adjust column count based on the actual schema
            columns = ["id", "jira_id", "test_type", "timestamp", "num_test_cases", "status", "user_name", "notes"]
            values = []
            for i, col in enumerate(columns):
                if i < len(row):
                    values.append(row[i])
                else:
                    values.append(None)

            placeholders = ", ".join(["?"] * len(values))
            cursor.execute(f"INSERT OR IGNORE INTO test_runs VALUES ({placeholders})", values)

        # 3. Migrate test_cases data
        cursor.execute("SELECT * FROM test_cases_backup")
        for row in cursor.fetchall():
            # Get column names from the backup table
            cursor.execute("PRAGMA table_info(test_cases_backup)")
            backup_columns = [col[1] for col in cursor.fetchall()]

            # Create a dictionary of column values
            row_dict = {backup_columns[i]: row[i] for i in range(len(row)) if i < len(backup_columns)}

            # Insert into new table with standardized columns
            cursor.execute('''
            INSERT INTO test_cases (
                id, jira_id, test_case_id, test_case_objective, prerequisite, priority,
                test_type, test_group, project, feature, timestamp, is_latest,
                dashboard_test_type, user_name, jira_issue_id, is_edited, test_run_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                row_dict.get("id"),
                row_dict.get("jira_id"),
                row_dict.get("test_case_id", ""),
                row_dict.get("test_case_objective"),
                row_dict.get("prerequisite"),
                row_dict.get("priority"),
                row_dict.get("test_type"),
                row_dict.get("test_group"),
                row_dict.get("project"),
                row_dict.get("feature"),
                row_dict.get("timestamp", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                row_dict.get("is_latest", 1),
                row_dict.get("dashboard_test_type"),
                row_dict.get("user_name"),
                row_dict.get("jira_issue_id"),
                row_dict.get("is_edited", 0),
                row_dict.get("test_run_id")
            ))

        # 4. Migrate test_steps data
        cursor.execute("SELECT * FROM test_steps_backup")
        for row in cursor.fetchall():
            # Get column names from the backup table
            cursor.execute("PRAGMA table_info(test_steps_backup)")
            backup_columns = [col[1] for col in cursor.fetchall()]

            # Create a dictionary of column values
            row_dict = {backup_columns[i]: row[i] for i in range(len(row)) if i < len(backup_columns)}

            # Insert into new table with standardized columns
            cursor.execute('''
            INSERT INTO test_steps (
                id, test_case_id, step_number, test_step, expected_result,
                actual_result, test_status, defect_id, comments, dashboard_test_type, user_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                row_dict.get("id"),
                row_dict.get("test_case_id"),
                row_dict.get("step_number"),
                row_dict.get("test_step"),
                row_dict.get("expected_result"),
                row_dict.get("actual_result"),
                row_dict.get("test_status"),
                row_dict.get("defect_id"),
                row_dict.get("comments"),
                row_dict.get("dashboard_test_type"),
                row_dict.get("user_name")
            ))

        # 5. Migrate test_case_executions data if it exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_case_executions_backup'")
        if cursor.fetchone():
            cursor.execute("SELECT * FROM test_case_executions_backup")
            for row in cursor.fetchall():
                # Get column names from the backup table
                cursor.execute("PRAGMA table_info(test_case_executions_backup)")
                backup_columns = [col[1] for col in cursor.fetchall()]

                # Create a dictionary of column values
                row_dict = {backup_columns[i]: row[i] for i in range(len(row)) if i < len(backup_columns)}

                # Insert into new table with standardized columns
                cursor.execute('''
                INSERT INTO test_case_executions (
                    id, test_run_id, test_case_id, execution_date, executed_by,
                    status, defect_id, comments
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    row_dict.get("id"),
                    row_dict.get("test_run_id"),
                    row_dict.get("test_case_id"),
                    row_dict.get("execution_date", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                    row_dict.get("executed_by"),
                    row_dict.get("status"),
                    row_dict.get("defect_id"),
                    row_dict.get("comments")
                ))

        # Create all necessary indexes
        print("Creating standardized indexes...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_jira_issues_jira_id ON jira_issues (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_runs_jira_id ON test_runs (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_id ON test_cases (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_issue_id ON test_cases (jira_issue_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_dashboard_test_type ON test_cases (dashboard_test_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_steps_test_case_id ON test_steps (test_case_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_steps_dashboard_test_type ON test_steps (dashboard_test_type)")

        # Drop backup tables
        print("Cleaning up backup tables...")
        for table in tables:
            cursor.execute(f"DROP TABLE IF EXISTS {table}_backup")

        # Verify and add enhanced description columns
        cursor.execute("PRAGMA table_info(jira_issues)")
        cols = [row[1] for row in cursor.fetchall()]

        # Add enhanced description columns if missing
        if 'enhanced_description' not in cols:
            cursor.execute("ALTER TABLE jira_issues ADD COLUMN enhanced_description TEXT")
            print("Added enhanced_description column to jira_issues table")
        if 'enhanced_timestamp' not in cols:
            cursor.execute("ALTER TABLE jira_issues ADD COLUMN enhanced_timestamp TEXT")
            print("Added enhanced_timestamp column to jira_issues table")

        # Re-enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Commit the transaction
        cursor.execute("COMMIT")
        print("Schema standardization completed successfully")
        return True
    except sqlite3.Error as e:
        print(f"Schema standardization error: {e}")
        if conn:
            try:
                cursor.execute("ROLLBACK")
            except:
                pass
        return False
    finally:
        if conn:
            conn.close()

def verify_and_update_schema(database_path):
    """Verifies and updates the database schema if needed."""
    print(f"Verifying database schema for: {database_path}")

    # Create a backup first
    backup_path = backup_database(database_path)
    if not backup_path:
        print("No existing database found, nothing to migrate.")
        return True

    # Detect the schema version
    schema_version = detect_schema_version(database_path)

    if schema_version == "new":
        print("Database already has the latest schema. Standardizing to ensure consistency...")
        success = standardize_schema(database_path)
        if success:
            print("Schema standardization completed successfully.")
            # Verify schema completeness after standardization
            verify_schema_completeness(database_path)
            return True
        else:
            print(f"Schema standardization failed. Restoring from backup: {backup_path}")
            # Wait a moment to ensure connections are closed
            time.sleep(1)
            # Restore from backup
            shutil.copy2(backup_path, database_path)
            print("Database restored from backup.")
            return False
    elif schema_version == "old":
        print("Migrating database from old schema to new schema...")
        success = migrate_old_to_new_schema(database_path)
        if success:
            print("Migration completed successfully. Standardizing to ensure consistency...")
            standardize_success = standardize_schema(database_path)
            if standardize_success:
                print("Schema standardization completed successfully.")
                # Verify schema completeness after standardization
                verify_schema_completeness(database_path)
                return True
            else:
                print(f"Schema standardization failed. Restoring from backup: {backup_path}")
                # Wait a moment to ensure connections are closed
                time.sleep(1)
                # Restore from backup
                shutil.copy2(backup_path, database_path)
                print("Database restored from backup.")
                return False
        else:
            print(f"Migration failed. Restoring from backup: {backup_path}")
            # Wait a moment to ensure connections are closed
            time.sleep(1)
            # Restore from backup
            shutil.copy2(backup_path, database_path)
            print("Database restored from backup.")
            return False
    else:
        print("Unknown schema version. Cannot migrate.")
        return False

@retry_on_db_lock()
def update_database_schema(database_path):
    """Updates the database schema to add missing columns."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Check if user_name column exists in test_cases table
        cursor.execute("PRAGMA table_info(test_cases)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add user_name column to test_cases table if it doesn't exist
        if "user_name" not in columns:
            print("Adding user_name column to test_cases table")
            cursor.execute("ALTER TABLE test_cases ADD COLUMN user_name TEXT")

        # Add test_run_id column to test_cases table if it doesn't exist
        if "test_run_id" not in columns:
            print("Adding test_run_id column to test_cases table")
            cursor.execute("ALTER TABLE test_cases ADD COLUMN test_run_id INTEGER REFERENCES test_runs(id)")

        # Add is_edited column to test_cases table if it doesn't exist
        if "is_edited" not in columns:
            print("Adding is_edited column to test_cases table")
            cursor.execute("ALTER TABLE test_cases ADD COLUMN is_edited BOOLEAN DEFAULT 0")

        # Check if user_name column exists in test_steps table
        cursor.execute("PRAGMA table_info(test_steps)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add user_name column to test_steps table if it doesn't exist
        if "user_name" not in columns:
            print("Adding user_name column to test_steps table")
            cursor.execute("ALTER TABLE test_steps ADD COLUMN user_name TEXT")

        # Check if user_name and notes columns exist in test_runs table
        cursor.execute("PRAGMA table_info(test_runs)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add user_name column to test_runs table if it doesn't exist
        if "user_name" not in columns:
            print("Adding user_name column to test_runs table")
            cursor.execute("ALTER TABLE test_runs ADD COLUMN user_name TEXT")

        # Add notes column to test_runs table if it doesn't exist
        if "notes" not in columns:
            print("Adding notes column to test_runs table")
            cursor.execute("ALTER TABLE test_runs ADD COLUMN notes TEXT")

        # Commit the changes
        conn.commit()
        print("Database schema updated successfully")
        return True
    except sqlite3.Error as e:
        print(f"Error updating database schema: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def update_existing_test_cases(database_path, default_user="admin"):
    """Updates existing test cases to set default values for new columns."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Update test_cases table to set default user_name
        cursor.execute("UPDATE test_cases SET user_name = ? WHERE user_name IS NULL", (default_user,))

        # Update test_steps table to set default user_name
        cursor.execute("UPDATE test_steps SET user_name = ? WHERE user_name IS NULL", (default_user,))

        # Update test_runs table to set default user_name and notes
        cursor.execute("UPDATE test_runs SET user_name = ? WHERE user_name IS NULL", (default_user,))
        cursor.execute("UPDATE test_runs SET notes = 'Automatically migrated' WHERE notes IS NULL")

        # Commit the changes
        conn.commit()
        print(f"Updated existing test cases with default user: {default_user}")
        return True
    except sqlite3.Error as e:
        print(f"Error updating existing test cases: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def get_all_test_cases_for_user(database_path, user_name):
    """Gets all test cases created by a specific user.

    Args:
        database_path: Path to the SQLite database
        user_name: Username of the test case creator

    Returns:
        A list of dictionaries containing test case data
    """
    conn = None
    try:
        # Create a new connection directly (not using thread-local)
        conn = sqlite3.connect(database_path, timeout=60)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get all test cases for this user
        query = """
        SELECT
            tc.id as test_case_db_id,
            tc.jira_id as "User Story ID",
            tc.test_case_id as "Test Case ID",
            tc.test_case_objective as "Test Case Objective",
            tc.prerequisite as "Prerequisite",
            tc.priority as "Priority",
            tc.test_type as "Test Type",
            tc.test_group as "Test Group",
            tc.project as "Project",
            tc.feature as "Feature",
            tc.timestamp as "Timestamp",
            tc.dashboard_test_type as "Dashboard Test Type",
            tc.user_name as "User Name",
            tc.is_edited as "Is Edited",
            ts.step_number as "Step No",
            ts.test_step as "Test Steps",
            ts.expected_result as "Expected Result",
            ts.actual_result as "Actual Result",
            ts.test_status as "Test Status",
            ts.defect_id as "Defect ID",
            ts.comments as "Comments",
            tr.id as "Test Run ID"
        FROM test_cases tc
        JOIN test_steps ts ON tc.id = ts.test_case_id
        JOIN test_runs tr ON tc.jira_id = tr.jira_id AND tc.dashboard_test_type = tr.test_type
        WHERE tc.user_name = ? OR tr.user_name = ?
        ORDER BY tc.test_case_id, ts.step_number
        """
        cursor.execute(query, (user_name, user_name))
        rows = cursor.fetchall()

        # Convert rows to dictionaries
        test_cases = []
        for row in rows:
            test_case = dict(row)
            test_cases.append(test_case)

        return test_cases
    except sqlite3.Error as e:
        print(f"Error getting test cases for user {user_name}: {e}")
        return []
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock()
def get_database_stats(database_path):
    """Gets statistics about the database."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Count JIRA issues
        cursor.execute("SELECT COUNT(*) FROM jira_issues")
        jira_count = cursor.fetchone()[0]

        # Count test cases and test steps
        cursor.execute("SELECT COUNT(*) FROM test_cases")
        test_case_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM test_steps")
        test_step_count = cursor.fetchone()[0]

        # Get test runs count
        cursor.execute("SELECT COUNT(*) FROM test_runs")
        test_run_count = cursor.fetchone()[0]

        # Get the database file size
        try:
            db_size = os.path.getsize(database_path)
            db_size_mb = db_size / (1024 * 1024)  # Convert to MB
        except OSError:
            db_size_mb = 0

        # Get the last modified time
        try:
            last_modified = datetime.fromtimestamp(os.path.getmtime(database_path))
        except OSError:
            last_modified = None

        # Return the statistics as a dictionary
        return {
            "status": "connected",
            "jira_issues": jira_count,
            "test_cases": test_case_count,
            "test_steps": test_step_count,
            "test_runs": test_run_count,
            "size_mb": db_size_mb,
            "last_modified": last_modified
        }
    except sqlite3.Error as e:
        print(f"Error getting database stats: {e}")
        return {
            "status": "error",
            "error": str(e)
        }
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def create_test_run(database_path, jira_id, test_type, user_name=None, notes=None):
    """Creates a new test run in the database.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        test_type: Test type (e.g., "positive", "negative", "all")
        user_name: Name of the user who created the test run
        notes: Optional notes about the test run

    Returns:
        The ID of the newly created test run, or None if an error occurred
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Create a test run record first (primary table)
        cursor.execute(
            "INSERT INTO test_runs (jira_id, test_type, user_name, notes, status) VALUES (?, ?, ?, ?, ?)",
            (jira_id, test_type.lower(), user_name, notes, "in_progress")
        )
        test_run_id = cursor.lastrowid
        print(f"Created new test run with ID {test_run_id} for {jira_id} ({test_type})")

        # Check if a JIRA issue already exists for this JIRA ID
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if not result:
            print(f"No JIRA issue found for {jira_id}, creating one")
            # Create a new JIRA issue with a reference to the test run
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status, test_run_id) VALUES (?, ?, ?, ?, ?)",
                (jira_id, "", "", "", test_run_id)
            )
            print(f"Created new JIRA issue for {jira_id} with test_run_id {test_run_id}")
        else:
            # Update the existing JIRA issue with the new test run ID
            cursor.execute(
                "UPDATE jira_issues SET test_run_id = ? WHERE jira_id = ?",
                (test_run_id, jira_id)
            )
            print(f"Updated existing JIRA issue for {jira_id} with test_run_id {test_run_id}")

        conn.commit()
        print(f"Committed test run creation")

        # Return the ID of the newly created test run
        return test_run_id
    except sqlite3.Error as e:
        print(f"Error creating test run: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return None
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def update_test_run(database_path, test_run_id, num_test_cases=None, status=None, notes=None):
    """Updates an existing test run in the database.

    Args:
        database_path: Path to the SQLite database
        test_run_id: ID of the test run to update
        num_test_cases: Number of test cases generated in this run
        status: New status for the test run (e.g., "completed", "failed")
        notes: Optional notes about the test run

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Build the update query
        update_fields = []
        update_values = []

        if num_test_cases is not None:
            update_fields.append("num_test_cases = ?")
            update_values.append(num_test_cases)

        if status:
            update_fields.append("status = ?")
            update_values.append(status)

        if notes:
            update_fields.append("notes = ?")
            update_values.append(notes)

        if not update_fields:
            return True  # Nothing to update

        # Add the test run ID to the values
        update_values.append(test_run_id)

        # Execute the update query
        cursor.execute(
            f"UPDATE test_runs SET {', '.join(update_fields)} WHERE id = ?",
            update_values
        )
        conn.commit()

        return True
    except sqlite3.Error as e:
        print(f"Error updating test run: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def get_test_runs_by_user(database_path, user_name):
    """Gets all test runs created by a specific user.

    Args:
        database_path: Path to the SQLite database
        user_name: Name of the user who created the test runs

    Returns:
        A list of test runs, or an empty list if none found
    """
    conn = None
    try:
        # Check if database exists
        if not os.path.exists(database_path):
            print(f"Database not found at {database_path}")
            return []

        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Get all test runs for the specified user
        # With the new schema, we can directly query the test_runs table
        cursor.execute(
            """
            SELECT id, jira_id, test_type, timestamp, num_test_cases, status, notes
            FROM test_runs
            WHERE user_name = ?
            ORDER BY timestamp DESC
            """,
            (user_name,)
        )

        # Convert the results to a list of dictionaries
        test_runs = []
        for row in cursor.fetchall():
            test_runs.append({
                "id": row[0],
                "jira_id": row[1],
                "test_type": row[2],
                "timestamp": row[3],
                "num_test_cases": row[4] if row[4] is not None else 0,
                "status": row[5] if row[5] is not None else "",
                "notes": row[6] if row[6] is not None else ""
            })

        return test_runs
    except sqlite3.Error as e:
        print(f"Error getting test runs by user: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error getting test runs by user: {e}")
        return []
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def delete_test_run(database_path, test_run_id):
    """Deletes a test run and all associated test cases and test steps.

    Args:
        database_path: Path to the SQLite database
        test_run_id: ID of the test run to delete

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Explicitly disable foreign key constraints for this operation
        cursor.execute("PRAGMA foreign_keys = OFF;")

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Get all test cases associated with this test run
        cursor.execute("SELECT id FROM test_cases WHERE test_run_id = ?", (test_run_id,))
        test_case_ids = [row[0] for row in cursor.fetchall()]

        # Delete all test steps associated with these test cases
        if test_case_ids:
            placeholders = ','.join(['?'] * len(test_case_ids))
            cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)
            print(f"Deleted test steps for test cases: {test_case_ids}")

        # Delete all test cases associated with this test run
        cursor.execute("DELETE FROM test_cases WHERE test_run_id = ?", (test_run_id,))
        print(f"Deleted test cases for test run: {test_run_id}")

        # Get the JIRA ID for this test run
        cursor.execute("SELECT jira_id FROM test_runs WHERE id = ?", (test_run_id,))
        result = cursor.fetchone()
        if result:
            jira_id = result[0]

            # Delete the test run
            cursor.execute("DELETE FROM test_runs WHERE id = ?", (test_run_id,))
            print(f"Deleted test run: {test_run_id}")

            # Always delete the JIRA issue regardless of test runs
            cursor.execute("DELETE FROM jira_issues WHERE jira_id = ?", (jira_id,))
            print(f"Deleted JIRA issue: {jira_id}")

        # Re-enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Commit the transaction
        conn.commit()
        print("Delete operation completed successfully")
        return True
    except sqlite3.Error as e:
        print(f"Error deleting test run: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def delete_test_case(database_path, test_case_id):
    """Deletes a test case and all associated test steps.

    Args:
        database_path: Path to the SQLite database
        test_case_id: ID of the test case to delete

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Explicitly disable foreign key constraints for this operation
        cursor.execute("PRAGMA foreign_keys = OFF;")

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Delete all test steps associated with this test case
        cursor.execute("DELETE FROM test_steps WHERE test_case_id = ?", (test_case_id,))
        print(f"Deleted test steps for test case: {test_case_id}")

        # Delete the test case
        cursor.execute("DELETE FROM test_cases WHERE id = ?", (test_case_id,))
        print(f"Deleted test case: {test_case_id}")

        # Re-enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Commit the transaction
        conn.commit()
        print("Delete operation completed successfully")
        return True
    except sqlite3.Error as e:
        print(f"Error deleting test case: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def delete_jira_issue(database_path, jira_id):
    """Deletes a JIRA issue and all associated test runs, test cases, and test steps.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID to delete

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Explicitly disable foreign key constraints for this operation
        cursor.execute("PRAGMA foreign_keys = OFF;")

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Get all test runs associated with this JIRA ID
        cursor.execute("SELECT id FROM test_runs WHERE jira_id = ?", (jira_id,))
        test_run_ids = [row[0] for row in cursor.fetchall()]

        # Delete each test run and its associated test cases and test steps
        for test_run_id in test_run_ids:
            # Get all test cases associated with this test run
            cursor.execute("SELECT id FROM test_cases WHERE test_run_id = ?", (test_run_id,))
            test_case_ids = [row[0] for row in cursor.fetchall()]

            # Delete all test steps associated with these test cases
            if test_case_ids:
                placeholders = ','.join(['?'] * len(test_case_ids))
                cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)
                print(f"Deleted test steps for test cases: {test_case_ids}")

            # Delete all test cases associated with this test run
            cursor.execute("DELETE FROM test_cases WHERE test_run_id = ?", (test_run_id,))
            print(f"Deleted test cases for test run: {test_run_id}")

        # Delete all test runs associated with this JIRA ID
        if test_run_ids:
            placeholders = ','.join(['?'] * len(test_run_ids))
            cursor.execute(f"DELETE FROM test_runs WHERE id IN ({placeholders})", test_run_ids)
            print(f"Deleted test runs: {test_run_ids}")

        # Delete the JIRA issue
        cursor.execute("DELETE FROM jira_issues WHERE jira_id = ?", (jira_id,))
        print(f"Deleted JIRA issue: {jira_id}")

        # Re-enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Commit the transaction
        conn.commit()
        print("Delete operation completed successfully")
        return True
    except sqlite3.Error as e:
        print(f"Error deleting JIRA issue: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def delete_edited_test_cases(database_path, jira_id=None, admin_password=None):
    """Deletes edited test cases (where is_edited=1) for a specific JIRA ID or all edited test cases if no JIRA ID is provided.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID to delete edited test cases for (optional)
        admin_password: Admin password for verification (required if allow_delete_test_cases is false)

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        # Check if delete operations are allowed
        if not admin_config.is_operation_allowed("delete_test_cases", admin_password):
            print("Admin password required to delete test cases")
            return False

        # Use thread-local connection
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Disable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = OFF;")

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Debug: Check existing edited test cases
        cursor.execute("SELECT id, jira_id FROM test_cases WHERE is_edited = 1;")
        edited_cases = cursor.fetchall()
        print(f"Edited test cases before deletion: {edited_cases}")

        # Get affected JIRA IDs before deletion (for later cleanup)
        affected_jira_ids = set()
        if jira_id:
            affected_jira_ids.add(jira_id)
        else:
            cursor.execute("SELECT DISTINCT jira_id FROM test_cases WHERE is_edited = 1")
            for row in cursor.fetchall():
                affected_jira_ids.add(row[0])

        print(f"Affected JIRA IDs: {affected_jira_ids}")

        # Delete test steps for edited test cases
        if jira_id:
            # Get test case IDs to delete with the specified JIRA ID
            cursor.execute(
                "SELECT id FROM test_cases WHERE jira_id = ? AND is_edited = 1",
                (jira_id,)
            )
            test_case_ids = [row[0] for row in cursor.fetchall()]

            if test_case_ids:
                # Delete test steps for these test cases
                placeholders = ", ".join(["?" for _ in test_case_ids])
                cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)
                print(f"Deleted test steps for {len(test_case_ids)} edited test cases with JIRA ID: {jira_id}")

                # Delete the test cases
                cursor.execute(f"DELETE FROM test_cases WHERE id IN ({placeholders})", test_case_ids)
                print(f"Deleted {len(test_case_ids)} edited test cases with JIRA ID: {jira_id}")
            else:
                print(f"No edited test cases found for JIRA ID: {jira_id}")
        else:
            # Get all edited test case IDs
            cursor.execute("SELECT id FROM test_cases WHERE is_edited = 1")
            test_case_ids = [row[0] for row in cursor.fetchall()]

            if test_case_ids:
                # Delete test steps for all edited test cases
                placeholders = ", ".join(["?" for _ in test_case_ids])
                cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)
                print(f"Deleted test steps for {len(test_case_ids)} edited test cases")

                # Delete all edited test cases
                cursor.execute("DELETE FROM test_cases WHERE is_edited = 1")
                print(f"Deleted {len(test_case_ids)} edited test cases")
            else:
                print("No edited test cases found")

        # For each affected JIRA ID, check if there are any test runs left
        for affected_jira_id in affected_jira_ids:
            # Check if there are any test cases left for this JIRA ID
            cursor.execute("SELECT COUNT(*) FROM test_cases WHERE jira_id = ?", (affected_jira_id,))
            test_case_count = cursor.fetchone()[0]

            if test_case_count == 0:
                # If no test cases left, delete all test runs for this JIRA ID
                cursor.execute("DELETE FROM test_runs WHERE jira_id = ?", (affected_jira_id,))
                print(f"Deleted all test runs for JIRA ID: {affected_jira_id}")

                # Delete the JIRA issue
                cursor.execute("DELETE FROM jira_issues WHERE jira_id = ?", (affected_jira_id,))
                print(f"Deleted JIRA issue: {affected_jira_id}")
            else:
                print(f"Not deleting JIRA issue {affected_jira_id} as there are still {test_case_count} test cases associated with it")

        # Debug: Check remaining edited test cases
        cursor.execute("SELECT id, jira_id FROM test_cases WHERE is_edited = 1;")
        remaining_cases = cursor.fetchall()
        print(f"Edited test cases after deletion: {remaining_cases}")

        # Re-enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Commit the transaction
        conn.commit()
        return True
    except sqlite3.Error as e:
        print(f"Error deleting edited test cases: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def delete_all_edited_test_cases(database_path, admin_password=None):
    """Deletes all edited test cases from the database.

    Args:
        database_path: Path to the SQLite database
        admin_password: Admin password for verification (passed to delete_edited_test_cases)

    Returns:
        True if successful, False otherwise
    """
    return delete_edited_test_cases(database_path, admin_password=admin_password)

@retry_on_db_lock()
def delete_specific_test_cases(database_path, start_id=539, end_id=578):
    """Deletes test cases with IDs in the specified range.

    Args:
        database_path: Path to the SQLite database
        start_id: Start ID of the range to delete (inclusive)
        end_id: End ID of the range to delete (inclusive)

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Generate the list of IDs to delete
        ids_to_delete = list(range(start_id, end_id + 1))

        # Print the IDs to delete for debugging
        print(f"Attempting to delete test cases with IDs: {ids_to_delete}")

        # Check if these IDs exist in the database
        placeholders = ','.join(['?'] * len(ids_to_delete))
        cursor.execute(
            f"""
            SELECT id, test_case_id, jira_id
            FROM test_cases
            WHERE id IN ({placeholders})
            """,
            ids_to_delete
        )

        found_test_cases = cursor.fetchall()
        found_ids = [row[0] for row in found_test_cases]

        print(f"Found {len(found_ids)} test cases with the specified IDs:")
        for case in found_test_cases:
            print(f"  ID: {case[0]}, Test Case ID: {case[1]}, JIRA ID: {case[2]}")

        if not found_ids:
            print("No test cases found with the specified IDs")
            conn.commit()
            return True

        # Delete test steps for these test cases
        placeholders = ','.join(['?'] * len(found_ids))

        # Delete test steps for these test cases
        cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", found_ids)
        print(f"Deleted test steps for {len(found_ids)} test cases")

        # Delete the test cases
        cursor.execute(f"DELETE FROM test_cases WHERE id IN ({placeholders})", found_ids)
        print(f"Deleted {len(found_ids)} test cases")

        # Commit the transaction
        conn.commit()
        print(f"Successfully deleted {len(found_ids)} test cases")
        return True
    except sqlite3.Error as e:
        print(f"Error deleting specific test cases: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            close_thread_local_connection()



def delete_by_timestamp(database_path, timestamp_pattern):
    """Directly executes SQL to delete test cases with a specific timestamp pattern.

    Args:
        database_path: Path to the SQLite database
        timestamp_pattern: Pattern to match in the timestamp field (e.g., '2025-04-28 16:30:26')

    Returns:
        True if successful, False otherwise
    """
    # This function is intentionally not decorated with @retry_on_db_lock()
    # to avoid any potential issues with the retry mechanism

    conn = None
    try:
        # Connect directly to the database without using the thread-local connection
        # This bypasses any potential issues with the thread-local connection management
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # First, check what test cases exist with the specified timestamp pattern
        cursor.execute(
            """
            SELECT id, test_case_id, jira_id, timestamp
            FROM test_cases
            WHERE timestamp LIKE ?
            """,
            (f"%{timestamp_pattern}%",)
        )

        found_test_cases = cursor.fetchall()
        print(f"Found {len(found_test_cases)} test cases with timestamp pattern '{timestamp_pattern}':")
        for case in found_test_cases:
            print(f"  ID: {case[0]}, Test Case ID: {case[1]}, JIRA ID: {case[2]}, Timestamp: {case[3]}")

        if not found_test_cases:
            print(f"No test cases found with timestamp pattern '{timestamp_pattern}'")
            conn.commit()
            return True

        # Get the IDs of the test cases to delete
        test_case_ids = [case[0] for case in found_test_cases]
        placeholders = ','.join(['?'] * len(test_case_ids))

        # Delete test steps for these test cases
        cursor.execute(
            f"""
            DELETE FROM test_steps
            WHERE test_case_id IN ({placeholders})
            """,
            test_case_ids
        )
        print(f"Deleted test steps for {len(test_case_ids)} test cases")

        # Delete the test cases
        cursor.execute(
            f"""
            DELETE FROM test_cases
            WHERE id IN ({placeholders})
            """,
            test_case_ids
        )
        print(f"Deleted {len(test_case_ids)} test cases")

        # Commit the transaction
        conn.commit()
        print(f"Successfully deleted {len(test_case_ids)} test cases with timestamp pattern '{timestamp_pattern}'")
        return True
    except sqlite3.Error as e:
        print(f"Error executing direct SQL: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

@retry_on_db_lock()
def delete_duplicate_test_cases(database_path, jira_id=None):
    """Deletes duplicate test cases for a specific JIRA ID or all duplicate test cases if no JIRA ID is provided.
    Duplicate test cases are identified as those with the same test_case_id but different timestamps.
    For each test_case_id, only the oldest entry is kept, and all newer entries are deleted.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID to delete duplicate test cases for (optional)

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Find duplicate test cases (same test_case_id but different timestamps)
        if jira_id:
            # Get all test case IDs for this JIRA ID
            cursor.execute(
                """
                SELECT test_case_id, COUNT(*) as count
                FROM test_cases
                WHERE jira_id = ?
                GROUP BY test_case_id
                HAVING count > 1
                """,
                (jira_id,)
            )
        else:
            # Get all test case IDs with duplicates
            cursor.execute(
                """
                SELECT test_case_id, COUNT(*) as count
                FROM test_cases
                GROUP BY test_case_id
                HAVING count > 1
                """
            )

        duplicate_test_case_ids = [row[0] for row in cursor.fetchall()]

        if not duplicate_test_case_ids:
            print(f"No duplicate test cases found" + (f" for {jira_id}" if jira_id else ""))
            conn.commit()
            return True

        print(f"Found {len(duplicate_test_case_ids)} test case IDs with duplicates")

        # For each duplicate test case ID, keep only the oldest entry and delete the rest
        total_deleted = 0
        for test_case_id in duplicate_test_case_ids:
            # Get all entries for this test case ID, ordered by timestamp
            if jira_id:
                cursor.execute(
                    """
                    SELECT id, timestamp
                    FROM test_cases
                    WHERE test_case_id = ? AND jira_id = ?
                    ORDER BY timestamp
                    """,
                    (test_case_id, jira_id)
                )
            else:
                cursor.execute(
                    """
                    SELECT id, timestamp
                    FROM test_cases
                    WHERE test_case_id = ?
                    ORDER BY timestamp
                    """,
                    (test_case_id,)
                )

            entries = cursor.fetchall()

            # Keep the oldest entry (first one) and delete the rest
            if len(entries) > 1:
                # Get IDs of entries to delete (all except the first one)
                ids_to_delete = [entry[0] for entry in entries[1:]]

                # Delete test steps for these test cases
                placeholders = ','.join(['?'] * len(ids_to_delete))
                cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", ids_to_delete)

                # Delete the duplicate test cases
                cursor.execute(f"DELETE FROM test_cases WHERE id IN ({placeholders})", ids_to_delete)

                total_deleted += len(ids_to_delete)
                print(f"Deleted {len(ids_to_delete)} duplicates of test case {test_case_id}")

        # Commit the transaction
        conn.commit()
        print(f"Successfully deleted {total_deleted} duplicate test cases")
        return True
    except sqlite3.Error as e:
        print(f"Error deleting duplicate test cases: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            close_thread_local_connection()

def delete_test_cases_by_time_range(database_path, start_datetime, end_datetime):
    """Deletes test cases created within a specific time range.

    Args:
        database_path: Path to the SQLite database
        start_datetime: Start datetime for the range
        end_datetime: End datetime for the range

    Returns:
        True if successful, False otherwise
    """
    # This function is intentionally not decorated with @retry_on_db_lock()
    # to avoid any potential issues with the retry mechanism

    conn = None
    try:
        # Connect directly to the database without using the thread-local connection
        # This bypasses any potential issues with the thread-local connection management
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Format datetimes as strings for SQLite
        start_str = start_datetime.strftime("%Y-%m-%d %H:%M:%S")
        end_str = end_datetime.strftime("%Y-%m-%d %H:%M:%S")

        # Get all test cases created within the time range
        # Use date() function to handle different timestamp formats
        cursor.execute(
            """SELECT id, test_case_id, jira_id, timestamp FROM test_cases
            WHERE date(timestamp) BETWEEN date(?) AND date(?)""",
            (start_str, end_str)
        )

        found_test_cases = cursor.fetchall()
        print(f"Found {len(found_test_cases)} test cases between {start_str} and {end_str}:")
        for case in found_test_cases:
            print(f"  ID: {case[0]}, Test Case ID: {case[1]}, JIRA ID: {case[2]}, Timestamp: {case[3]}")

        test_case_ids = [row[0] for row in found_test_cases]

        if not test_case_ids:
            print(f"No test cases found between {start_str} and {end_str}")
            conn.commit()
            return True

        # Delete all test steps associated with these test cases
        placeholders = ','.join(['?'] * len(test_case_ids))
        cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({placeholders})", test_case_ids)
        print(f"Deleted test steps for {len(test_case_ids)} test cases")

        # Delete the test cases
        cursor.execute(f"DELETE FROM test_cases WHERE id IN ({placeholders})", test_case_ids)
        print(f"Deleted {len(test_case_ids)} test cases created between {start_str} and {end_str}")

        # Commit the transaction
        conn.commit()
        print("Delete operation completed successfully")
        return True
    except sqlite3.Error as e:
        print(f"Error deleting test cases by time range: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

@retry_on_db_lock()
def delete_all_jira_issues(database_path, admin_password=None):
    """Deletes all JIRA issues from the database.

    Args:
        database_path: Path to the SQLite database
        admin_password: Admin password for verification

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        # Check if admin operations are allowed
        if not admin_config.is_operation_allowed("delete_jira_issues", admin_password):
            print("Admin password required to delete all JIRA issues")
            return False

        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Explicitly disable foreign key constraints for this operation
        cursor.execute("PRAGMA foreign_keys = OFF;")

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Get count before deletion
        cursor.execute("SELECT COUNT(*) FROM jira_issues")
        count_before = cursor.fetchone()[0]
        print(f"Found {count_before} JIRA issues to delete")

        # Delete all JIRA issues
        cursor.execute("DELETE FROM jira_issues")

        # Reset the auto-increment counter
        cursor.execute("DELETE FROM sqlite_sequence WHERE name = 'jira_issues'")

        # Re-enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Commit the transaction
        conn.commit()

        # Verify deletion
        cursor.execute("SELECT COUNT(*) FROM jira_issues")
        count_after = cursor.fetchone()[0]

        print(f"Deleted {count_before} JIRA issues. Remaining: {count_after}")
        return True
    except sqlite3.Error as e:
        print(f"Error deleting JIRA issues: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def get_test_cases_by_test_run(database_path, test_run_id):
    """Gets all test cases associated with a specific test run.

    Args:
        database_path: Path to the SQLite database
        test_run_id: ID of the test run

    Returns:
        A DataFrame containing all test cases and steps for the test run, or an empty DataFrame if none found
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # First, check if the test run exists
        cursor.execute(
            """
            SELECT id, jira_id, test_type FROM test_runs WHERE id = ?
            """,
            (test_run_id,)
        )

        test_run = cursor.fetchone()
        if not test_run:
            print(f"Test run with ID {test_run_id} not found")
            return pd.DataFrame()

        jira_id = test_run[1]
        test_type = test_run[2]

        print(f"Getting test cases for test run {test_run_id} (JIRA ID: {jira_id}, Test Type: {test_type})")

        # Get all test cases and steps for this test run
        # This query joins test_cases, test_steps, and test_runs to get all the data we need
        query = """
        SELECT
            tc.timestamp as "Timestamp",
            tc.project as "Project",
            tc.feature as "Feature",
            tc.jira_id as "User Story ID",
            tc.test_case_id as "Test Case ID",
            tc.test_case_objective as "Test Case Objective",
            tc.prerequisite as "Prerequisite",
            ts.step_number as "Step No",
            ts.test_step as "Test Steps",
            ts.expected_result as "Expected Result",
            ts.actual_result as "Actual Result",
            ts.test_status as "Test Status",
            tc.priority as "Priority",
            ts.defect_id as "Defect ID",
            ts.comments as "Comments",
            tc.test_type as "Test Type",
            tc.test_group as "Test Group",
            tc.user_name as "User Name",
            tc.dashboard_test_type as "Dashboard Test Type",
            tr.id as "Test Run ID",
            tc.is_edited as "Is Edited"
        FROM test_cases tc
        JOIN test_runs tr ON tc.jira_id = tr.jira_id AND tc.dashboard_test_type = tr.test_type
        LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
        WHERE tr.id = ?
        ORDER BY tc.test_case_id, ts.step_number
        """

        cursor.execute(query, (test_run_id,))

        # Convert the results to a DataFrame
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()

        if not rows:
            print(f"No test cases found for test run {test_run_id}")
            return pd.DataFrame()

        # Create a DataFrame from the results
        df = pd.DataFrame(rows, columns=columns)

        # Convert any None values to empty strings
        df = df.fillna("")

        return df
    except sqlite3.Error as e:
        print(f"Error getting test cases by test run: {e}")
        return pd.DataFrame()
    finally:
        if conn:
            close_thread_local_connection()

@retry_on_db_lock()
def count_test_cases_for_test_run(database_path, test_run_id):
    """Counts the number of test cases associated with a specific test run.

    Args:
        database_path: Path to the SQLite database
        test_run_id: ID of the test run

    Returns:
        The number of test cases for the test run
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Count the number of unique test case IDs for this test run
        cursor.execute(
            """
            SELECT COUNT(DISTINCT test_case_id)
            FROM test_cases
            WHERE test_run_id = ?
            """,
            (test_run_id,)
        )

        count = cursor.fetchone()[0]
        return count
    except sqlite3.Error as e:
        print(f"Error counting test cases for test run: {e}")
        return 0
    finally:
        if conn:
            close_thread_local_connection()

# Main function for initializing the database and testing
if __name__ == "__main__":
    # Initialize the database
    print("Initializing database...")
    init_db(DATABASE_PATH)

    # Get database stats
    print("\nDatabase statistics:")
    stats = get_database_stats(DATABASE_PATH)
    for key, value in stats.items():
        print(f"{key}: {value}")

    # Example: Clear database for a specific JIRA ID
    # clear_database_for_jira(DATABASE_PATH, "TP-1", "positive")

    print("\nDatabase setup complete.")

# Function to close the database connection
def close_connection():
    """Closes the database connection for the current thread.
    This is a wrapper around close_thread_local_connection for easier access.
    """
    close_thread_local_connection()
    print("Database connection closed successfully")

# Function to run when this script is executed directly
def main():
    """Main function to run when this script is executed directly."""
    print("Initializing database...")
    init_db(DATABASE_PATH)
    # Update the database schema to add any missing columns
    update_database_schema(DATABASE_PATH)
    # Update existing test cases with default values
    update_existing_test_cases(DATABASE_PATH)
    print("Database initialization complete.")

# Run the main function when this script is executed directly
if __name__ == "__main__":
    main()

@retry_on_db_lock()
def get_latest_test_run_id(database_path, jira_id, test_type=None, user_name=None):
    """Gets the latest test run ID for a specific JIRA ID and test type.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        test_type: Test type (e.g., "positive", "negative", "all")
        user_name: Name of the user who created the test run (if None, returns the latest test run regardless of user)

    Returns:
        The latest test run ID, or None if no test runs found
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Normalize the test type
        normalized_test_type = test_type.lower() if test_type else "all"

        # Get the latest test run for this JIRA ID and test type
        if normalized_test_type != "all":
            if user_name:
                cursor.execute(
                    """SELECT id
                       FROM test_runs
                       WHERE jira_id = ? AND test_type = ? AND user_name = ?
                       ORDER BY timestamp DESC LIMIT 1""",
                    (jira_id, normalized_test_type, user_name)
                )
            else:
                cursor.execute(
                    """SELECT id
                       FROM test_runs
                       WHERE jira_id = ? AND test_type = ?
                       ORDER BY timestamp DESC LIMIT 1""",
                    (jira_id, normalized_test_type)
                )
        else:
            if user_name:
                cursor.execute(
                    """SELECT id
                       FROM test_runs
                       WHERE jira_id = ? AND test_type = 'all' AND user_name = ?
                       ORDER BY timestamp DESC LIMIT 1""",
                    (jira_id, user_name)
                )
            else:
                cursor.execute(
                    """SELECT id
                       FROM test_runs
                       WHERE jira_id = ? AND test_type = 'all'
                       ORDER BY timestamp DESC LIMIT 1""",
                    (jira_id,)
                )

        result = cursor.fetchone()
        if not result:
            print(f"No test runs found for JIRA ID {jira_id}" + (f" and test type {normalized_test_type}" if normalized_test_type else ""))
            return None

        test_run_id = result[0]
        print(f"Latest test run ID for {jira_id} ({normalized_test_type}): {test_run_id}")
        return test_run_id
    except sqlite3.Error as e:
        print(f"Error getting latest test run ID: {e}")
        return None
    finally:
        # No need to close the connection as it's managed by get_thread_local_connection
        pass

@retry_on_db_lock()
def get_test_cases_for_test_run(database_path, test_run_id):
    """Gets all test cases for a specific test run.

    Args:
        database_path: Path to the SQLite database
        test_run_id: ID of the test run

    Returns:
        A DataFrame containing all test cases and steps for the test run, or an empty DataFrame if none found
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # First, check if the test run exists
        cursor.execute(
            """
            SELECT id, jira_id, test_type FROM test_runs WHERE id = ?
            """,
            (test_run_id,)
        )

        test_run = cursor.fetchone()
        if not test_run:
            print(f"Test run with ID {test_run_id} not found")
            return pd.DataFrame()

        jira_id = test_run[1]
        test_type = test_run[2]

        print(f"Getting test cases for test run {test_run_id} (JIRA ID: {jira_id}, Test Type: {test_type})")

        # First, check if any test cases are directly associated with this test run
        cursor.execute(
            """
            SELECT COUNT(*) FROM test_cases WHERE test_run_id = ?
            """,
            (test_run_id,)
        )
        direct_count = cursor.fetchone()[0]
        print(f"Found {direct_count} test cases directly associated with test run {test_run_id}")

        # Get all test cases for this test run
        # For "all" test runs, we need to get all test cases associated with the test_run_id
        # regardless of their individual dashboard_test_type
        if test_type == "all":
            query = """
            SELECT
                tc.id,
                tc.test_case_id,
                tc.test_case_objective,
                tc.feature,
                tc.prerequisite AS prerequisites,
                tc.test_group,
                tc.priority,
                '' AS test_status,
                '' AS notes,
                tc.dashboard_test_type,
                tc.jira_id,
                tc.timestamp,
                tc.user_name,
                'header' AS row_type,
                ts.step_number,
                ts.test_step AS step_description,
                ts.expected_result,
                tc.is_edited,
                tc.test_run_id
            FROM test_cases tc
            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
            WHERE tc.test_run_id = ?
            ORDER BY tc.test_case_id, ts.step_number
            """
            cursor.execute(query, (test_run_id,))
        else:
            query = """
            SELECT
                tc.id,
                tc.test_case_id,
                tc.test_case_objective,
                tc.feature,
                tc.prerequisite AS prerequisites,
                tc.test_group,
                tc.priority,
                '' AS test_status,
                '' AS notes,
                tc.dashboard_test_type,
                tc.jira_id,
                tc.timestamp,
                tc.user_name,
                'header' AS row_type,
                ts.step_number,
                ts.test_step AS step_description,
                ts.expected_result,
                tc.is_edited,
                tc.test_run_id
            FROM test_cases tc
            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
            WHERE tc.test_run_id = ? OR (tc.jira_id = ? AND tc.dashboard_test_type = ?)
            ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number
            """
            cursor.execute(query, (test_run_id, jira_id, test_type))

        # Convert the results to a DataFrame
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()

        if not rows:
            print(f"No test cases found for test run {test_run_id}")
            return pd.DataFrame()

        # Convert to DataFrame
        df = pd.DataFrame(rows, columns=columns)
        print(f"Found {len(df)} rows for test run {test_run_id}")
        return df
    except sqlite3.Error as e:
        print(f"Error getting test cases for test run: {e}")
        return pd.DataFrame()
    finally:
        # No need to close the connection as it's managed by get_thread_local_connection
        pass

@retry_on_db_lock()
def update_test_cases_in_database(database_path, df, jira_id, test_type, user_name, is_edited=True):
    """Updates test cases in the database with edited data from a DataFrame.

    Args:
        database_path: Path to the SQLite database
        df: DataFrame containing the edited test cases
        jira_id: JIRA ID (e.g., "TP-1")
        test_type: Test type (e.g., "positive", "negative", "all")
        user_name: Name of the user who edited the test cases
        is_edited: Whether the test cases are marked as edited (default: True)

    Returns:
        A tuple of (success, message)
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Get the JIRA issue ID from the database
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()
        if not result:
            # Create a new JIRA issue if it doesn't exist
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status, created_at) VALUES (?, ?, ?, ?, ?)",
                (jira_id, f"Issue {jira_id}", "", "Open", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            )
            conn.commit()
            cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
            result = cursor.fetchone()

        jira_issue_id = result[0]
        print(f"Using JIRA issue with ID {jira_issue_id} for {jira_id}")

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Track the number of updated test cases
        updated_count = 0

        # Process each row in the DataFrame
        for _, row in df.iterrows():
            # Skip rows without a test case ID
            if "Test Case ID" not in row or not row["Test Case ID"] or pd.isna(row["Test Case ID"]):
                continue

            test_case_id = row["Test Case ID"]

            # Check if this test case already exists
            cursor.execute(
                """SELECT id FROM test_cases
                   WHERE test_case_id = ? AND jira_issue_id = ? AND dashboard_test_type = ?""",
                (test_case_id, jira_issue_id, test_type)
            )
            existing_tc = cursor.fetchone()

            if existing_tc:
                # Update existing test case
                tc_id = existing_tc[0]

                # Update the test case with the edited values
                update_fields = []
                update_values = []

                # Map DataFrame columns to database columns
                column_mapping = {
                    "Test Case Objective": "test_case_objective",
                    "Feature": "feature",
                    "Prerequisite": "prerequisite",
                    "Test Group": "test_group",
                    "Priority": "priority",
                    "Notes": "notes"
                }

                for df_col, db_col in column_mapping.items():
                    if df_col in row and not pd.isna(row[df_col]):
                        update_fields.append(f"{db_col} = ?")
                        update_values.append(row[df_col])

                # Only update if there are fields to update
                if update_fields:
                    # Add is_edited flag and timestamp
                    update_fields.append("is_edited = ?")
                    update_values.append(1 if is_edited else 0)
                    update_fields.append("timestamp = ?")
                    update_values.append(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

                    # Add user_name if provided
                    if user_name:
                        update_fields.append("user_name = ?")
                        update_values.append(user_name)

                    # Create the SQL query
                    query = f"""UPDATE test_cases SET {', '.join(update_fields)} WHERE id = ?"""
                    update_values.append(tc_id)

                    # Execute the update
                    cursor.execute(query, update_values)
                    updated_count += 1

                # Check if there are test steps to update
                if "Step No" in row and not pd.isna(row["Step No"]):
                    step_number = int(row["Step No"]) if not pd.isna(row["Step No"]) else None
                    step_description = row["Test Steps"] if "Test Steps" in row and not pd.isna(row["Test Steps"]) else ""
                    expected_result = row["Expected Result"] if "Expected Result" in row and not pd.isna(row["Expected Result"]) else ""
                    test_status = row["Test Status"] if "Test Status" in row and not pd.isna(row["Test Status"]) else ""
                    actual_result = row["Actual Result"] if "Actual Result" in row and not pd.isna(row["Actual Result"]) else ""
                    defect_id = row["Defect ID"] if "Defect ID" in row and not pd.isna(row["Defect ID"]) else ""
                    comments = row["Comments"] if "Comments" in row and not pd.isna(row["Comments"]) else ""

                    if step_number is not None:
                        # Check if this step already exists
                        cursor.execute(
                            """SELECT id FROM test_steps
                               WHERE test_case_id = ? AND step_number = ?""",
                            (tc_id, step_number)
                        )
                        existing_step = cursor.fetchone()

                        if existing_step:
                            # Update existing step
                            step_id = existing_step[0]
                            cursor.execute(
                                """UPDATE test_steps SET test_step = ?, expected_result = ?,
                                   test_status = ?, actual_result = ?, defect_id = ?, comments = ?
                                   WHERE id = ?""",
                                (step_description, expected_result, test_status, actual_result, defect_id, comments, step_id)
                            )
                        else:
                            # Insert new step
                            cursor.execute(
                                """INSERT INTO test_steps (test_case_id, step_number, test_step, expected_result,
                                   test_status, actual_result, defect_id, comments)
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                                (tc_id, step_number, step_description, expected_result,
                                 test_status, actual_result, defect_id, comments)
                            )
            else:
                # This is a new test case, insert it
                # First, get the test run ID if available
                test_run_id = None
                cursor.execute(
                    """SELECT id FROM test_runs
                       WHERE jira_id = ? AND test_type = ?
                       ORDER BY timestamp DESC LIMIT 1""",
                    (jira_id, test_type)
                )
                test_run_result = cursor.fetchone()
                if test_run_result:
                    test_run_id = test_run_result[0]

                # Insert the new test case
                cursor.execute(
                    """INSERT INTO test_cases (
                        test_case_id, test_case_objective, feature, prerequisite,
                        test_group, priority, notes, dashboard_test_type,
                        jira_issue_id, jira_id, timestamp, user_name, is_edited, test_run_id, row_type
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    (
                        test_case_id,
                        row.get("Test Case Objective", ""),
                        row.get("Feature", ""),
                        row.get("Prerequisite", ""),
                        row.get("Test Group", ""),
                        row.get("Priority", ""),
                        row.get("Notes", ""),
                        test_type,
                        jira_issue_id,
                        jira_id,
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        user_name,
                        1 if is_edited else 0,
                        test_run_id,
                        "header"
                    )
                )

                # Get the ID of the newly inserted test case
                cursor.execute("SELECT last_insert_rowid()")
                tc_id = cursor.fetchone()[0]
                updated_count += 1

                # Insert test step if available
                if "Step No" in row and not pd.isna(row["Step No"]):
                    step_number = int(row["Step No"]) if not pd.isna(row["Step No"]) else None
                    step_description = row["Test Steps"] if "Test Steps" in row and not pd.isna(row["Test Steps"]) else ""
                    expected_result = row["Expected Result"] if "Expected Result" in row and not pd.isna(row["Expected Result"]) else ""
                    test_status = row["Test Status"] if "Test Status" in row and not pd.isna(row["Test Status"]) else ""
                    actual_result = row["Actual Result"] if "Actual Result" in row and not pd.isna(row["Actual Result"]) else ""
                    defect_id = row["Defect ID"] if "Defect ID" in row and not pd.isna(row["Defect ID"]) else ""
                    comments = row["Comments"] if "Comments" in row and not pd.isna(row["Comments"]) else ""

                    if step_number is not None:
                        cursor.execute(
                            """INSERT INTO test_steps (test_case_id, step_number, test_step, expected_result,
                               test_status, actual_result, defect_id, comments)
                               VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                            (tc_id, step_number, step_description, expected_result,
                             test_status, actual_result, defect_id, comments)
                        )

        # Commit the transaction
        conn.commit()

        return True, f"Successfully updated {updated_count} test cases in the database."
    except sqlite3.Error as e:
        # Rollback in case of error
        if conn:
            conn.rollback()
        print(f"Error updating test cases in database: {e}")
        return False, f"Error updating test cases: {str(e)}"
    except Exception as e:
        # Rollback in case of error
        if conn:
            conn.rollback()
        print(f"Unexpected error updating test cases: {e}")
        return False, f"Unexpected error: {str(e)}"
    finally:
        # No need to close the connection as it's managed by get_thread_local_connection
        pass

@retry_on_db_lock()
def get_latest_generated_test_cases(database_path, jira_id, test_type, user_name=None):
    """Gets the most recently generated test cases for a specific JIRA ID and test type.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        test_type: Test type (e.g., "positive", "negative", "all")
        user_name: Name of the user who created the test cases (if None, returns the latest test cases regardless of user)

    Returns:
        A DataFrame containing the most recently generated test cases, or an empty DataFrame if none found
    """
    print(f"Getting latest generated test cases for {jira_id} ({test_type}) by user {user_name}")
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Get all test cases for this JIRA ID and test type, ordered by timestamp
        query = """
        SELECT
            tc.id,
            tc.test_case_id,
            tc.test_case_objective,
            tc.feature,
            tc.prerequisite AS prerequisites,
            tc.test_group,
            tc.priority,
            '' AS test_status,
            '' AS notes,
            tc.dashboard_test_type,
            tc.jira_id,
            tc.timestamp,
            tc.user_name,
            'header' AS row_type,
            ts.step_number,
            ts.test_step AS step_description,
            ts.expected_result,
            tc.is_edited
        FROM test_cases tc
        LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
        WHERE tc.jira_id = ?
        """

        # Add test type filter if not 'all'
        params = [jira_id]
        if test_type.lower() != 'all':
            query += " AND tc.dashboard_test_type = ?"
            params.append(test_type.lower())

        # Add user filter if provided
        if user_name:
            query += " AND tc.user_name = ?"
            params.append(user_name)

        # Order by timestamp to get the most recent test cases
        query += " ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number"

        cursor.execute(query, params)

        # Convert the results to a DataFrame
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()

        if not rows:
            print(f"No test cases found for {jira_id} ({test_type})")
            return pd.DataFrame()

        # Convert to DataFrame
        df = pd.DataFrame(rows, columns=columns)
        print(f"Found {len(df)} rows for {jira_id} ({test_type})")
        return df
    except sqlite3.Error as e:
        print(f"Error getting latest generated test cases: {e}")
        return pd.DataFrame()
    finally:
        # No need to close the connection as it's managed by get_thread_local_connection
        pass

@retry_on_db_lock()
def get_latest_generated_test_cases_original(database_path, jira_id, test_type, user_name=None):
    """Gets the most recently generated test cases for a specific JIRA ID and test type.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        test_type: Test type (e.g., "positive", "negative", "all")
        user_name: Name of the user who created the test cases (if None, returns the latest test cases regardless of user)

    Returns:
        A DataFrame containing the most recently generated test cases, or an empty DataFrame if none found
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Get the latest test run ID for this JIRA ID and test type
        latest_test_run_id = get_latest_test_run_id(database_path, jira_id, test_type, user_name)

        if latest_test_run_id:
            # Get the test run details to check the test type
            cursor.execute(
                """SELECT test_type, num_test_cases, timestamp FROM test_runs WHERE id = ?""",
                (latest_test_run_id,)
            )
            test_run = cursor.fetchone()

            if test_run:
                run_test_type = test_run[0]
                num_test_cases = test_run[1]
                timestamp = test_run[2]
                print(f"Found test run {latest_test_run_id} with {num_test_cases} test cases of type {run_test_type} from {timestamp}")

                # If this is an 'all' test run, we need to get all test cases from this run
                if run_test_type == 'all':
                    # Get all test cases from this test run regardless of test type
                    query = """
                    SELECT
                        tc.id,
                        tc.test_case_id,
                        tc.test_case_objective,
                        tc.feature,
                        tc.prerequisites,
                        tc.test_group,
                        tc.priority,
                        tc.test_status,
                        tc.notes,
                        tc.dashboard_test_type,
                        tc.jira_id,
                        tc.timestamp,
                        tc.user_name,
                        tc.row_type,
                        ts.step_number,
                        ts.step_description,
                        ts.expected_result,
                        tc.is_edited
                    FROM test_cases tc
                    LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                    WHERE tc.test_run_id = ?
                    ORDER BY tc.test_case_id, ts.step_number
                    """
                    cursor.execute(query, (latest_test_run_id,))
                else:
                    # Get the test cases for this specific test run and test type
                    return get_test_cases_for_test_run(database_path, latest_test_run_id)
            else:
                # If no test run details found, fall back to getting test cases by JIRA ID and test type
                query = """
                SELECT
                    tc.id,
                    tc.test_case_id,
                    tc.test_case_objective,
                    tc.feature,
                    tc.prerequisites,
                    tc.test_group,
                    tc.priority,
                    tc.test_status,
                    tc.notes,
                    tc.dashboard_test_type,
                    tc.jira_id,
                    tc.timestamp,
                    tc.user_name,
                    tc.row_type,
                    ts.step_number,
                    ts.step_description,
                    tc.is_edited
                FROM test_cases tc
                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                WHERE tc.jira_id = ? AND tc.dashboard_test_type = ?
                """

                # Add user filter if provided
                if user_name:
                    query += " AND tc.user_name = ?"
                    params = (jira_id, test_type, user_name)
                else:
                    params = (jira_id, test_type)

                # Order by timestamp to get the most recent test cases
                query += " ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number"

                cursor.execute(query, params)
        else:
            # If no test run found, get the most recent test cases from the database
            query = """
            SELECT
                tc.id,
                tc.test_case_id,
                tc.test_case_objective,
                tc.feature,
                tc.prerequisites,
                tc.test_group,
                tc.priority,
                tc.test_status,
                tc.notes,
                tc.dashboard_test_type,
                tc.jira_id,
                tc.timestamp,
                tc.user_name,
                tc.row_type,
                ts.step_number,
                ts.step_description,
                tc.is_edited
            FROM test_cases tc
            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
            WHERE tc.jira_id = ? AND tc.dashboard_test_type = ?
            """

            # Add user filter if provided
            if user_name:
                query += " AND tc.user_name = ?"
                params = (jira_id, test_type, user_name)
            else:
                params = (jira_id, test_type)

            # Order by timestamp to get the most recent test cases
            query += " ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number"

            cursor.execute(query, params)

        # Convert the results to a DataFrame
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()

        if not rows:
            print(f"No test cases found for {jira_id} ({test_type})")
            return pd.DataFrame()

        # Convert to DataFrame
        df = pd.DataFrame(rows, columns=columns)
        print(f"Found {len(df)} rows for {jira_id} ({test_type})")
        return df
    except sqlite3.Error as e:
        print(f"Error getting latest generated test cases: {e}")
        return pd.DataFrame()
    finally:
        # No need to close the connection as it's managed by get_thread_local_connection
        pass

@retry_on_db_lock()
def fix_all_test_type(database_path, jira_id="TP-10", tc_id="TC_006"):
    """Fixes test cases with 'ALL' test type by changing them to 'NEGATIVE'.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-10")
        tc_id: Test case ID to fix (e.g., "TC_006")

    Returns:
        True if successful, False otherwise
    """
    conn = None
    try:
        # Connect to the database
        conn = sqlite3.connect(database_path, timeout=60)
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION;")

        # Update the test case
        cursor.execute(
            """UPDATE test_cases
               SET test_type = 'NEGATIVE', test_group = 'Group 2: NEGATIVE'
               WHERE jira_id = ? AND test_case_id = ? AND test_type = 'ALL'""",
            (jira_id, tc_id)
        )

        # Check if any rows were affected
        rows_affected = cursor.rowcount

        # Commit the transaction
        conn.commit()

        print(f"Fixed {rows_affected} test cases with 'ALL' test type")
        return rows_affected > 0
    except sqlite3.Error as e:
        print(f"Error fixing test cases with 'ALL' test type: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

@retry_on_db_lock()
def get_unique_jira_ids(db_path, user_name=None):
    """
    Get unique JIRA IDs from the database.

    Args:
        db_path (str): Path to the database
        user_name (str, optional): User name to filter by

    Returns:
        list: List of unique JIRA IDs
    """
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Build the query based on whether we have a user_name filter
        query = "SELECT DISTINCT jira_id FROM test_cases WHERE 1=1"
        params = []

        if user_name:
            query += " AND user_name = ?"
            params.append(user_name)

        # Order by JIRA ID
        query += " ORDER BY jira_id"

        # Execute the query
        cursor.execute(query, params)

        # Get the results
        results = cursor.fetchall()

        # Close the connection
        conn.close()

        # Return the JIRA IDs as a list
        return [result[0] for result in results]
    except sqlite3.Error as e:
        print(f"Error getting unique JIRA IDs: {e}")
        if conn:
            conn.close()
        return []

@retry_on_db_lock()
def get_test_cases_by_filters(db_path, jira_id=None, test_type=None, start_date=None, end_date=None, user_name=None):
    """
    Get test cases from the database based on filters.

    Args:
        db_path (str): Path to the database
        jira_id (str, optional): JIRA ID to filter by
        test_type (str, optional): Test type to filter by
        start_date (str, optional): Start date in YYYY-MM-DD format
        end_date (str, optional): End date in YYYY-MM-DD format
        user_name (str, optional): User name to filter by

    Returns:
        pd.DataFrame: DataFrame containing the filtered test cases
    """
    conn = None
    try:
        conn = sqlite3.connect(db_path)

        # Start building the query
        query = """
        SELECT
            tc.jira_id as "User Story ID",
            tc.test_case_id as "Test Case ID",
            tc.test_case_objective as "Test Case Objective",
            tc.prerequisite as "Prerequisite",
            tc.priority as "Priority",
            tc.test_type as "Test Type",
            tc.test_group as "Test Group",
            tc.project as "Project",
            tc.feature as "Feature",
            tc.timestamp as "Timestamp",
            tc.dashboard_test_type as "Dashboard Test Type",
            tc.user_name as "User Name",
            tc.is_edited as "Is Edited",
            ts.step_number as "Step No",
            ts.test_step as "Test Steps",
            ts.expected_result as "Expected Result",
            ts.actual_result as "Actual Result",
            ts.test_status as "Test Status",
            ts.defect_id as "Defect ID",
            ts.comments as "Comments",
            tr.id as "Test Run ID"
        FROM test_cases tc
        LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
        LEFT JOIN test_runs tr ON tc.jira_id = tr.jira_id AND tc.dashboard_test_type = tr.test_type
        WHERE 1=1
        """

        params = []

        # Add filters to the query
        if jira_id:
            query += " AND tc.jira_id = ?"
            params.append(jira_id)

        if test_type:
            query += " AND tc.dashboard_test_type = ?"
            params.append(test_type)

        if start_date:
            query += " AND tc.timestamp >= ?"
            params.append(start_date)

        if end_date:
            query += " AND tc.timestamp <= ?"
            params.append(end_date + " 23:59:59")  # Include the entire day

        if user_name:
            query += " AND tc.user_name = ?"
            params.append(user_name)

        # Order by test case ID and step number
        query += " ORDER BY tc.test_case_id, ts.step_number"

        # Execute the query
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()

        return df
    except sqlite3.Error as e:
        print(f"Error getting test cases by filters: {e}")
        if conn:
            conn.close()
        return pd.DataFrame()  # Return empty DataFrame on error

@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_jira_issue_enhancement(database_path, jira_id):
    """Retrieves the enhanced description and timestamp for a JIRA issue."""
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()
        cursor.execute(
            "SELECT enhanced_description, enhanced_timestamp FROM jira_issues WHERE jira_id = ?",
            (jira_id,)
        )
        row = cursor.fetchone()
        if row:
            return row[0], row[1]
        return None, None
    except sqlite3.Error as e:
        print(f"Error retrieving enhanced description: {e}")
        return None, None
    finally:
        if conn:
            conn.close()