#!/usr/bin/env python3

import streamlit as st
import pandas as pd
import os
from datetime import datetime, timedelta
import sqlite3
import Test_case_db_helper as db
from pathlib import Path
from helpers import create_formatted_excel_from_scenarios

# Import visualization and reporting modules
from gui.visualization import render_visualization
from gui.reporting import render_reporting

def render_test_analysis():
    """
    Render the test analysis page.
    """
    # Test Analysis Page
    st.markdown('<h1 class="main-header">📊 Test Analysis Dashboard</h1>', unsafe_allow_html=True)

    # Check if the user is already logged in through the admin panel
    is_admin_logged_in = st.session_state.get("is_admin_logged_in", False)
    current_user = st.session_state.get("admin_username", "")

    # If the user is logged in as admin, use that username
    if is_admin_logged_in and not current_user:
        current_user = st.session_state.get("admin_user", "")
        # Store it in admin_username for consistency
        st.session_state["admin_username"] = current_user

    # If no user is logged in, show a message
    if not current_user:
        st.warning("Please log in through the Admin Panel to view your test runs.")

        # Add a button to navigate to the Admin Panel
        if st.button("Go to Admin Panel", key="test_analysis_admin_panel_button"):
            # Set a flag to automatically open the Admin Panel expander
            st.session_state["open_admin_panel"] = True
            st.rerun()
    else:
        # Show the user's test runs with a clear message
        st.info(f"Showing test runs for user: {current_user}")

        # Create tabs for different analysis functions
        analysis_tabs = st.tabs(["Test Runs", "Visualization", "Reporting"])

        # We'll handle the unwanted sections differently - by not rendering them at all

        # Tab 1: Test Runs - For viewing and editing test cases
        with analysis_tabs[0]:
            # Get the user's test runs from the database
            try:
                test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)
                if test_runs is None:
                    test_runs = []
            except Exception as e:
                st.error(f"Error retrieving test runs: {str(e)}")
                test_runs = []

            if test_runs:
                # Create a DataFrame from the test runs
                test_runs_df = pd.DataFrame(test_runs)

                # Format the timestamp column
                if "timestamp" in test_runs_df.columns:
                    test_runs_df["timestamp"] = pd.to_datetime(test_runs_df["timestamp"])
                    test_runs_df["timestamp"] = test_runs_df["timestamp"].dt.strftime("%Y-%m-%d %H:%M:%S")

                # Rename columns for better display
                test_runs_df = test_runs_df.rename(columns={
                    "id": "Run ID",
                    "jira_id": "JIRA ID",
                    "test_type": "Test Type",
                    "timestamp": "Timestamp",
                    "num_test_cases": "# Test Cases",
                    "status": "Status",
                    "notes": "Notes"
                })

                # Check if a test run is selected (from session state)
                selected_run_id = st.session_state.get("selected_test_run_id", None)

                # If no run is selected, show the list of test runs
                if not selected_run_id:
                    # Create a more visually appealing display for test runs
                    st.markdown("### Your Test Runs")

                    # Create a table with action buttons
                    # First, create a copy of the dataframe for display
                    display_runs_df = test_runs_df.copy()

                    # Add a view button column
                    display_runs_df["View"] = False

                    # Display the table with action buttons
                    edited_runs = st.data_editor(
                        display_runs_df,
                        column_config={
                            "Run ID": st.column_config.NumberColumn("Run ID", width="small"),
                            "JIRA ID": st.column_config.TextColumn("JIRA ID", width="small"),
                            "Test Type": st.column_config.TextColumn("Test Type", width="small"),
                            "Timestamp": st.column_config.DatetimeColumn("Created", width="medium", format="DD-MM-YYYY HH:mm"),
                            "# Test Cases": st.column_config.NumberColumn("# Test Cases", width="small"),
                            "Status": st.column_config.TextColumn("Status", width="small"),
                            "Notes": st.column_config.TextColumn("Notes", width="medium"),
                            "View": st.column_config.CheckboxColumn("View", width="small", help="Select to view test cases")
                        },
                        disabled=["Run ID", "JIRA ID", "Test Type", "Timestamp", "# Test Cases", "Status", "Notes"],
                        hide_index=True,
                        use_container_width=True,
                        key="test_runs_table"
                    )

                    # Check if any run is selected for viewing
                    if edited_runs["View"].any():
                        # Get the first selected run
                        selected_row = edited_runs[edited_runs["View"]].iloc[0]
                        # Store the selected run ID in session state
                        st.session_state["selected_test_run_id"] = selected_row["Run ID"]
                        # Don't rerun the app, just continue with the selected run
                        selected_run_id = selected_row["Run ID"]

                    # Add a section with cards for quick access
                    st.markdown("### Recent Test Runs")

                    # Get the raw test runs directly from the database
                    try:
                        # Get test runs directly from the database
                        raw_test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)

                        # Convert to DataFrame for easier handling
                        if raw_test_runs:
                            test_runs_df = pd.DataFrame(raw_test_runs)

                            # Convert timestamp to datetime for proper sorting
                            if 'timestamp' in test_runs_df.columns:
                                test_runs_df['timestamp'] = pd.to_datetime(test_runs_df['timestamp'])
                                # Sort by timestamp (descending)
                                test_runs_df = test_runs_df.sort_values(by='timestamp', ascending=False)

                            # Rename columns for display
                            test_runs_df.columns = ['Run ID', 'JIRA ID', 'Test Type', 'Timestamp', '# Test Cases', 'Status', 'Notes']

                            # Get the 5 most recent test runs
                            recent_runs = test_runs_df.head(5)

                            # Debug information (only show count, not the full message)
                            st.caption(f"Showing {len(recent_runs)} most recent test runs")
                        else:
                            # Create an empty DataFrame if no test runs found
                            recent_runs = pd.DataFrame(columns=['Run ID', 'JIRA ID', 'Test Type', 'Timestamp', '# Test Cases', 'Status', 'Notes'])
                            st.caption("No test runs found")
                    except Exception as e:
                        st.error(f"Error retrieving test runs: {str(e)}")
                        recent_runs = pd.DataFrame(columns=['Run ID', 'JIRA ID', 'Test Type', 'Timestamp', '# Test Cases', 'Status', 'Notes'])

                    # Create a more flexible card-based layout for recent runs
                    # Use 3 columns for desktop, but will adjust for mobile
                    num_cols = 3
                    cols = st.columns(num_cols)

                    # Display a message if no recent runs
                    if len(recent_runs) == 0:
                        st.info("No recent test runs found. Generate some test cases to see them here.")

                    # Loop through recent runs and create cards
                    for i, (_, run) in enumerate(recent_runs.iterrows()):
                        # Calculate which column to use (0, 1, or 2)
                        col_idx = i % num_cols

                        with cols[col_idx]:
                            with st.container(border=True):
                                # Make the header more prominent with Run ID included
                                st.markdown(f"<h4 style='color:#1E88E5;'>Run #{run['Run ID']}: {run['JIRA ID']} - {run['Test Type'].upper()}</h4>", unsafe_allow_html=True)

                                # Format the timestamp nicely
                                try:
                                    timestamp = pd.to_datetime(run['Timestamp'])
                                    formatted_time = timestamp.strftime("%b %d, %Y %H:%M")
                                    st.markdown(f"**Created:** {formatted_time}")
                                except:
                                    st.markdown(f"**Created:** {run['Timestamp']}")

                                # Display the test case count directly from the database record
                                # This is more accurate than counting from the test_cases table
                                test_case_count = run['# Test Cases']

                                # Display the count with a badge-like style
                                st.markdown(f"**Test Cases:** <span style='background-color:#E3F2FD;padding:3px 8px;border-radius:10px;'>{test_case_count}</span>", unsafe_allow_html=True)

                                # Add a button to view this test run (without page refresh)
                                if st.button(f"View Test Cases", key=f"view_run_{run['Run ID']}", use_container_width=True):
                                    st.session_state["selected_test_run_id"] = run['Run ID']
                                    # Use rerun to avoid page refresh
                                    st.rerun()

                    # Add a section for all test runs with a selectbox
                    st.markdown("### All Test Runs")

                    # Get the raw test runs directly from the database again for the All Test Runs section
                    try:
                        # Get test runs directly from the database
                        raw_test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)

                        # Convert to DataFrame for easier handling
                        if raw_test_runs:
                            all_test_runs_df = pd.DataFrame(raw_test_runs)

                            # Convert timestamp to datetime for proper sorting
                            if 'timestamp' in all_test_runs_df.columns:
                                all_test_runs_df['timestamp'] = pd.to_datetime(all_test_runs_df['timestamp'])
                                # Sort by timestamp (descending)
                                all_test_runs_df = all_test_runs_df.sort_values(by='timestamp', ascending=False)

                            # Rename columns for display
                            all_test_runs_df.columns = ['Run ID', 'JIRA ID', 'Test Type', 'Timestamp', '# Test Cases', 'Status', 'Notes']

                            # Convert test type to uppercase for consistency
                            all_test_runs_df['Test Type'] = all_test_runs_df['Test Type'].str.upper()
                        else:
                            # Create an empty DataFrame if no test runs found
                            all_test_runs_df = pd.DataFrame(columns=['Run ID', 'JIRA ID', 'Test Type', 'Timestamp', '# Test Cases', 'Status', 'Notes'])
                            st.caption("No test runs found")
                    except Exception as e:
                        st.error(f"Error retrieving test runs: {str(e)}")
                        all_test_runs_df = pd.DataFrame(columns=['Run ID', 'JIRA ID', 'Test Type', 'Timestamp', '# Test Cases', 'Status', 'Notes'])

                    # Create a selectbox with all test runs
                    if not all_test_runs_df.empty:
                        # Create filters for better organization
                        col1, col2 = st.columns(2)

                        with col1:
                            # Group test runs by JIRA ID for better organization
                            jira_ids = all_test_runs_df["JIRA ID"].unique()

                            # Create a filter for JIRA IDs
                            selected_jira_id = st.selectbox(
                                "Filter by JIRA ID",
                                ["All JIRA IDs"] + list(jira_ids),
                                key="jira_id_filter"
                            )

                        with col2:
                            # Create a filter for test types
                            test_types = all_test_runs_df["Test Type"].unique()

                            # Create a filter for test types
                            selected_test_type = st.selectbox(
                                "Filter by Test Type",
                                ["All Test Types"] + list(test_types),
                                key="test_type_filter"
                            )

                        # Apply both filters
                        filtered_runs = all_test_runs_df.copy()

                        # Filter by JIRA ID if selected
                        if selected_jira_id != "All JIRA IDs":
                            filtered_runs = filtered_runs[filtered_runs["JIRA ID"] == selected_jira_id]

                        # Filter by test type if selected
                        if selected_test_type != "All Test Types":
                            filtered_runs = filtered_runs[filtered_runs["Test Type"] == selected_test_type]

                        # Format the timestamp for better readability
                        filtered_runs["Formatted Timestamp"] = pd.to_datetime(filtered_runs["Timestamp"]).dt.strftime("%b %d, %Y %H:%M")

                        # Create a list of options for the selectbox with better formatting
                        run_options = [
                            f"Run #{run['Run ID']}: {run['JIRA ID']} - {run['Test Type'].upper()} ({run['Formatted Timestamp']}) - {run['# Test Cases']} test cases"
                            for _, run in filtered_runs.iterrows()
                        ]
                        run_ids = filtered_runs["Run ID"].tolist()

                        # Create a mapping from option string to run ID
                        option_to_run_id = dict(zip(run_options, run_ids))

                        # Create the selectbox with a clear label
                        if run_options:
                            selected_option = st.selectbox(
                                "Select a test run to view details",
                                run_options,
                                key="all_test_runs_selectbox"
                            )

                            # Add a button to view the selected test run
                            if st.button("View Selected Test Run", key="view_selected_run", use_container_width=True):
                                selected_run_id = option_to_run_id[selected_option]
                                st.session_state["selected_test_run_id"] = selected_run_id
                                # Use rerun to avoid page refresh
                                st.rerun()
                        else:
                            st.info(f"No test runs found for JIRA ID: {selected_jira_id}")
                    else:
                        st.info("No test runs available. Generate some test cases to see them here.")

                if selected_run_id:
                    # Show a back button to return to the test run list
                    if st.button("← Back to Test Runs", key="back_to_test_runs"):
                        st.session_state.pop("selected_test_run_id", None)
                        # Use rerun to avoid page refresh
                        st.rerun()
                    else:
                        # Get the test cases for the selected test run
                        test_cases = db.get_test_cases_by_test_run(db.DATABASE_PATH, selected_run_id)

                        # Convert to DataFrame if it's a list
                        if isinstance(test_cases, list) and len(test_cases) > 0:
                            test_cases_df = pd.DataFrame(test_cases)
                        elif hasattr(test_cases, 'empty') and not test_cases.empty:
                            test_cases_df = test_cases
                        else:
                            # Try a different approach - get test cases directly from the database
                            # Get the JIRA ID and test type from the test run
                            run_info = test_runs_df[test_runs_df["Run ID"] == selected_run_id]
                            if not run_info.empty:
                                jira_id = run_info["JIRA ID"].iloc[0]
                                test_type = run_info["Test Type"].iloc[0]

                                # Get test cases for this JIRA ID and test type
                                test_cases_df = db.get_test_cases_from_database(db.DATABASE_PATH, jira_id, test_type, user_name=current_user)

                                # Filter to only include test cases for this test run
                                if not test_cases_df.empty and "Test Run ID" in test_cases_df.columns:
                                    test_cases_df = test_cases_df[test_cases_df["Test Run ID"] == selected_run_id]
                            else:
                                test_cases_df = pd.DataFrame()

                        # Always try to get test steps directly from the database
                        try:
                            # Get the JIRA ID and test type from the test run
                            run_info = test_runs_df[test_runs_df["Run ID"] == selected_run_id]
                            if not run_info.empty:
                                jira_id = run_info["JIRA ID"].iloc[0]
                                test_type = run_info["Test Type"].iloc[0]

                                # Get test cases with steps for this JIRA ID and test type
                                conn = sqlite3.connect(db.DATABASE_PATH)
                                conn.row_factory = sqlite3.Row
                                cursor = conn.cursor()

                                # Get all test cases and steps for this test run
                                # First, get the latest timestamp for each test case to avoid duplicates
                                query = """
                                SELECT
                                    tc.id as test_case_db_id,
                                    tc.jira_id as "User Story ID",
                                    tc.test_case_id as "Test Case ID",
                                    tc.test_case_objective as "Test Case Objective",
                                    tc.prerequisite as "Prerequisite",
                                    tc.priority as "Priority",
                                    tc.test_type as "Test Type",
                                    tc.test_group as "Test Group",
                                    tc.project as "Project",
                                    tc.feature as "Feature",
                                    tc.timestamp as "Timestamp",
                                    tc.dashboard_test_type as "Dashboard Test Type",
                                    tc.user_name as "User Name",
                                    tc.is_edited as "Is Edited",
                                    ts.step_number as "Step No",
                                    ts.test_step as "Test Steps",
                                    ts.expected_result as "Expected Result",
                                    ts.actual_result as "Actual Result",
                                    ts.test_status as "Test Status",
                                    ts.defect_id as "Defect ID",
                                    ts.comments as "Comments",
                                    tc.test_run_id as "Test Run ID"
                                FROM test_cases tc
                                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                                WHERE tc.test_run_id = ?
                                ORDER BY tc.test_case_id, ts.step_number
                                """
                                cursor.execute(query, (selected_run_id,))
                                rows = cursor.fetchall()

                                # Convert rows to dictionaries
                                test_cases_with_steps = []

                                # Track test cases we've already processed to avoid duplicates
                                processed_test_cases = {}

                                for row in rows:
                                    test_case = dict(row)
                                    test_case_id = test_case["Test Case ID"]
                                    step_no = test_case.get("Step No", "")

                                    # Always add a header row for each test case
                                    if test_case_id not in processed_test_cases:
                                        # Add the full test case header
                                        processed_test_cases[test_case_id] = True

                                        # Create a header row with all test case information
                                        header_row = {
                                            "Timestamp": test_case["Timestamp"],
                                            "Project": test_case["Project"],
                                            "Feature": test_case["Feature"],
                                            "User Story ID": test_case["User Story ID"],
                                            "Test Case ID": test_case_id,
                                            "Test Case Objective": test_case["Test Case Objective"],
                                            "Prerequisite": test_case["Prerequisite"],
                                            "Step No": "",  # Empty for header
                                            "Test Steps": "",  # Empty for header
                                            "Expected Result": "",  # Empty for header
                                            "Actual Result": "",
                                            "Test Status": "",
                                            "Priority": test_case["Priority"],
                                            "Defect ID": "",
                                            "Comments": "",
                                            "Test Type": test_case["Test Type"],
                                            "Test Group": test_case["Test Group"],
                                            "User Name": test_case["User Name"],
                                            "Dashboard Test Type": test_case["Dashboard Test Type"],
                                            "Test Run ID": test_case["Test Run ID"],
                                            "Is Edited": test_case["Is Edited"]
                                        }
                                        test_cases_with_steps.append(header_row)

                                    # Always add the step row, regardless of whether it's the first step
                                    if step_no is not None and step_no != "":
                                        step_row = {
                                            "Timestamp": "",  # Empty for steps
                                            "Project": "",  # Empty for steps
                                            "Feature": "",  # Empty for steps
                                            "User Story ID": "",  # Empty for steps
                                            "Test Case ID": "",  # Empty for steps
                                            "Test Case Objective": "",  # Empty for steps
                                            "Prerequisite": "",  # Empty for steps
                                            "Step No": step_no,
                                            "Test Steps": test_case["Test Steps"],
                                            "Expected Result": test_case["Expected Result"],
                                            "Actual Result": test_case["Actual Result"],
                                            "Test Status": test_case["Test Status"],
                                            "Priority": "",  # Empty for steps
                                            "Defect ID": test_case["Defect ID"],
                                            "Comments": test_case["Comments"],
                                            "Test Type": "",  # Empty for steps
                                            "Test Group": "",  # Empty for steps
                                            "User Name": "",  # Empty for steps
                                            "Dashboard Test Type": "",  # Empty for steps
                                            "Test Run ID": test_case["Test Run ID"],
                                            "Is Edited": ""  # Empty for steps
                                        }
                                        test_cases_with_steps.append(step_row)

                                # Convert to DataFrame
                                if test_cases_with_steps:
                                    test_cases_df = pd.DataFrame(test_cases_with_steps)
                                    st.success(f"Successfully retrieved {len(test_cases_df)} rows with test steps")
                                else:
                                    st.warning(f"No test cases found for test run {selected_run_id}")

                                conn.close()
                        except Exception as e:
                            st.error(f"Error getting test steps: {e}")

                        # Debug information (commented out for cleaner UI)
                        # st.write(f"Found {len(test_cases_df)} rows for test run {selected_run_id}")

                        # Check if we have test steps
                        if "Step No" not in test_cases_df.columns or test_cases_df["Step No"].isna().all():
                            st.warning("No test steps found for this test run. The test cases may not have been properly generated.")

                        if not test_cases_df.empty:
                            # Get the JIRA ID and test type from the test run
                            run_info = test_runs_df[test_runs_df["Run ID"] == selected_run_id]
                            jira_id = run_info["JIRA ID"].iloc[0] if not run_info.empty else "Unknown"
                            test_type = run_info["Test Type"].iloc[0] if not run_info.empty else "Unknown"

                            # Display the test cases with a nicer header
                            st.markdown(f"""
                            <div style="background-color: #f0f2f6; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                                <h2 style="margin: 0; color: #1E88E5;">Test Cases for {jira_id}</h2>
                                <p style="margin: 5px 0 0 0;">Run ID: {selected_run_id} | Type: {test_type.upper()}</p>
                            </div>
                            """, unsafe_allow_html=True)

                            # Ensure User Story ID and Step No are treated as strings
                            if "User Story ID" in test_cases_df.columns:
                                test_cases_df["User Story ID"] = test_cases_df["User Story ID"].fillna("").astype(str)
                            if "Step No" in test_cases_df.columns:
                                test_cases_df["Step No"] = test_cases_df["Step No"].astype(str)

                            # Get unique test case IDs and count
                            unique_test_cases = []
                            if "Test Case ID" in test_cases_df.columns:
                                # Filter out empty strings and count only valid test case IDs (TC_XXX format)
                                valid_test_case_ids = test_cases_df['Test Case ID'].dropna().astype(str)
                                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                                # Only count IDs that match the TC_XXX pattern
                                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                                # Get unique IDs
                                unique_test_cases = valid_test_case_ids.unique()

                                # If no unique test cases were found, try a different approach
                                if len(unique_test_cases) == 0:
                                    # Count rows with non-empty Test Case ID
                                    unique_test_cases = test_cases_df['Test Case ID'].dropna().unique()

                            # Count test statuses
                            status_counts = {"Pass": 0, "Fail": 0, "Blocked": 0, "Not Run": 0}
                            if "Test Status" in test_cases_df.columns:
                                # Normalize status values (case-insensitive)
                                test_cases_df["Test Status"] = test_cases_df["Test Status"].fillna("").str.strip().str.upper()

                                # Count each status
                                for status in status_counts.keys():
                                    status_counts[status] = len(test_cases_df[test_cases_df["Test Status"].str.upper() == status.upper()])

                            # Count priorities
                            priority_counts = {"High": 0, "Medium": 0, "Low": 0}
                            if "Priority" in test_cases_df.columns:
                                # Normalize priority values (case-insensitive)
                                test_cases_df["Priority"] = test_cases_df["Priority"].fillna("").str.strip().str.title()

                                # Count each priority
                                for priority in priority_counts.keys():
                                    priority_counts[priority] = len(test_cases_df[test_cases_df["Priority"].str.title() == priority])

                            # Create a summary section with metrics
                            st.markdown("#### Test Run Summary")

                            # First row of metrics
                            col1, col2, col3, col4 = st.columns(4)
                            with col1:
                                st.metric("Total Test Cases", len(unique_test_cases))
                            with col2:
                                st.metric("Passed", status_counts["Pass"],
                                         delta=f"{status_counts['Pass']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                         delta_color="normal")
                            with col3:
                                st.metric("Failed", status_counts["Fail"],
                                         delta=f"{status_counts['Fail']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                         delta_color="inverse")
                            with col4:
                                st.metric("Not Run", status_counts["Not Run"] + status_counts["Blocked"],
                                         delta=f"{status_counts['Not Run'] + status_counts['Blocked']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                         delta_color="off")

                            # Second row with priority metrics
                            st.markdown("#### Test Case Priorities")
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.metric("High Priority", priority_counts["High"],
                                         delta=f"{priority_counts['High']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                         delta_color="inverse")
                            with col2:
                                st.metric("Medium Priority", priority_counts["Medium"],
                                         delta=f"{priority_counts['Medium']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                         delta_color="normal")
                            with col3:
                                st.metric("Low Priority", priority_counts["Low"],
                                         delta=f"{priority_counts['Low']}/{len(unique_test_cases)}" if len(unique_test_cases) > 0 else None,
                                         delta_color="normal")

                        # Updated column configuration to match the desired order with Timestamp as first column
                        column_config = {
                            "Timestamp": st.column_config.TextColumn(width="medium"),  # Add Timestamp column
                            "Project": st.column_config.TextColumn(width="small"),
                            "Feature": st.column_config.TextColumn(width="medium"),
                            "User Story ID": st.column_config.Column(width="small", required=True),  # Use generic Column type
                            "Test Case ID": st.column_config.TextColumn(width="small"),
                            "Test Case Objective": st.column_config.TextColumn(width="medium"),
                            "Prerequisite": st.column_config.TextColumn(width="medium"),
                            "Step No": st.column_config.TextColumn(width="small"),
                            "Test Steps": st.column_config.TextColumn(width="large"),
                            "Expected Result": st.column_config.TextColumn(width="large"),
                            "Actual Result": st.column_config.TextColumn(width="small", default=""),
                            "Test Status": st.column_config.SelectboxColumn(width="small", options=["Pass", "Fail", "Blocked", "Not Run"], default="Not Run"),
                            "Priority": st.column_config.SelectboxColumn(width="small", options=["High", "Medium", "Low"], default="Medium"),
                            "Defect ID": st.column_config.TextColumn(width="small", default=""),
                            "Comments": st.column_config.TextColumn(width="large", default=""),
                            "Test Type": st.column_config.TextColumn(width="small"),    # Test type
                            "Test Group": st.column_config.TextColumn(width="medium"),  # Group identifier
                            "User Name": st.column_config.TextColumn(width="small"),    # User who generated the test case
                            "Dashboard Test Type": st.column_config.TextColumn(width="small")  # Dashboard test type
                        }

                        # Ensure all columns in config exist in df
                        for col in column_config.keys():
                            if col not in test_cases_df.columns:
                                test_cases_df[col] = "" # Add missing column with default value

                        # Reorder columns based on config
                        ordered_columns = [col for col in column_config.keys() if col in test_cases_df.columns]
                        ordered_columns += [col for col in test_cases_df.columns if col not in ordered_columns]
                        test_cases_df = test_cases_df[ordered_columns]

                        # Process the dataframe to group test steps with their parent test cases
                        # This creates a more readable format similar to the Generated Test Cases tab

                        # First, create a copy of the dataframe to avoid modifying the original
                        display_df = test_cases_df.copy()

                        # First, identify all unique test cases
                        unique_test_cases = display_df['Test Case ID'].dropna().unique()

                        # For each test case, make sure the header row has the header information and step rows don't
                        for test_case in unique_test_cases:
                            # Get all rows for this test case
                            test_case_rows = display_df[display_df['Test Case ID'] == test_case].index.tolist()

                            # Find the header row (should be the one with empty Step No)
                            header_row = None
                            for idx in test_case_rows:
                                step_no = display_df.loc[idx, 'Step No']
                                if pd.isna(step_no) or step_no == "":
                                    header_row = idx
                                    break

                            # If we found the header row, make sure all step rows have empty header fields
                            if header_row is not None:
                                for idx in test_case_rows:
                                    if idx != header_row and pd.notna(display_df.loc[idx, 'Step No']) and display_df.loc[idx, 'Step No'] != "":
                                        # Clear fields that should only appear in the test case header
                                        display_df.loc[idx, 'Timestamp'] = ''
                                        display_df.loc[idx, 'Project'] = ''
                                        display_df.loc[idx, 'Feature'] = ''
                                        display_df.loc[idx, 'User Story ID'] = ''
                                        display_df.loc[idx, 'Test Case ID'] = ''
                                        display_df.loc[idx, 'Test Case Objective'] = ''
                                        display_df.loc[idx, 'Prerequisite'] = ''
                                        display_df.loc[idx, 'Priority'] = ''
                                        display_df.loc[idx, 'Test Type'] = ''
                                        display_df.loc[idx, 'Test Group'] = ''
                                        display_df.loc[idx, 'User Name'] = ''
                                        display_df.loc[idx, 'Dashboard Test Type'] = ''
                                        # Keep the Test Run ID field for reference

                        # Create a data editor for the test cases
                        edited_df = st.data_editor(
                            display_df,
                            column_config=column_config,
                            hide_index=True,
                            use_container_width=True,
                            key="test_cases_editor"
                        )

                        # Add an export button
                        if st.button("Export to Excel", key="test_run_export_button"):
                            try:
                                # Create a temporary Excel file with the test cases
                                test_cases_dir = Path("Test_cases")
                                test_cases_dir.mkdir(exist_ok=True)
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                temp_file = f"Test_Run_{selected_run_id}_{timestamp}.xlsx"
                                temp_file_path = os.path.join(test_cases_dir, temp_file)

                                # Export the edited dataframe to Excel
                                create_formatted_excel_from_scenarios(
                                    edited_df,
                                    temp_file_path,
                                    is_dataframe=True,
                                    save_to_db=False,
                                    create_excel=True  # Explicitly create Excel file for download
                                )

                                # Store the path in session state for the download button
                                st.session_state["test_run_excel_path"] = temp_file_path
                                st.session_state["test_run_excel_filename"] = temp_file

                                st.success(f"Excel file prepared successfully: {temp_file}")
                            except Exception as e:
                                st.error(f"Error preparing Excel file: {str(e)}")

                        # Show download button if the Excel file has been prepared
                        if "test_run_excel_path" in st.session_state and os.path.exists(st.session_state["test_run_excel_path"]):
                            with open(st.session_state["test_run_excel_path"], "rb") as file:
                                st.download_button(
                                    label="📥 Download Test Cases",
                                    data=file,
                                    file_name=st.session_state["test_run_excel_filename"],
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                    key="test_analysis_download_button"
                                )
                        else:
                            st.warning(f"No test cases found for test run {selected_run_id}.")
            else:
                st.info("No test runs found for your user. Generate some test cases to see them here.")

        # Tab 2: Visualization - Real data visualizations
        with analysis_tabs[1]:
            try:
                # Use our new visualization module with proper error handling
                render_visualization(current_user)
            except Exception as e:
                st.error(f"Error rendering visualizations: {str(e)}")
                st.info("Please make sure you have test cases in the database and try again.")
                # Add a button to refresh the page
                if st.button("Refresh Visualization Tab", key="refresh_viz_tab"):
                    st.rerun()

        # Tab 3: Reporting - For generating reports
        with analysis_tabs[2]:
            try:
                # Use our new reporting module with proper error handling
                render_reporting(current_user)
            except Exception as e:
                st.error(f"Error rendering reporting: {str(e)}")
                st.info("Please make sure you have test cases in the database and try again.")
                # Add a button to refresh the page
                if st.button("Refresh Reporting Tab", key="refresh_reporting_tab"):
                    st.rerun()
