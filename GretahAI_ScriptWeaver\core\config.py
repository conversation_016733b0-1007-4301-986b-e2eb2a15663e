"""
Configuration module for GretahAI ScriptWeaver.

This module provides configuration constants and functions for the application.
"""

import os
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.core.config")

# Define the directory where app.py and other files are located
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)

# Configuration file paths
APP_CONFIG_FILE = os.path.join(parent_dir, "config.json")

# Default configuration values
DEFAULT_CONFIG = {
    "google_api_key": "",
    "headless_mode": True,
    "max_failures": 1,
    "screenshot_dir": "screenshots",
    "generated_tests_dir": "generated_tests",
    "temp_uploads_dir": "temp_uploads",
    "detected_elements_dir": "detected_elements"
}

# Script regeneration configuration
MAX_REGENERATIONS = 3  # Maximum number of automatic regeneration attempts

# Ensure directories exist
def ensure_directories_exist():
    """Ensure that all required directories exist."""
    for dir_name in [
        DEFAULT_CONFIG["screenshot_dir"],
        DEFAULT_CONFIG["generated_tests_dir"],
        DEFAULT_CONFIG["temp_uploads_dir"],
        DEFAULT_CONFIG["detected_elements_dir"]
    ]:
        dir_path = os.path.join(parent_dir, dir_name)
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path)
                logger.info(f"Created directory: {dir_path}")
            except Exception as e:
                logger.error(f"Error creating directory {dir_path}: {e}")

# Call this function to ensure directories exist
ensure_directories_exist()
