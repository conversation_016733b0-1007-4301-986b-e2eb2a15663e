"""
Utility functions for the test script generator.
This module provides utility functions for parsing Excel files, saving and loading JSON, etc.
"""

import os
import json
import pandas as pd

def parse_excel(file_path):
    """
    Parse an Excel file containing test cases.
    
    Args:
        file_path (str): Path to the Excel file
        
    Returns:
        list: List of test cases
    """
    print(f"Parsing Excel file: {file_path}")
    
    try:
        # Read the Excel file
        df = pd.read_excel(file_path, dtype=str)  # Read all columns as strings to avoid NaN issues
        
        # Clean up the DataFrame - replace NaN with empty strings and strip whitespace
        df = df.fillna('')
        for col in df.columns:
            if df[col].dtype == 'object':
                df[col] = df[col].str.strip()
        
        # Check if the DataFrame is empty
        if df.empty:
            print("Excel file is empty")
            return []
        
        # Get the column names
        columns = df.columns.tolist()
        
        # Check if the required columns are present
        required_columns = ['Test Case ID', 'Test Case Objective', 'Step No', 'Test Steps', 'Expected Result']
        missing_columns = [col for col in required_columns if col not in columns]
        if missing_columns:
            print(f"Required columns missing: {', '.join(missing_columns)}")
            return []
        
        # Initialize variables
        test_cases = []
        test_cases_map = {}
        current_tc_id = None
        
        # Process each row
        for _, row in df.iterrows():
            tc_id = row['Test Case ID'].strip()
            step_no = row['Step No'].strip()
            test_steps = row['Test Steps'].strip()
            expected_result = row['Expected Result'].strip()
            
            # Skip completely empty rows
            if not any([tc_id, step_no, test_steps, expected_result]):
                continue
            
            # If we have a test case ID, this is a new test case
            if tc_id:
                current_tc_id = tc_id
                if tc_id not in test_cases_map:
                    # Create new test case
                    test_cases_map[tc_id] = {
                        'Test Case ID': tc_id,
                        'Test Case Objective': row['Test Case Objective'].strip(),
                        'Test Type': row.get('Test Type', '').strip(),
                        'Feature': row.get('Feature', '').strip(),
                        'Prerequisites': row.get('Prerequisite', '').strip(),
                        'Steps': []
                    }
                    test_cases.append(test_cases_map[tc_id])
            
            # If we have step information (with or without TC ID)
            if current_tc_id and (step_no or test_steps or expected_result):
                step = {
                    'Step No': step_no or str(len(test_cases_map[current_tc_id]['Steps']) + 1),
                    'Test Steps': test_steps,
                    'Expected Result': expected_result
                }
                test_cases_map[current_tc_id]['Steps'].append(step)
        
        print(f"Parsed {len(test_cases)} test cases from Excel")
        return test_cases
    
    except Exception as e:
        print(f"Error parsing Excel file: {e}")
        return []

def save_json(data, file_path):
    """
    Save data to a JSON file.
    
    Args:
        data: Data to save
        file_path (str): Path to save the JSON file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Save the data to a JSON file
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        
        print(f"Data saved to {file_path}")
        return True
    
    except Exception as e:
        print(f"Error saving JSON file: {e}")
        return False

def load_json(file_path):
    """
    Load data from a JSON file.
    
    Args:
        file_path (str): Path to the JSON file
        
    Returns:
        dict: The loaded data
    """
    try:
        # Check if the file exists
        if not os.path.exists(file_path):
            print(f"JSON file not found: {file_path}")
            return {}
        
        # Load the data from the JSON file
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"Data loaded from {file_path}")
        return data
    
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return {}

def get_config():
    """
    Get the configuration from config.json.
    
    Returns:
        dict: The configuration
    """
    return load_json('config.json')

def save_test_cases_as_json(test_cases, file_path):
    """
    Save test cases to a JSON file.
    
    Args:
        test_cases (list): List of test cases
        file_path (str): Path to save the JSON file
        
    Returns:
        bool: True if successful, False otherwise
    """
    return save_json(test_cases, file_path)

def load_test_cases_from_json(file_path):
    """
    Load test cases from a JSON file.
    
    Args:
        file_path (str): Path to the JSON file
        
    Returns:
        list: List of test cases
    """
    return load_json(file_path)

def save_elements_as_json(elements, file_path):
    """
    Save elements to a JSON file.
    
    Args:
        elements (list): List of elements
        file_path (str): Path to save the JSON file
        
    Returns:
        bool: True if successful, False otherwise
    """
    return save_json(elements, file_path)

def load_elements_from_json(file_path):
    """
    Load elements from a JSON file.
    
    Args:
        file_path (str): Path to the JSON file
        
    Returns:
        list: List of elements
    """
    return load_json(file_path)

def get_element_by_name(elements, name):
    """
    Get an element by its name.
    
    Args:
        elements (list): List of elements
        name (str): Name of the element
        
    Returns:
        dict: The element with the specified name, or None if not found
    """
    for element in elements:
        if element['name'] == name:
            return element
    return None

def get_element_by_selector(elements, selector_type, selector):
    """
    Get an element by its selector.
    
    Args:
        elements (list): List of elements
        selector_type (str): Type of selector (css or xpath)
        selector (str): Selector string
        
    Returns:
        dict: The element with the specified selector, or None if not found
    """
    for element in elements:
        if element['selector_type'] == selector_type and element['selector'] == selector:
            return element
    return None
