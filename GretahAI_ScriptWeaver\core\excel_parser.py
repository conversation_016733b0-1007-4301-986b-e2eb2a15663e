"""
Excel parser module for GretahAI ScriptWeaver.

This module provides functions for parsing Excel files containing test cases.
It is a wrapper around the parse_excel function in util.py to maintain
backward compatibility with the new modular structure.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.core.excel_parser")

# Add the parent directory to the path so we can import from parent modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Import the parse_excel function from util.py
try:
    from util import parse_excel as _parse_excel
    
    # Create a wrapper function with the same signature
    def parse_excel(file_path):
        """
        Parse an Excel file containing test cases.
        
        This is a wrapper around the parse_excel function in util.py to maintain
        backward compatibility with the new modular structure.
        
        Args:
            file_path (str): Path to the Excel file
            
        Returns:
            list: List of test cases
        """
        logger.info(f"Parsing Excel file: {file_path}")
        return _parse_excel(file_path)
        
except ImportError as e:
    logger.error(f"Error importing parse_excel from util.py: {e}")
    
    # Create a dummy function that returns an empty list
    def parse_excel(file_path):
        """
        Dummy parse_excel function that returns an empty list.
        
        This function is used when the real parse_excel function cannot be imported.
        
        Args:
            file_path (str): Path to the Excel file
            
        Returns:
            list: Empty list
        """
        logger.error(f"Cannot parse Excel file {file_path}: parse_excel function not available")
        return []
