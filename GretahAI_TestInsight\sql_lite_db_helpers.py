"""
Provides helper functions for interacting with a SQLite database to store and manage
test automation results, artifacts, and AI-generated analysis.

Key functionalities include:
- Initializing the database schema for test runs, suites, cases, artifacts, and AI summaries.
- Validating and fixing the database schema, particularly for AI summary types.
- Managing thread-local database connections for safe multi-threaded access.
- Implementing a retry decorator (`@retry_on_db_lock`) to handle SQLite 'database is locked' errors.
- Saving parsed test results (from XML or similar formats) into the database structure.
- Linking test artifacts (screenshots, logs, page source) to specific test cases.
- Storing and retrieving various types of AI-generated summaries associated with test cases or runs.
- Retrieving test run history and detailed results for specific runs.
- Comparing test results between two different runs to identify regressions, fixes, and new tests.
- Fetching specific test case details, including associated artifacts and summaries.
- Building unified data structures representing test cases with their related information.
- Managing application configuration settings stored within the database.
- Providing utility functions like safe JSON parsing.
"""
import sqlite3
import os
import json
import time
import random
from datetime import datetime
import threading
import traceback

# Use thread-local storage for database connections
thread_local = threading.local()

def retry_on_db_lock(max_attempts=5, initial_wait=0.1):
    """
    Decorator to retry operations when SQLite database is locked.

    Args:
        max_attempts: Maximum number of retry attempts
        initial_wait: Initial wait time between retries (will be increased exponentially)
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            attempt = 0
            last_error = None
            wait_time = initial_wait

            while attempt < max_attempts:
                try:
                    return func(*args, **kwargs)
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e):
                        attempt += 1
                        last_error = e
                        if attempt < max_attempts:
                            # Exponential backoff with jitter
                            jitter = random.uniform(0.8, 1.2)
                            sleep_time = wait_time * jitter
                            print(f"Database locked, retrying in {sleep_time:.2f}s (attempt {attempt}/{max_attempts})")
                            time.sleep(sleep_time)
                            wait_time *= 2  # Exponential backoff
                        continue
                    else:
                        # Close connection if it exists in thread local on other operational errors
                        if hasattr(thread_local, 'connection') and thread_local.connection:
                            try:
                                thread_local.connection.close()
                            except Exception:
                                pass
                            thread_local.connection = None
                        raise  # Re-raise if it's a different error
                except Exception as e:
                    # Close connection on any other exception
                    if hasattr(thread_local, 'connection') and thread_local.connection:
                        try:
                            thread_local.connection.close()
                        except Exception:
                            pass
                        thread_local.connection = None
                    raise  # Re-raise any other exception

            # If all attempts failed
            print(f"All {max_attempts} attempts failed due to database locks")
            # Ensure connection is closed before raising the final error
            if hasattr(thread_local, 'connection') and thread_local.connection:
                try:
                    thread_local.connection.close()
                except Exception:
                    pass
                thread_local.connection = None
            raise last_error

        return wrapper
    return decorator

def get_thread_local_connection(database_path, timeout=30):
    """Gets or creates a database connection for the current thread."""
    if not hasattr(thread_local, 'connection') or thread_local.connection is None:
        try:
            conn = sqlite3.connect(database_path, timeout=timeout, check_same_thread=False)
            conn.row_factory = sqlite3.Row
            conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
            conn.execute(f"PRAGMA busy_timeout={timeout * 1000}")  # Set busy timeout
            thread_local.connection = conn
        except sqlite3.Error as e:
            print(f"Failed to create connection in thread {threading.get_ident()}: {e}")
            thread_local.connection = None
            raise
    return thread_local.connection

def close_thread_local_connection():
    """Closes the connection for the current thread if it exists."""
    if hasattr(thread_local, 'connection') and thread_local.connection:
        try:
            thread_local.connection.close()
        except Exception as e:
            print(f"Error closing connection in thread {threading.get_ident()}: {e}")
        finally:
            thread_local.connection = None

@retry_on_db_lock()
def validate_and_fix_schema(database_path):
    """
    Validates that key tables have expected schema and attempts to fix if they don't.
    Particularly checks that ai_summaries table has the correct summary_type constraint
    and that test_runs table has the performance_metrics column.
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Check if test_runs table has performance_metrics column
        cursor.execute("PRAGMA table_info(test_runs)")
        columns = cursor.fetchall()
        has_performance_metrics_run = any(col[1] == 'performance_metrics' for col in columns)

        if not has_performance_metrics_run:
            print("Adding performance_metrics column to test_runs table...")
            try:
                cursor.execute("ALTER TABLE test_runs ADD COLUMN performance_metrics TEXT")
                conn.commit()
                print("Added performance_metrics column to test_runs table successfully.")
            except sqlite3.Error as e:
                print(f"Error adding performance_metrics column to test_runs table: {e}")
                if conn and conn.in_transaction:
                    conn.rollback()

        # Check if test_cases table has performance_metrics column
        cursor.execute("PRAGMA table_info(test_cases)")
        columns = cursor.fetchall()
        has_performance_metrics_case = any(col[1] == 'performance_metrics' for col in columns)

        if not has_performance_metrics_case:
            print("Adding performance_metrics column to test_cases table...")
            try:
                cursor.execute("ALTER TABLE test_cases ADD COLUMN performance_metrics TEXT")
                conn.commit()
                print("Added performance_metrics column to test_cases table successfully.")
            except sqlite3.Error as e:
                print(f"Error adding performance_metrics column to test_cases table: {e}")
                if conn and conn.in_transaction:
                    conn.rollback()

        # Check if ai_summaries table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ai_summaries'")
        if cursor.fetchone() is None:
            print("ai_summaries table does not exist, will be created by init_db")
            return True

        # Test if we can insert a comparison_summary
        try:
            cursor.execute("BEGIN TRANSACTION;")
            cursor.execute("""
                INSERT INTO ai_summaries
                (case_id, run_id, model_name, summary_type, summary_content)
                VALUES (NULL, NULL, 'schema_test', 'comparison_summary', 'schema validation test')
            """)
            print("Schema validation: comparison_summary is allowed in the ai_summaries table")
            cursor.execute("ROLLBACK;") # Rollback the test insertion
            return True
        except sqlite3.IntegrityError as e:
            if "CHECK constraint failed: summary_type" in str(e):
                print("Schema issue detected: comparison_summary not allowed in summary_type constraint")
                cursor.execute("ROLLBACK;") # Ensure we rollback the failed transaction

                # Attempt to rebuild the ai_summaries table with correct schema
                print("Attempting to rebuild ai_summaries table with correct schema...")

                # 1. Get existing data
                cursor.execute("SELECT id, case_id, run_id, model_name, summary_type, summary_content, timestamp FROM ai_summaries")
                existing_data = cursor.fetchall()

                # 2. Rename old table
                cursor.execute("ALTER TABLE ai_summaries RENAME TO ai_summaries_old")

                # 3. Create new table with correct schema
                cursor.execute('''
                    CREATE TABLE ai_summaries (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        case_id INTEGER,
                        run_id INTEGER,
                        model_name TEXT,
                        summary_type TEXT NOT NULL CHECK(summary_type IN ('log_summary', 'rca', 'visual_analysis', 'comparison_summary')),
                        summary_content TEXT,
                        timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (case_id) REFERENCES test_cases (id) ON DELETE SET NULL,
                        FOREIGN KEY (run_id) REFERENCES test_runs (id) ON DELETE SET NULL
                    )
                ''')
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_summary_case_id ON ai_summaries (case_id);")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_summary_run_id ON ai_summaries (run_id);")

                # 4. Transfer valid data
                valid_types = ('log_summary', 'rca', 'visual_analysis')
                for row in existing_data:
                    if row['summary_type'] in valid_types:
                        cursor.execute('''
                            INSERT INTO ai_summaries (id, case_id, run_id, model_name, summary_type, summary_content, timestamp)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ''', (row['id'], row['case_id'], row['run_id'], row['model_name'], row['summary_type'], row['summary_content'], row['timestamp']))

                # 5. Drop old table
                cursor.execute("DROP TABLE ai_summaries_old")

                # Commit changes
                conn.commit()
                print("ai_summaries table rebuilt successfully with comparison_summary support")
                return True
            else:
                # Some other integrity error
                print(f"Unexpected integrity error during schema validation: {e}")
                cursor.execute("ROLLBACK;")
                return False
    except sqlite3.Error as e:
        print(f"Database error during schema validation: {e}")
        if conn and conn.in_transaction:
            try:
                conn.rollback()
            except:
                pass
        return False
    except Exception as e:
        print(f"Unexpected error during schema validation: {e}")
        if conn and conn.in_transaction:
            try:
                conn.rollback()
            except:
                pass
        return False

@retry_on_db_lock()
def init_db(database_path):
    """Initializes the database schema if it doesn't exist."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        # Enable Foreign Key support
        cursor.execute("PRAGMA foreign_keys = ON;")
        cursor.execute("PRAGMA journal_mode=WAL;")  # Write-Ahead Logging for better concurrency

        # Test Runs Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_runs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL UNIQUE,
                total_tests INTEGER,
                passed_tests INTEGER,
                failed_tests INTEGER,
                skipped_tests INTEGER,
                duration REAL,
                performance_metrics TEXT
            )
        ''')

        # Test Suites Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_suites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                run_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                total_tests INTEGER,
                failed_tests INTEGER,
                skipped_tests INTEGER,
                duration REAL,
                FOREIGN KEY (run_id) REFERENCES test_runs (id) ON DELETE CASCADE
            )
        ''')

        # Test Cases Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                classname TEXT NOT NULL,
                duration REAL,
                result TEXT NOT NULL CHECK(result IN ('PASSED', 'FAILED', 'SKIPPED', 'ERROR')),
                failure_message TEXT,
                performance_metrics TEXT,
                FOREIGN KEY (suite_id) REFERENCES test_suites (id) ON DELETE CASCADE
            )
        ''')
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_case_name ON test_cases (name, classname);")

        # Artifacts Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS artifacts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_id INTEGER NOT NULL,
                type TEXT NOT NULL CHECK(type IN ('screenshot', 'page_source', 'log', 'other')),
                path TEXT NOT NULL,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (case_id) REFERENCES test_cases (id) ON DELETE CASCADE
            )
        ''')
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_artifact_case_id ON artifacts (case_id);")

        # AI Summaries Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_summaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_id INTEGER,
                run_id INTEGER,
                model_name TEXT,
                summary_type TEXT NOT NULL CHECK(summary_type IN ('log_summary', 'rca', 'visual_analysis', 'comparison_summary')),
                summary_content TEXT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (case_id) REFERENCES test_cases (id) ON DELETE SET NULL,
                FOREIGN KEY (run_id) REFERENCES test_runs (id) ON DELETE SET NULL
            )
        ''')
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_summary_case_id ON ai_summaries (case_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_summary_run_id ON ai_summaries (run_id);")

        # Add App Configuration Table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS app_config (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Initialize default config path if not exists
        default_config_path = os.path.join(os.getcwd(), "config.json")
        cursor.execute("INSERT OR IGNORE INTO app_config (key, value) VALUES ('config_path', ?)",
                    (default_config_path,))

        conn.commit()
        print("Database initialized successfully.")

        # Validate the schema after initialization
        validate_and_fix_schema(database_path)

        return True
    except sqlite3.Error as e:
        print(f"Database initialization error: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    finally:
        if conn and not hasattr(thread_local, 'connection'):
            pass
        elif conn:
            close_thread_local_connection()

@retry_on_db_lock()
def clear_database(database_path):
    """Drops all tables and re-initializes the database."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        print("Attempting to clear database...")
        # List of tables to drop (order might matter due to foreign keys if PRAGMA foreign_keys=ON)
        tables = ["ai_summaries", "artifacts", "test_cases", "test_suites", "test_runs", "app_config"]
        cursor.execute("PRAGMA foreign_keys = OFF;") # Temporarily disable FK constraints
        cursor.execute("BEGIN TRANSACTION;")
        for table in tables:
            print(f"Dropping table {table}...")
            cursor.execute(f"DROP TABLE IF EXISTS {table};")
        conn.commit()
        cursor.execute("PRAGMA foreign_keys = ON;") # Re-enable FK constraints
        print("All tables dropped.")
        # Re-initialize the schema
        print("Re-initializing database schema...")
        init_success = init_db(database_path) # Call init_db directly
        if init_success:
            print("Database cleared and re-initialized successfully.")
            return True
        else:
            print("Database tables dropped, but re-initialization failed.")
            return False
    except sqlite3.Error as e:
        print(f"Database error during clearing: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        # Attempt to re-enable FK constraints even on error
        try:
            if conn: cursor.execute("PRAGMA foreign_keys = ON;")
        except: pass
        return False
    except Exception as e:
        print(f"Unexpected error during database clearing: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        try:
            if conn: cursor.execute("PRAGMA foreign_keys = ON;")
        except: pass
        return False

@retry_on_db_lock()
def get_config_path(database_path):
    """Get the config file path from the database."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        cursor.execute("SELECT value FROM app_config WHERE key = 'config_path'")
        row = cursor.fetchone()
        if row:
            return row['value']
        return os.path.join(os.getcwd(), "config.json")  # Default fallback
    except sqlite3.Error as e:
        print(f"Error fetching config path: {e}")
        return os.path.join(os.getcwd(), "config.json")  # Default fallback

@retry_on_db_lock()
def update_config_path(database_path, new_path):
    """Update the config file path in the database."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        cursor.execute("UPDATE app_config SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = 'config_path'",
                    (new_path,))
        conn.commit()
        return True
    except sqlite3.Error as e:
        print(f"Error updating config path: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False

@retry_on_db_lock()
def link_artifact_direct(database_path, case_id, artifact_type, artifact_path):
    """Directly links a known artifact path to a test case."""
    if not artifact_path or not os.path.exists(artifact_path):
        return False
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        # Check if this exact artifact is already linked to avoid duplicates
        cursor.execute("SELECT 1 FROM artifacts WHERE case_id = ? AND type = ? AND path = ?",
                       (case_id, artifact_type, artifact_path))
        if cursor.fetchone() is None:
            cursor.execute("INSERT INTO artifacts (case_id, type, path) VALUES (?, ?, ?)",
                           (case_id, artifact_type, artifact_path))
            conn.commit()
            return True
        else:
            return True  # Already linked is considered success
    except sqlite3.Error as e:
        print(f"Error linking artifact {artifact_path} for case {case_id}: {e}")
        if conn and conn.in_transaction: conn.rollback()
        return False

@retry_on_db_lock()
def find_artifacts(database_path, case_id, artifact_type=None):
    """Finds artifacts linked to a specific test case, optionally filtering by type."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        if artifact_type:
            cursor.execute("SELECT id, type, path, timestamp FROM artifacts WHERE case_id = ? AND type = ? ORDER BY timestamp DESC",
                           (case_id, artifact_type))
        else:
            cursor.execute("SELECT id, type, path, timestamp FROM artifacts WHERE case_id = ? ORDER BY timestamp DESC",
                           (case_id,))
        artifacts = cursor.fetchall()
        return [dict(row) for row in artifacts]
    except sqlite3.Error as e:
        print(f"Database Error finding artifacts for case {case_id}: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error finding artifacts for case {case_id}: {e}")
        return []

@retry_on_db_lock()
def find_log_files(database_path, case_id):
    """Finds log files linked to a specific test case."""
    logs = find_artifacts(database_path, case_id, artifact_type='log')
    return logs[0] if logs else None

@retry_on_db_lock()
def find_page_source_files(database_path, case_id):
    """Finds page source files linked to a specific test case."""
    sources = find_artifacts(database_path, case_id, artifact_type='page_source')
    return sources[0] if sources else None

@retry_on_db_lock()
def find_and_link_artifact(database_path, case_id, artifact_type, artifact_path):
    """Finds if an artifact exists and links it if not already present. Wrapper around link_artifact_direct."""
    print(f"DEBUG [find_and_link_artifact]: Linking {artifact_type} at {artifact_path} for case {case_id}")
    return link_artifact_direct(database_path, case_id, artifact_type, artifact_path)

@retry_on_db_lock()
def find_run_id_by_timestamp(database_path, timestamp):
    """Finds the run_id for a given timestamp."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM test_runs WHERE timestamp = ?", (timestamp,))
        row = cursor.fetchone()
        return row['id'] if row else None
    except sqlite3.Error as e:
        print(f"Database Error finding run_id for timestamp {timestamp}: {e}")
        return None

@retry_on_db_lock()
def save_summary(database_path, case_id, model_name, summary_type, summary_content, run_id=None):
    """Saves an AI summary to the database, linked to a test case or run."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO ai_summaries (case_id, run_id, model_name, summary_type, summary_content)
            VALUES (?, ?, ?, ?, ?)
        ''', (case_id, run_id, model_name, summary_type, summary_content))
        conn.commit()
        print(f"Saved {summary_type} summary for model {model_name}, case_id={case_id}, run_id={run_id}")
        return True
    except sqlite3.IntegrityError as e:
        # Check if it's the specific CHECK constraint error
        if "CHECK constraint failed: summary_type" in str(e):
            print(f"Database schema error: The summary_type '{summary_type}' is not allowed.")
            print("Please ensure the database schema is up-to-date. You might need to clear the database via the UI.")
            raise ValueError(f"Database schema outdated: summary_type '{summary_type}' not allowed. Clear database and retry.") from e
        else:
            print(f"Database integrity error saving summary: {e}")
            if conn and conn.in_transaction: conn.rollback()
            return False
    except sqlite3.Error as e:
        print(f"Database error saving summary: {e}")
        if conn and conn.in_transaction: conn.rollback()
        return False
    except Exception as e:
        print(f"Unexpected error saving summary: {e}")
        if conn and conn.in_transaction: conn.rollback()
        return False

@retry_on_db_lock()
def get_comparison_summary(db_path, current_run_id, previous_run_timestamp, model_name):
    """
    Retrieves a comparison summary from the database.

    Args:
        db_path: Path to the database.
        current_run_id: The ID of the current run (the one the summary is attached to).
        previous_run_timestamp: The timestamp of the run it was compared against.
        model_name: The name of the AI model used for the summary.

    Returns:
        The summary content (dict or str) if found, otherwise None.
    """
    conn = None
    try:
        conn = get_thread_local_connection(db_path)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT summary_content
            FROM ai_summaries
            WHERE run_id = ?
              AND summary_type = 'comparison_summary'
              AND model_name = ?
            ORDER BY timestamp DESC
        """, (current_run_id, model_name))

        summaries = cursor.fetchall()

        for summary_row in summaries:
            content_str = summary_row['summary_content']
            if not content_str:
                continue

            parsed_content = safe_parse_json(content_str)
            if isinstance(parsed_content, dict):
                metadata = parsed_content.get('metadata', {})
                if metadata.get('compared_run_timestamp') == previous_run_timestamp:
                    print(f"Found matching comparison summary for run {current_run_id} vs {previous_run_timestamp} with model {model_name}")
                    return parsed_content
            elif isinstance(parsed_content, str):
                print(f"Found string comparison summary for run {current_run_id} with model {model_name}. Cannot verify previous run.")
                return parsed_content

        print(f"No matching comparison summary found for run {current_run_id} vs {previous_run_timestamp} with model {model_name}")
        return None

    except sqlite3.Error as e:
        print(f"Database error in get_comparison_summary: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error in get_comparison_summary: {e}")
        return None

@retry_on_db_lock(max_attempts=3)
def save_test_results(database_path, xml_data, timestamp):
    """Save parsed test results and link artifacts using paths from XML data."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        print(f"DEBUG: Attempting to save results for timestamp: {timestamp}")
        cursor.execute("BEGIN TRANSACTION;")

        summary = xml_data["summary"]
        run_duration = summary.get("duration", 0)  # Use duration from summary

        # Check if performance metrics are available in the summary
        performance_metrics_json = None
        if "performance_metrics" in summary:
            try:
                performance_metrics_json = json.dumps(summary["performance_metrics"])
            except Exception as e:
                print(f"Error serializing performance metrics: {e}")

        cursor.execute('''
            INSERT INTO test_runs (timestamp, total_tests, passed_tests, failed_tests, skipped_tests, duration, performance_metrics)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (timestamp, summary["total_tests"], summary["passed_tests"], summary["failed_tests"], summary.get("skipped_tests", 0), run_duration, performance_metrics_json))
        run_id = cursor.lastrowid

        for suite in xml_data["test_suites"]:
            cursor.execute('''
                INSERT INTO test_suites (run_id, name, total_tests, failed_tests, skipped_tests, duration)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (run_id, suite["name"], suite.get("tests", 0), suite.get("failures", 0) + suite.get("errors", 0), suite.get("skipped", 0), suite.get("time", 0)))
            suite_id = cursor.lastrowid

            for case in suite.get("testcases", []):
                valid_results = ('PASSED', 'FAILED', 'SKIPPED', 'ERROR')
                case_result = case["result"] if case["result"] in valid_results else 'ERROR'
                case_name = case["name"]
                class_name = case["classname"]

                # Extract performance metrics for this test case if available
                case_performance_metrics_json = None
                if "performance_metrics" in case:
                    try:
                        case_performance_metrics_json = json.dumps(case["performance_metrics"])
                    except Exception as e:
                        print(f"Error serializing performance metrics for case {case_name}: {e}")

                cursor.execute('''
                    INSERT INTO test_cases (suite_id, name, classname, duration, result, failure_message, performance_metrics)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (suite_id, case_name, class_name, case["duration"], case_result, case.get("failure_message"), case_performance_metrics_json))
                case_id = cursor.lastrowid

                conn.commit()
                cursor.execute("BEGIN TRANSACTION;")

                try:
                    log_path = case.get("log_path")
                    screenshot_path = case.get("screenshot_path")
                    page_source_path = case.get("page_source_path")

                    print(f"DEBUG [db_helper]: Linking artifacts for case {case_id} ({case_name}):")
                    print(f"  Log Path: {log_path}")
                    print(f"  Screenshot Path: {screenshot_path}")
                    print(f"  Page Source Path: {page_source_path}")

                    if log_path:
                        link_artifact_direct(database_path, case_id, 'log', log_path)
                    if screenshot_path:
                        link_artifact_direct(database_path, case_id, 'screenshot', screenshot_path)
                    if page_source_path:
                        link_artifact_direct(database_path, case_id, 'page_source', page_source_path)

                    conn.commit()

                except Exception as artifact_link_err:
                    print(f"Error during artifact linking for case {case_id}: {artifact_link_err}")
                    if conn and conn.in_transaction: conn.rollback()
                    cursor.execute("BEGIN TRANSACTION;")
                    continue

            cursor.execute("BEGIN TRANSACTION;")

        if conn.in_transaction:
            conn.commit()
        print(f"DEBUG: Successfully processed results for timestamp: {timestamp}")
        return True

    except sqlite3.IntegrityError as e:
        print(f"DEBUG: IntegrityError saving results for {timestamp}: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    except sqlite3.Error as e:
        print(f"DEBUG: sqlite3.Error saving results for {timestamp}: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        return False
    except Exception as e:
        print(f"DEBUG: Unexpected error saving results for {timestamp}: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        print(traceback.format_exc())
        return False

@retry_on_db_lock()
def load_test_run_history(database_path, limit=20):
    """Loads the most recent test run history summaries from the database."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, timestamp, total_tests, passed_tests, failed_tests, duration, performance_metrics
            FROM test_runs
            ORDER BY timestamp DESC
            LIMIT ?
        ''', (limit,))
        history = cursor.fetchall()

        # Process the results to parse performance metrics JSON
        processed_history = []
        for row_dict in [dict(row) for row in history]:
            # Parse performance metrics JSON if available
            if row_dict.get('performance_metrics'):
                try:
                    row_dict['performance'] = json.loads(row_dict['performance_metrics'])
                except json.JSONDecodeError:
                    row_dict['performance'] = {}
            else:
                row_dict['performance'] = {}

            # Remove the raw JSON string to avoid duplication
            if 'performance_metrics' in row_dict:
                del row_dict['performance_metrics']

            processed_history.append(row_dict)

        return processed_history
    except sqlite3.Error as e:
        print(f"Database Error loading test run history: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error loading test run history: {e}")
        return []

@retry_on_db_lock()
def get_test_run_details(database_path, run_timestamp):
    """Loads detailed test case data for a specific run timestamp from the database."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT tc.id as case_id, tc.name, tc.classname, tc.duration, tc.result, tc.failure_message, tc.performance_metrics,
                   ts.name as suite_name, tr.timestamp as run_timestamp
            FROM test_cases tc
            JOIN test_suites ts ON tc.suite_id = ts.id
            JOIN test_runs tr ON ts.run_id = tr.id
            WHERE tr.timestamp = ?
        ''', (run_timestamp,))
        details = cursor.fetchall()
        return [dict(row) for row in details]
    except sqlite3.Error as e:
        print(f"Database Error loading test run details for {run_timestamp}: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error loading test run details for {run_timestamp}: {e}")
        return []

@retry_on_db_lock()
def find_case_id(db_path, run_timestamp, class_name, test_name):
    """Finds the case_id for a specific test within a specific run."""
    conn = None
    try:
        conn = get_thread_local_connection(db_path)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT tc.id
            FROM test_cases tc
            JOIN test_suites ts ON tc.suite_id = ts.id
            JOIN test_runs tr ON ts.run_id = tr.id
            WHERE tr.timestamp = ? AND tc.classname = ? AND tc.name = ?
            LIMIT 1
        """, (run_timestamp, class_name, test_name))
        result = cursor.fetchone()
        return result['id'] if result else None
    except sqlite3.Error as e:
        print(f"Database error finding case ID for {run_timestamp}/{class_name}/{test_name}: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error finding case ID: {e}")
        return None

@retry_on_db_lock()
def get_single_test_case_details(db_path, case_id):
    """Fetches all details for a single test case, including artifacts and summaries."""
    conn = None
    details = {}
    try:
        conn = get_thread_local_connection(db_path)
        cursor = conn.cursor()

        # Fetch base case details
        cursor.execute("""
            SELECT id as db_case_id, name as test_name, classname as class_name, duration, result, failure_message, performance_metrics
            FROM test_cases
            WHERE id = ?
        """, (case_id,))
        case_data = cursor.fetchone()

        if not case_data:
            print(f"No test case found with ID: {case_id}")
            return None

        details = dict(case_data)

        # Process performance metrics JSON if available
        if details.get("performance_metrics"):
            try:
                details["performance_metrics"] = safe_parse_json(details["performance_metrics"])
            except Exception as e:
                print(f"Error parsing performance metrics for case {case_id}: {e}")
                details["performance_metrics"] = {}

        # Fetch latest artifacts
        cursor.execute("""
            SELECT type, path
            FROM artifacts
            WHERE case_id = ?
            ORDER BY timestamp DESC
        """, (case_id,))
        artifacts = cursor.fetchall()
        artifact_paths = {
            'screenshot_path': None,
            'page_source_path': None,
            'log_path': None
        }
        for art in artifacts:
            type_key = f"{art['type']}_path"
            if type_key in artifact_paths and artifact_paths[type_key] is None: # Take the first (latest) of each type
                artifact_paths[type_key] = art['path']
        details.update(artifact_paths)

        # Fetch latest visual analysis summary
        cursor.execute("""
            SELECT summary_content
            FROM ai_summaries
            WHERE case_id = ? AND summary_type = 'visual_analysis'
            ORDER BY timestamp DESC
            LIMIT 1
        """, (case_id,))
        summary_data = cursor.fetchone()
        if summary_data and summary_data['summary_content']:
            details['visual_analysis'] = safe_parse_json(summary_data['summary_content'])
        else:
            details['visual_analysis'] = None # Ensure key exists even if no summary

        return details

    except sqlite3.Error as e:
        print(f"Database error fetching details for case ID {case_id}: {e}")
        return None # Return None on DB error
    except Exception as e:
        print(f"Unexpected error fetching details for case ID {case_id}: {e}")
        print(traceback.format_exc())
        return None # Return None on other errors

@retry_on_db_lock()
def compare_test_results(db_path, current_run_timestamp, previous_run_timestamp):
    """Compares test results between two runs."""
    conn = get_thread_local_connection(db_path)
    cursor = conn.cursor()

    try:
        # Get current run details
        cursor.execute("""
            SELECT tc.id as case_id, tc.name, tc.classname, tc.result, tc.duration, tc.failure_message
            FROM test_cases tc
            JOIN test_suites ts ON tc.suite_id = ts.id
            JOIN test_runs tr ON ts.run_id = tr.id
            WHERE tr.timestamp = ?
        """, (current_run_timestamp,))
        current_results_raw = cursor.fetchall()
        current_results = {
            (row["name"], row["classname"]): {"result": row["result"], "duration": row["duration"], "failure_message": row["failure_message"], "case_id": row["case_id"]}
            for row in current_results_raw
        }

        # Get previous run details
        cursor.execute("""
            SELECT tc.id as case_id, tc.name, tc.classname, tc.result, tc.duration, tc.failure_message
            FROM test_cases tc
            JOIN test_suites ts ON tc.suite_id = ts.id
            JOIN test_runs tr ON ts.run_id = tr.id
            WHERE tr.timestamp = ?
        """, (previous_run_timestamp,))
        previous_results_raw = cursor.fetchall()
        previous_results = {
            (row["name"], row["classname"]): {"result": row["result"], "duration": row["duration"], "failure_message": row["failure_message"], "case_id": row["case_id"]}
            for row in previous_results_raw
        }

        fixed_tests = []
        regression_tests = []
        still_failing = []
        new_tests = []

        # Identify changes
        current_keys = set(current_results.keys())
        previous_keys = set(previous_results.keys())

        common_keys = current_keys.intersection(previous_keys)
        new_keys = current_keys.difference(previous_keys)
        removed_keys = previous_keys.difference(current_keys)  # Less relevant for typical comparison

        for key in common_keys:
            name, classname = key
            current_test = current_results[key]
            previous_test = previous_results[key]

            if previous_test["result"] == "FAILED" and current_test["result"] == "PASSED":
                fixed_tests.append({
                    "name": name,
                    "classname": classname,
                    "result": current_test["result"],
                    "duration": current_test["duration"],
                    "case_id": current_test["case_id"]
                })
            elif previous_test["result"] == "PASSED" and current_test["result"] == "FAILED":
                regression_tests.append({
                    "name": name,
                    "classname": classname,
                    "result": current_test["result"],
                    "duration": current_test["duration"],
                    "failure_message": current_test["failure_message"],
                    "case_id": current_test["case_id"]
                })
            elif previous_test["result"] == "FAILED" and current_test["result"] == "FAILED":
                still_failing.append({
                    "name": name,
                    "classname": classname,
                    "result": current_test["result"],
                    "duration": current_test["duration"],
                    "failure_message": current_test["failure_message"],
                    "case_id": current_test["case_id"]
                })

        for key in new_keys:
            name, classname = key
            current_test = current_results[key]
            new_tests.append({
                "name": name,
                "classname": classname,
                "result": current_test["result"],
                "duration": current_test["duration"],
                "failure_message": current_test["failure_message"],
                "case_id": current_test["case_id"]
            })

        summary = {
            "fixed": len(fixed_tests),
            "regression": len(regression_tests),
            "still_failing": len(still_failing),
            "new_tests": len(new_tests)
        }

        return {
            "summary": summary,
            "fixed_tests": fixed_tests,
            "regression_tests": regression_tests,
            "still_failing": still_failing,
            "new_tests": new_tests
        }

    except sqlite3.Error as e:
        print(f"Database error during comparison: {e}")
        return None

@retry_on_db_lock()
def build_unified_test_entries_from_db(database_path, run_timestamp):
    """Build unified test entries from the database for a given run."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        entries = []
        cursor.execute('''
            SELECT tc.id as case_id, tc.name, tc.classname, tc.duration, tc.result, tc.failure_message, tc.performance_metrics,
                   ts.name as suite_name
            FROM test_cases tc
            JOIN test_suites ts ON tc.suite_id = ts.id
            JOIN test_runs tr ON ts.run_id = tr.id
            WHERE tr.timestamp = ?
        ''', (run_timestamp,))
        cases = cursor.fetchall()

        if not cases:
            return []

        cases = [dict(case) for case in cases]
        case_ids = [case['case_id'] for case in cases]

        artifacts_map = {}
        if case_ids:
            placeholders = ','.join('?' * len(case_ids))
            cursor.execute(f"SELECT case_id, type, path FROM artifacts WHERE case_id IN ({placeholders}) ORDER BY timestamp DESC", case_ids)
            all_artifacts = [dict(art) for art in cursor.fetchall()]
            for art in all_artifacts:
                case_id = art['case_id']
                if case_id not in artifacts_map:
                    artifacts_map[case_id] = {}
                if art['type'] not in artifacts_map[case_id]:
                     artifacts_map[case_id][art['type']] = art['path']

            summaries_map = {}
            cursor.execute(f"""
                SELECT case_id, summary_type, summary_content FROM ai_summaries
                WHERE case_id IN ({placeholders}) AND summary_type IN ('log_summary', 'visual_analysis')
                ORDER BY timestamp DESC
            """, case_ids)
            all_summaries = [dict(summary) for summary in cursor.fetchall()]
            for summary in all_summaries:
                case_id = summary['case_id']
                if case_id not in summaries_map:
                    summaries_map[case_id] = {}
                if summary['summary_type'] not in summaries_map[case_id]:
                    try:
                        summaries_map[case_id][summary['summary_type']] = safe_parse_json(summary['summary_content'])
                    except Exception as parse_err:
                        summaries_map[case_id][summary['summary_type']] = {"raw_text": summary['summary_content'], "parse_error": str(parse_err)}

        for case in cases:
            case_id = case['case_id']
            case_artifacts = artifacts_map.get(case_id, {})
            case_summaries = summaries_map.get(case_id, {})

            # Process performance metrics JSON if available
            performance_metrics = None
            if case.get("performance_metrics"):
                try:
                    performance_metrics = json.loads(case["performance_metrics"])
                except json.JSONDecodeError:
                    performance_metrics = {}

            entry = {
                "db_case_id": case_id,
                "test_name": case["name"],
                "class_name": case["classname"],
                "duration": float(case["duration"]) if case["duration"] else 0,
                "result": case["result"],
                "failure_message": case.get("failure_message", ""),
                "screenshot_path": case_artifacts.get("screenshot"),
                "page_source_path": case_artifacts.get("page_source"),
                "log_path": case_artifacts.get("log"),
                "ai_summary": case_summaries.get("log_summary"),
                "visual_analysis": case_summaries.get("visual_analysis"),
                "performance_metrics": performance_metrics
            }
            entries.append(entry)
        return entries

    except sqlite3.Error as e:
        print(f"Database Error building unified entries: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error building unified entries: {e}")
        print(traceback.format_exc())
        return []

def safe_parse_json(json_str, default=None):
    """
    Safely parse JSON string with enhanced error handling.

    Args:
        json_str (str): The JSON string to parse
        default: The value to return if parsing fails

    Returns:
        Parsed JSON object or default value if parsing fails
    """
    if not json_str:
        print("Warning: Empty JSON string received")
        return default

    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        print(f"JSON Parse Error at position {e.pos}: {e}")
        error_context = json_str[max(0, e.pos-40):min(len(json_str), e.pos+40)]
        print(f"Context around error: ...{error_context}...")

        try:
            cleaned_json = json_str.strip()
            if cleaned_json.startswith('\ufeff'):
                cleaned_json = cleaned_json[1:]
            if cleaned_json.count('{') > cleaned_json.count('}'):
                cleaned_json += '}' * (cleaned_json.count('{') - cleaned_json.count('}'))

            parsed = json.loads(cleaned_json)
            print("Successfully parsed JSON after cleaning")
            return parsed
        except Exception as cleanup_err:
            print(f"Failed to recover JSON: {cleanup_err}")

            try:
                import re
                json_pattern = r'(\{[^{}]*(\{[^{}]*\})*[^{}]*\})'
                matches = re.findall(json_pattern, json_str)
                if matches:
                    for potential_json, _ in matches:
                        try:
                            result = json.loads(potential_json)
                            print("Successfully extracted JSON using regex")
                            return result
                        except:
                            continue
            except:
                pass

            try:
                error_log_path = os.path.join(os.getcwd(), "json_error_logs")
                os.makedirs(error_log_path, exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                with open(os.path.join(error_log_path, f"json_error_{timestamp}.txt"), "w", encoding="utf-8") as f:
                    f.write(f"Original JSON string:\n{json_str}\n\nError: {e}")
                print(f"Saved problematic JSON to json_error_logs/json_error_{timestamp}.txt")
            except Exception as save_err:
                print(f"Failed to save error log: {save_err}")

            return default

@retry_on_db_lock()
def get_all_unique_classnames(db_path: str) -> list[str]:
    """Retrieves a list of all unique classnames across all runs."""
    conn = get_thread_local_connection(db_path)
    cursor = conn.cursor()
    try:
        cursor.execute("""
            SELECT DISTINCT classname
            FROM test_cases
            WHERE classname IS NOT NULL AND classname != ''
            ORDER BY classname
        """)
        classnames = [row[0] for row in cursor.fetchall()]
        return classnames
    except sqlite3.Error as e:
        print(f"Database error in get_all_unique_classnames: {e}")
        return []

@retry_on_db_lock()
def get_classnames_for_run(db_path: str, run_timestamp: str) -> list[str]:
    """Retrieves a list of unique classnames for a given test run timestamp."""
    conn = get_thread_local_connection(db_path)
    cursor = conn.cursor()
    try:
        cursor.execute("""
            SELECT DISTINCT tc.classname
            FROM test_cases tc
            JOIN test_suites ts ON tc.suite_id = ts.id
            JOIN test_runs tr ON ts.run_id = tr.id
            WHERE tr.timestamp = ? AND tc.classname IS NOT NULL AND tc.classname != ''
            ORDER BY tc.classname
        """, (run_timestamp,))
        classnames = [row[0] for row in cursor.fetchall()]
        return classnames
    except sqlite3.Error as e:
        print(f"Database error in get_classnames_for_run: {e}")
        return []

@retry_on_db_lock()
def find_previous_run_with_class(db_path: str, current_run_ts: str, classname: str) -> str | None:
    """
    Finds the timestamp of the most recent run *before* current_run_ts
    that contains at least one test case with the specified classname.
    """
    conn = get_thread_local_connection(db_path)
    cursor = conn.cursor()
    try:
        cursor.execute("""
            SELECT tr.timestamp
            FROM test_runs tr
            JOIN test_suites ts ON tr.id = ts.run_id
            JOIN test_cases tc ON ts.id = tc.suite_id
            WHERE tr.timestamp < ? AND tc.classname = ?
            GROUP BY tr.timestamp
            ORDER BY tr.timestamp DESC
            LIMIT 1
        """, (current_run_ts, classname))
        result = cursor.fetchone()
        return result[0] if result else None
    except sqlite3.Error as e:
        print(f"Database error in find_previous_run_with_class: {e}")
        return None

@retry_on_db_lock()
def find_run_summary(database_path: str, run_id: int, summary_type: str) -> list[dict]:
    """
    Finds AI summaries associated with a specific run_id and summary_type.

    Args:
        database_path: Path to the SQLite database.
        run_id: The ID of the test run.
        summary_type: The type of summary to find (e.g., 'rca', 'comparison_summary').

    Returns:
        A list of dictionaries, each representing a summary found, ordered by timestamp descending.
        Returns an empty list if none are found or on error.
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, case_id, run_id, model_name, summary_type, summary_content, timestamp
            FROM ai_summaries
            WHERE run_id = ? AND summary_type = ?
            ORDER BY timestamp DESC
        """, (run_id, summary_type))
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return results
    except sqlite3.Error as e:
        print(f"Database error in find_run_summary: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error in find_run_summary: {e}")
        return []
