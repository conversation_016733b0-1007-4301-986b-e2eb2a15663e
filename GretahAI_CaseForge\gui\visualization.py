#!/usr/bin/env python3

import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import os
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import Test_case_db_helper as db  # Import database helper

def render_visualization(current_user):
    """
    Render the visualization tab for test analysis.

    Args:
        current_user (str): The current user's username
    """
    st.subheader("Test Case Analytics Dashboard")
    st.info("Visualize your test case data to gain insights into your testing coverage and distribution.")

    # Check if the database exists
    if os.path.exists(db.DATABASE_PATH):
        # Get data from database
        try:
            # Get unique JIRA IDs
            jira_ids = db.get_unique_jira_ids(db.DATABASE_PATH, user_name=current_user)

            # Get test runs
            try:
                test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)
                if test_runs is None:
                    test_runs = []
            except Exception as e:
                st.error(f"Error retrieving test runs: {str(e)}")
                test_runs = []

            # If no data found, show a helpful message
            if not jira_ids and not test_runs:
                st.warning("No test data found for your user. Please generate some test cases first.")
                st.info("Go to the Test Generator tab to create test cases, then come back to this tab to visualize them.")
                return
        except Exception as e:
            st.error(f"Error testing database functions: {str(e)}")
            st.info("There might be an issue with the database. Please check the database connection and try again.")
            return
    else:
        st.error(f"Database not found at {db.DATABASE_PATH}")
        return

    # Create a selection interface for data source
    st.markdown("### 📊 Select Data Source")

    # Create tabs for different data sources
    data_source_tabs = st.tabs(["By JIRA ID", "By Test Run", "All Data"])

    # Initialize variables
    df_all = pd.DataFrame()
    selected_data_source = None
    selected_jira_id = None
    selected_run_id = None

    # Tab 1: By JIRA ID
    with data_source_tabs[0]:
        # Get unique JIRA IDs from the database
        jira_ids = db.get_unique_jira_ids(db.DATABASE_PATH, user_name=current_user)

        if jira_ids:
            # Create a selectbox for JIRA IDs
            selected_jira_id = st.selectbox(
                "Select JIRA ID",
                options=jira_ids,
                key="viz_jira_id_select"
            )

            # Add a test type filter
            test_type_options = ["All Types", "positive", "negative", "security", "performance"]
            selected_test_type = st.selectbox(
                "Select Test Type",
                options=test_type_options,
                key="viz_test_type_select"
            )

            # Apply button
            if st.button("Show Visualizations for Selected JIRA ID", key="viz_jira_apply", use_container_width=True):
                with st.spinner("Loading data..."):
                    # Get test cases for the selected JIRA ID
                    if selected_test_type == "All Types":
                        # Get all test types for this JIRA ID
                        test_cases_data = db.get_test_cases_by_filters(
                            db.DATABASE_PATH,
                            jira_id=selected_jira_id,
                            user_name=current_user
                        )
                    else:
                        # Get specific test type for this JIRA ID
                        test_cases_data = db.get_test_cases_by_filters(
                            db.DATABASE_PATH,
                            jira_id=selected_jira_id,
                            test_type=selected_test_type,
                            user_name=current_user
                        )

                    if not test_cases_data.empty:
                        df_all = test_cases_data
                        selected_data_source = "jira"
                        st.success(f"Loaded {len(df_all)} test cases for JIRA ID: {selected_jira_id}")
                    else:
                        st.error(f"No test cases found for JIRA ID: {selected_jira_id}")
        else:
            st.warning("No JIRA IDs found in the database. Generate some test cases first.")

    # Tab 2: By Test Run
    with data_source_tabs[1]:
        # Get all test runs for the current user
        try:
            test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)
            if test_runs is None:
                test_runs = []
        except Exception as e:
            st.error(f"Error retrieving test runs: {str(e)}")
            test_runs = []

        if test_runs:
            # Create a dataframe for better display
            test_runs_df = pd.DataFrame(test_runs)

            # Add a timestamp column if it exists
            if 'timestamp' in test_runs_df.columns:
                test_runs_df['timestamp'] = pd.to_datetime(test_runs_df['timestamp'])
                test_runs_df = test_runs_df.sort_values('timestamp', ascending=False)

            # Format the dataframe for display
            display_runs = test_runs_df[['id', 'jira_id', 'test_type', 'timestamp', 'num_test_cases', 'status', 'notes']].copy()

            # Format the timestamp
            if 'timestamp' in display_runs.columns:
                display_runs['timestamp'] = pd.to_datetime(display_runs['timestamp']).dt.strftime("%b %d, %Y %H:%M")

            # Rename columns for better display
            display_runs.columns = ['Run ID', 'JIRA ID', 'Test Type', 'Timestamp', '# Test Cases', 'Status', 'Notes']

            # Convert test type to uppercase
            display_runs['Test Type'] = display_runs['Test Type'].str.upper()

            # Display the test runs with better formatting
            st.dataframe(
                display_runs,
                use_container_width=True,
                column_config={
                    "Run ID": st.column_config.NumberColumn("Run ID", width="small"),
                    "JIRA ID": st.column_config.TextColumn("JIRA ID", width="small"),
                    "Test Type": st.column_config.TextColumn("Test Type", width="small"),
                    "Timestamp": st.column_config.TextColumn("Created", width="medium"),
                    "# Test Cases": st.column_config.NumberColumn("# Test Cases", width="small"),
                    "Status": st.column_config.TextColumn("Status", width="small"),
                    "Notes": st.column_config.TextColumn("Notes", width="medium")
                },
                hide_index=True
            )

            # Format the timestamp for better readability
            if 'timestamp' in test_runs_df.columns:
                test_runs_df['formatted_timestamp'] = pd.to_datetime(test_runs_df['timestamp']).dt.strftime("%b %d, %Y %H:%M")
            else:
                test_runs_df['formatted_timestamp'] = "Unknown"

            # Create a selectbox for test runs with better formatting
            run_options = [
                f"Run {run['id']}: {run['jira_id']} - {run['test_type'].upper()} ({run['formatted_timestamp']})"
                for _, run in test_runs_df.iterrows()
            ]

            selected_run_option = st.selectbox(
                "Select Test Run",
                options=run_options,
                key="viz_test_run_select"
            )

            # Extract the run ID from the selected option
            if selected_run_option:
                selected_run_id = int(selected_run_option.split(":")[0].replace("Run ", "").strip())

            # Apply button
            if st.button("Show Visualizations for Selected Test Run", key="viz_run_apply", use_container_width=True):
                with st.spinner("Loading data..."):
                    # Get test cases for the selected test run
                    test_cases_data = db.get_test_cases_by_test_run(db.DATABASE_PATH, selected_run_id)

                    if isinstance(test_cases_data, list) and len(test_cases_data) > 0:
                        df_all = pd.DataFrame(test_cases_data)
                        selected_data_source = "run"
                        st.success(f"Loaded {len(df_all)} test cases for Test Run ID: {selected_run_id}")
                    elif hasattr(test_cases_data, 'empty') and not test_cases_data.empty:
                        df_all = test_cases_data
                        selected_data_source = "run"
                        st.success(f"Loaded {len(df_all)} test cases for Test Run ID: {selected_run_id}")
                    else:
                        st.error(f"No test cases found for Test Run ID: {selected_run_id}")
        else:
            st.warning("No test runs found in the database. Generate some test cases first.")

    # Tab 3: All Data
    with data_source_tabs[2]:
        st.markdown("This view shows visualizations for all your test cases across all JIRA IDs and test runs.")

        # Add date range filter
        default_start_date = datetime.now() - timedelta(days=90)
        default_end_date = datetime.now()
        date_range = st.date_input(
            "Date Range",
            value=(default_start_date, default_end_date),
            key="viz_all_date_range"
        )

        # Apply button
        if st.button("Show Visualizations for All Data", key="viz_all_apply", use_container_width=True):
            with st.spinner("Loading all test case data..."):
                # Convert date range to string format
                start_date = date_range[0].strftime("%Y-%m-%d") if isinstance(date_range, tuple) and len(date_range) > 0 else None
                end_date = date_range[1].strftime("%Y-%m-%d") if isinstance(date_range, tuple) and len(date_range) > 1 else None

                # Get all test cases within the date range
                all_data = db.get_test_cases_by_filters(
                    db.DATABASE_PATH,
                    start_date=start_date,
                    end_date=end_date,
                    user_name=current_user
                )

                if not all_data.empty:
                    df_all = all_data
                    selected_data_source = "all"
                    st.success(f"Loaded {len(df_all)} test cases from {start_date} to {end_date}")
                else:
                    st.error("No test cases found for the selected date range.")

    # Add a divider
    st.markdown("---")

    # Check if we have data to visualize
    if not df_all.empty:
        # Display summary metrics at the top
        st.markdown("### 📊 Key Metrics")

        # Create metrics row
        metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)

        # Calculate metrics
        try:
            # Handle different DataFrame structures
            if "Test Case ID" in df_all.columns:
                # If it's a Series, use unique() method
                if isinstance(df_all["Test Case ID"].dropna(), pd.Series):
                    total_test_cases = len(df_all["Test Case ID"].dropna().unique())
                else:
                    # If it's not a Series, convert to list and use set for uniqueness
                    total_test_cases = len(set([x for x in df_all["Test Case ID"].dropna() if x]))
            else:
                total_test_cases = 0

            if "User Story ID" in df_all.columns:
                # If it's a Series, use unique() method
                if isinstance(df_all["User Story ID"].dropna(), pd.Series):
                    unique_jira_ids = len(df_all["User Story ID"].dropna().unique())
                else:
                    # If it's not a Series, convert to list and use set for uniqueness
                    unique_jira_ids = len(set([x for x in df_all["User Story ID"].dropna() if x]))
            else:
                unique_jira_ids = 0

            # Test status metrics
            if "Test Status" in df_all.columns:
                pass_count = len(df_all[df_all["Test Status"] == "Pass"])
                pass_rate = int((pass_count / total_test_cases) * 100) if total_test_cases > 0 else 0
            else:
                pass_count = 0
                pass_rate = 0

            # Priority metrics
            if "Priority" in df_all.columns:
                if isinstance(df_all[df_all["Priority"] == "High"].dropna(), pd.Series):
                    high_priority = len(df_all[df_all["Priority"] == "High"].dropna().unique())
                else:
                    high_priority = len([x for x in df_all[df_all["Priority"] == "High"].dropna() if x])
            else:
                high_priority = 0
        except Exception as e:
            st.error(f"Error calculating metrics: {str(e)}")
            total_test_cases = 0
            unique_jira_ids = 0
            pass_count = 0
            pass_rate = 0
            high_priority = 0

        # Display metrics
        with metric_col1:
            st.metric("Total Test Cases", total_test_cases)

        with metric_col2:
            st.metric("Pass Rate", f"{pass_rate}%", delta=f"{pass_count}/{total_test_cases}" if total_test_cases > 0 else None)

        with metric_col3:
            st.metric("High Priority", high_priority)

        with metric_col4:
            st.metric("User Stories", unique_jira_ids)

        # Add context information
        if selected_data_source == "jira":
            st.info(f"Showing visualizations for JIRA ID: **{selected_jira_id}**")
        elif selected_data_source == "run":
            st.info(f"Showing visualizations for Test Run ID: **{selected_run_id}**")
        elif selected_data_source == "all":
            st.info(f"Showing visualizations for all test cases from **{date_range[0].strftime('%Y-%m-%d')}** to **{date_range[1].strftime('%Y-%m-%d')}**")

        # Create tabs for different visualizations
        viz_tabs = st.tabs(["Test Type Distribution", "JIRA Coverage", "Test Status", "Priority Distribution", "Timeline Analysis"])

        # Tab 1: Test Type Distribution
        with viz_tabs[0]:
            st.markdown("### Test Case Distribution by Type")

            if "Test Type" in df_all.columns:
                try:
                    # Clean and standardize test type values
                    df_all["Test Type"] = df_all["Test Type"].fillna("Unknown")

                    # Count test cases by test type
                    if isinstance(df_all["Test Type"], pd.Series):
                        test_type_counts = df_all["Test Type"].value_counts().reset_index()
                        test_type_counts.columns = ["Test Type", "Count"]
                    else:
                        # Manual counting if not a Series
                        type_counts = {}
                        for test_type in df_all["Test Type"]:
                            if pd.isna(test_type):
                                test_type = "Unknown"
                            type_counts[test_type] = type_counts.get(test_type, 0) + 1

                        # Convert to DataFrame
                        test_type_counts = pd.DataFrame({
                            "Test Type": list(type_counts.keys()),
                            "Count": list(type_counts.values())
                        })
                except Exception as e:
                    st.error(f"Error processing test type data: {str(e)}")
                    # Create an empty DataFrame with the right columns
                    test_type_counts = pd.DataFrame(columns=["Test Type", "Count"])

                # Define colors for test types
                type_colors = {
                    "positive": "#4CAF50",
                    "negative": "#F44336",
                    "security": "#2196F3",
                    "performance": "#FF9800",
                    "Unknown": "#9E9E9E"
                }

                # Create a pie chart
                fig = px.pie(
                    test_type_counts,
                    values="Count",
                    names="Test Type",
                    title="Test Cases by Type",
                    color="Test Type",
                    color_discrete_map=type_colors,
                    hole=0.4
                )

                # Add hover information
                fig.update_traces(
                    textposition='inside',
                    textinfo='percent+label',
                    hoverinfo='label+percent+value',
                    marker=dict(line=dict(color='#FFFFFF', width=2))
                )

                # Improve layout
                fig.update_layout(
                    height=500,
                    legend_title_text="Test Types",
                    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                    margin=dict(t=60, b=20, l=20, r=20)
                )

                # Display the chart
                st.plotly_chart(fig, use_container_width=True)

                # Add a bar chart showing the same data for comparison
                bar_fig = px.bar(
                    test_type_counts,
                    x="Test Type",
                    y="Count",
                    color="Test Type",
                    color_discrete_map=type_colors,
                    title="Test Cases by Type"
                )

                # Improve bar chart layout
                bar_fig.update_layout(
                    height=400,
                    xaxis_title="Test Type",
                    yaxis_title="Number of Test Cases",
                    legend_title_text="Test Types",
                    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
                )

                # Display the bar chart
                st.plotly_chart(bar_fig, use_container_width=True)
            else:
                st.warning("No Test Type data available for visualization.")

        # Tab 2: JIRA Coverage
        with viz_tabs[1]:
            st.markdown("### JIRA Coverage Analysis")

            if "User Story ID" in df_all.columns:
                try:
                    # Count test cases by JIRA ID
                    if isinstance(df_all["User Story ID"], pd.Series):
                        jira_counts = df_all["User Story ID"].value_counts().reset_index()
                        jira_counts.columns = ["JIRA ID", "Count"]
                    else:
                        # Manual counting if not a Series
                        jira_id_counts = {}
                        for jira_id in df_all["User Story ID"]:
                            if pd.notna(jira_id):
                                jira_id_counts[jira_id] = jira_id_counts.get(jira_id, 0) + 1

                        # Convert to DataFrame
                        jira_counts = pd.DataFrame({
                            "JIRA ID": list(jira_id_counts.keys()),
                            "Count": list(jira_id_counts.values())
                        })

                    # Sort by count (descending)
                    jira_counts = jira_counts.sort_values("Count", ascending=False)
                except Exception as e:
                    st.error(f"Error processing JIRA ID data: {str(e)}")
                    # Create an empty DataFrame with the right columns
                    jira_counts = pd.DataFrame(columns=["JIRA ID", "Count"])

                # Limit to top 10 if there are more than 10 JIRA IDs
                if len(jira_counts) > 10:
                    jira_counts = jira_counts.head(10)
                    title = "Top 10 JIRA IDs by Test Case Count"
                else:
                    title = "JIRA IDs by Test Case Count"

                # Create a horizontal bar chart
                fig = px.bar(
                    jira_counts,
                    y="JIRA ID",
                    x="Count",
                    color="Count",
                    color_continuous_scale="Viridis",
                    title=title,
                    orientation="h"
                )

                # Improve layout
                fig.update_layout(
                    height=500,
                    xaxis_title="Number of Test Cases",
                    yaxis_title="JIRA ID",
                    yaxis=dict(autorange="reversed"),  # Reverse y-axis to show highest count at top
                    coloraxis_showscale=False  # Hide color scale
                )

                # Display the chart
                st.plotly_chart(fig, use_container_width=True)

                # If we have test type data, show JIRA coverage by test type
                if "Test Type" in df_all.columns:
                    st.markdown("### JIRA Coverage by Test Type")

                    try:
                        # Check if we can use groupby (requires Series)
                        if isinstance(df_all["User Story ID"], pd.Series) and isinstance(df_all["Test Type"], pd.Series):
                            # Group by JIRA ID and Test Type
                            jira_type_counts = df_all.groupby(["User Story ID", "Test Type"]).size().reset_index(name="Count")
                        else:
                            # Manual counting
                            jira_type_dict = {}
                            for i in range(len(df_all)):
                                jira_id = df_all["User Story ID"].iloc[i] if hasattr(df_all["User Story ID"], "iloc") else df_all["User Story ID"][i]
                                test_type = df_all["Test Type"].iloc[i] if hasattr(df_all["Test Type"], "iloc") else df_all["Test Type"][i]

                                if pd.isna(jira_id) or pd.isna(test_type):
                                    continue

                                key = (jira_id, test_type)
                                jira_type_dict[key] = jira_type_dict.get(key, 0) + 1

                            # Convert to DataFrame
                            jira_type_data = []
                            for (jira_id, test_type), count in jira_type_dict.items():
                                jira_type_data.append({
                                    "User Story ID": jira_id,
                                    "Test Type": test_type,
                                    "Count": count
                                })

                            jira_type_counts = pd.DataFrame(jira_type_data)
                    except Exception as e:
                        st.error(f"Error processing JIRA coverage by test type: {str(e)}")
                        # Create an empty DataFrame with the right columns
                        jira_type_counts = pd.DataFrame(columns=["User Story ID", "Test Type", "Count"])

                    # Create a grouped bar chart
                    fig = px.bar(
                        jira_type_counts,
                        x="User Story ID",
                        y="Count",
                        color="Test Type",
                        color_discrete_map=type_colors,
                        title="Test Cases by JIRA ID and Test Type",
                        barmode="group"
                    )

                    # Improve layout
                    fig.update_layout(
                        height=500,
                        xaxis_title="JIRA ID",
                        yaxis_title="Number of Test Cases",
                        legend_title_text="Test Type",
                        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
                    )

                    # Display the chart
                    st.plotly_chart(fig, use_container_width=True)
            else:
                st.warning("No JIRA ID data available for visualization.")

        # Tab 3: Test Status
        with viz_tabs[2]:
            st.markdown("### Test Case Status Analysis")

            if "Test Status" in df_all.columns:
                try:
                    # Clean and standardize test status values
                    df_all["Test Status"] = df_all["Test Status"].fillna("Not Run")

                    # Count test cases by status
                    if isinstance(df_all["Test Status"], pd.Series):
                        status_counts = df_all["Test Status"].value_counts().reset_index()
                        status_counts.columns = ["Status", "Count"]
                    else:
                        # Manual counting if not a Series
                        status_count_dict = {}
                        for status in df_all["Test Status"]:
                            if pd.isna(status):
                                status = "Not Run"
                            status_count_dict[status] = status_count_dict.get(status, 0) + 1

                        # Convert to DataFrame
                        status_counts = pd.DataFrame({
                            "Status": list(status_count_dict.keys()),
                            "Count": list(status_count_dict.values())
                        })
                except Exception as e:
                    st.error(f"Error processing test status data: {str(e)}")
                    # Create an empty DataFrame with the right columns
                    status_counts = pd.DataFrame(columns=["Status", "Count"])

                # Define colors for test status
                status_colors = {
                    "Pass": "#4CAF50",
                    "Fail": "#F44336",
                    "Blocked": "#FF9800",
                    "Not Run": "#9E9E9E"
                }

                # Create a bar chart
                fig = px.bar(
                    status_counts,
                    x="Status",
                    y="Count",
                    color="Status",
                    color_discrete_map=status_colors,
                    title="Test Case Status Distribution"
                )

                # Improve layout
                fig.update_layout(
                    height=400,
                    xaxis_title="Test Status",
                    yaxis_title="Number of Test Cases",
                    legend_title_text="Status",
                    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
                )

                # Display the chart
                st.plotly_chart(fig, use_container_width=True)

                # Calculate pass rate
                try:
                    total_cases = status_counts["Count"].sum()
                    pass_count = status_counts[status_counts["Status"] == "Pass"]["Count"].sum() if "Pass" in status_counts["Status"].values else 0
                    pass_rate = (pass_count / total_cases) * 100 if total_cases > 0 else 0
                except Exception as e:
                    st.error(f"Error calculating pass rate: {str(e)}")
                    total_cases = 0
                    pass_count = 0
                    pass_rate = 0

                # Create a gauge chart for pass rate
                gauge_fig = go.Figure(go.Indicator(
                    mode="gauge+number",
                    value=pass_rate,
                    title={"text": "Pass Rate (%)"},
                    gauge={
                        "axis": {"range": [0, 100]},
                        "bar": {"color": "#4CAF50"},
                        "steps": [
                            {"range": [0, 50], "color": "#F44336"},
                            {"range": [50, 80], "color": "#FF9800"},
                            {"range": [80, 100], "color": "#4CAF50"}
                        ],
                        "threshold": {
                            "line": {"color": "black", "width": 4},
                            "thickness": 0.75,
                            "value": 80
                        }
                    }
                ))

                # Improve gauge layout
                gauge_fig.update_layout(
                    height=400,
                    margin=dict(t=40, b=0, l=40, r=40)
                )

                # Display the gauge chart
                st.plotly_chart(gauge_fig, use_container_width=True)

                # If we have test type data, show status by test type
                if "Test Type" in df_all.columns:
                    st.markdown("### Test Status by Test Type")

                    try:
                        # Check if we can use crosstab (requires Series)
                        if isinstance(df_all["Test Type"], pd.Series) and isinstance(df_all["Test Status"], pd.Series):
                            # Create a cross-tabulation of Test Type vs Test Status
                            status_by_type = pd.crosstab(
                                df_all["Test Type"],
                                df_all["Test Status"]
                            )
                        else:
                            # Manual cross-tabulation
                            status_type_dict = {}
                            for i in range(len(df_all)):
                                test_type = df_all["Test Type"].iloc[i] if hasattr(df_all["Test Type"], "iloc") else df_all["Test Type"][i]
                                test_status = df_all["Test Status"].iloc[i] if hasattr(df_all["Test Status"], "iloc") else df_all["Test Status"][i]

                                if pd.isna(test_type):
                                    test_type = "Unknown"
                                if pd.isna(test_status):
                                    test_status = "Not Run"

                                if test_type not in status_type_dict:
                                    status_type_dict[test_type] = {}

                                status_type_dict[test_type][test_status] = status_type_dict[test_type].get(test_status, 0) + 1

                            # Get unique test types and statuses
                            test_types = list(status_type_dict.keys())
                            test_statuses = set()
                            for type_dict in status_type_dict.values():
                                test_statuses.update(type_dict.keys())
                            test_statuses = list(test_statuses)

                            # Create DataFrame
                            data = {}
                            for status in test_statuses:
                                data[status] = [status_type_dict.get(test_type, {}).get(status, 0) for test_type in test_types]

                            status_by_type = pd.DataFrame(data, index=test_types)
                    except Exception as e:
                        st.error(f"Error processing test status by test type: {str(e)}")
                        # Create an empty DataFrame
                        status_by_type = pd.DataFrame()

                    # Create heatmap
                    heatmap_fig = px.imshow(
                        status_by_type,
                        labels=dict(x="Test Status", y="Test Type", color="Count"),
                        x=status_by_type.columns,
                        y=status_by_type.index,
                        color_continuous_scale="Viridis",
                        title="Test Status by Test Type"
                    )

                    # Add text annotations
                    heatmap_fig.update_traces(text=status_by_type.values, texttemplate="%{text}")

                    # Improve layout
                    heatmap_fig.update_layout(
                        height=400,
                        margin=dict(t=60, b=40, l=40, r=40)
                    )

                    # Display the heatmap
                    st.plotly_chart(heatmap_fig, use_container_width=True)
            else:
                st.warning("No Test Status data available for visualization.")

        # Tab 4: Priority Distribution
        with viz_tabs[3]:
            st.markdown("### Test Case Priority Distribution")

            if "Priority" in df_all.columns:
                # Clean and standardize priority values
                df_all["Priority"] = df_all["Priority"].fillna("Medium")

                # Count test cases by priority
                priority_counts = df_all["Priority"].value_counts().reset_index()
                priority_counts.columns = ["Priority", "Count"]

                # Define colors for priority levels
                priority_colors = {"High": "#F44336", "Medium": "#FF9800", "Low": "#4CAF50"}

                # Create a donut chart
                fig = px.pie(
                    priority_counts,
                    values="Count",
                    names="Priority",
                    title="Test Case Priority Distribution",
                    color="Priority",
                    color_discrete_map=priority_colors,
                    hole=0.6
                )

                # Add hover information
                fig.update_traces(
                    textposition='inside',
                    textinfo='percent+label',
                    hoverinfo='label+percent+value',
                    marker=dict(line=dict(color='#FFFFFF', width=2))
                )

                # Improve layout
                fig.update_layout(
                    height=500,
                    legend_title_text="Priority",
                    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                    margin=dict(t=60, b=20, l=20, r=20)
                )

                # Display the chart
                st.plotly_chart(fig, use_container_width=True)

                # Display priority counts as metrics
                col1, col2, col3 = st.columns(3)

                # Calculate percentages
                total = priority_counts["Count"].sum()

                with col1:
                    high_count = priority_counts[priority_counts["Priority"] == "High"]["Count"].sum() if "High" in priority_counts["Priority"].values else 0
                    high_pct = (high_count / total) * 100 if total > 0 else 0
                    st.metric("High Priority", high_count, f"{high_pct:.1f}%")

                with col2:
                    medium_count = priority_counts[priority_counts["Priority"] == "Medium"]["Count"].sum() if "Medium" in priority_counts["Priority"].values else 0
                    medium_pct = (medium_count / total) * 100 if total > 0 else 0
                    st.metric("Medium Priority", medium_count, f"{medium_pct:.1f}%")

                with col3:
                    low_count = priority_counts[priority_counts["Priority"] == "Low"]["Count"].sum() if "Low" in priority_counts["Priority"].values else 0
                    low_pct = (low_count / total) * 100 if total > 0 else 0
                    st.metric("Low Priority", low_count, f"{low_pct:.1f}%")

                # If we have test type data, show priority by test type
                if "Test Type" in df_all.columns:
                    st.markdown("### Priority by Test Type")

                    # Create a cross-tabulation of Test Type vs Priority
                    priority_by_type = pd.crosstab(
                        df_all["Test Type"],
                        df_all["Priority"]
                    )

                    # Create a stacked bar chart
                    stacked_fig = px.bar(
                        priority_by_type.reset_index().melt(id_vars=["Test Type"], var_name="Priority", value_name="Count"),
                        x="Test Type",
                        y="Count",
                        color="Priority",
                        color_discrete_map=priority_colors,
                        title="Test Case Priority by Type",
                        barmode="stack"
                    )

                    # Improve layout
                    stacked_fig.update_layout(
                        height=400,
                        xaxis_title="Test Type",
                        yaxis_title="Number of Test Cases",
                        legend_title_text="Priority",
                        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
                    )

                    # Display the chart
                    st.plotly_chart(stacked_fig, use_container_width=True)
            else:
                st.warning("No Priority data available for visualization.")

        # Tab 5: Timeline Analysis
        with viz_tabs[4]:
            st.markdown("### Test Case Timeline Analysis")

            if "Timestamp" in df_all.columns:
                # Convert timestamp to datetime if it's not already
                if not pd.api.types.is_datetime64_any_dtype(df_all["Timestamp"]):
                    df_all["Timestamp"] = pd.to_datetime(df_all["Timestamp"], errors='coerce')

                # Extract date from timestamp
                df_all["Date"] = df_all["Timestamp"].dt.date

                # Count test cases by date
                timeline_data = df_all.groupby("Date").size().reset_index(name="Count")

                # Sort by date
                timeline_data = timeline_data.sort_values("Date")

                # Calculate cumulative count
                timeline_data["Cumulative"] = timeline_data["Count"].cumsum()

                # Create a subplot with two charts
                fig = make_subplots(
                    rows=2,
                    cols=1,
                    subplot_titles=("Daily Test Case Creation", "Cumulative Test Cases Over Time"),
                    vertical_spacing=0.15,
                    specs=[[{"type": "scatter"}], [{"type": "scatter"}]]
                )

                # Add daily count line chart
                fig.add_trace(
                    go.Scatter(
                        x=timeline_data["Date"],
                        y=timeline_data["Count"],
                        mode="lines+markers",
                        name="Daily Count",
                        line=dict(color="#1E88E5", width=2),
                        marker=dict(size=8, color="#1E88E5"),
                        hovertemplate="Date: %{x}<br>Test Cases: %{y}<extra></extra>"
                    ),
                    row=1, col=1
                )

                # Add cumulative line chart
                fig.add_trace(
                    go.Scatter(
                        x=timeline_data["Date"],
                        y=timeline_data["Cumulative"],
                        mode="lines",
                        name="Cumulative",
                        line=dict(color="#4CAF50", width=3),
                        fill="tozeroy",
                        fillcolor="rgba(76, 175, 80, 0.2)",
                        hovertemplate="Date: %{x}<br>Total Test Cases: %{y}<extra></extra>"
                    ),
                    row=2, col=1
                )

                # Update layout
                fig.update_layout(
                    height=700,
                    hovermode="x unified",
                    showlegend=False,
                    title_text="Test Case Creation Timeline",
                    title_x=0.5,
                    title_font=dict(size=20)
                )

                # Update x and y axis labels
                fig.update_xaxes(title_text="Date", row=1, col=1)
                fig.update_yaxes(title_text="Number of Test Cases", row=1, col=1)
                fig.update_xaxes(title_text="Date", row=2, col=1)
                fig.update_yaxes(title_text="Cumulative Test Cases", row=2, col=1)

                # Display the chart
                st.plotly_chart(fig, use_container_width=True)

                # Add test case creation by test type over time
                if "Test Type" in df_all.columns:
                    st.markdown("### Test Case Creation by Type Over Time")

                    # Group by date and test type
                    type_timeline = df_all.groupby(["Date", "Test Type"]).size().reset_index(name="Count")

                    # Create a stacked area chart
                    area_fig = px.area(
                        type_timeline,
                        x="Date",
                        y="Count",
                        color="Test Type",
                        color_discrete_map=type_colors,
                        title="Test Case Creation by Type Over Time"
                    )

                    # Update layout
                    area_fig.update_layout(
                        height=500,
                        hovermode="x unified",
                        xaxis_title="Date",
                        yaxis_title="Number of Test Cases",
                        legend_title_text="Test Type",
                        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
                    )

                    # Display the chart
                    st.plotly_chart(area_fig, use_container_width=True)
            else:
                st.warning("No timestamp data available for timeline analysis.")
    else:
        st.warning("No test case data available for visualization. Generate some test cases first.")
