"""
AI-powered test case to step table conversion functionality.

This module handles the conversion of test cases to automation-ready step tables
using Google AI, including validation and enhancement of the conversion results.
"""

import os
import json
import time
import uuid
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

# Import helper functions and classes from ai_helpers.py
from .ai_helpers import (
    extract_json_from_response, extract_markdown_from_response,
    json_to_markdown_table, markdown_table_to_json, error_handler
)

# Get logger
logger = logging.getLogger("ScriptWeaver.core.ai_conversion")


def generate_step_description(step_data: Dict[str, Any]) -> str:
    """
    Generate a human-readable description for a step based on its data.

    Args:
        step_data: Dictionary containing step information

    Returns:
        str: Human-readable description of the step
    """
    try:
        step_type = step_data.get('step_type', 'ui')
        action = step_data.get('action', '')
        locator = step_data.get('locator', '')
        test_data_param = step_data.get('test_data_param', '')
        expected_result = step_data.get('expected_result', '')

        # Generate description based on step type and action
        if step_type == 'ui':
            if action == 'navigate':
                if 'http' in locator.lower():
                    return f"Navigate to the URL: {locator}"
                else:
                    return f"Navigate to the {locator} page"
            elif action == 'click':
                return f"Click the {locator} element"
            elif action == 'type':
                if test_data_param:
                    return f"Enter {test_data_param} in the {locator} field"
                else:
                    return f"Enter text in the {locator} field"
            elif action == 'select':
                return f"Select option from the {locator} dropdown"
            elif action == 'wait_for_element':
                return f"Wait for the {locator} element to appear"
            elif action == 'upload_file':
                return f"Upload file using the {locator} element"
            else:
                return f"Perform {action} action on {locator}"

        elif step_type == 'api':
            if action.startswith('api_'):
                method = action.replace('api_', '').upper()
                return f"Make {method} API call to {locator or 'endpoint'}"
            else:
                return f"Execute API {action}"

        elif step_type == 'setup':
            return f"Setup: {action} for test preparation"

        elif step_type == 'teardown':
            return f"Cleanup: {action} after test completion"

        elif step_type == 'assertion':
            return f"Verify that {expected_result}"

        elif step_type == 'data':
            return f"Data operation: {action}"

        else:
            # Generic fallback
            if action and expected_result:
                return f"{action.capitalize()} and verify {expected_result}"
            elif action:
                return f"{action.capitalize()}"
            else:
                return "Execute test step"

    except Exception as e:
        logger.warning(f"Error generating step description: {e}")
        return f"Execute {step_data.get('action', 'test')} step"


def ensure_step_descriptions(step_data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Ensure all steps in the list have step_description field.
    Generate descriptions for steps that don't have them.

    Args:
        step_data_list: List of step dictionaries

    Returns:
        List of step dictionaries with step_description field
    """
    updated_steps = []

    for step in step_data_list:
        if not isinstance(step, dict):
            updated_steps.append(step)
            continue

        # Create a copy to avoid modifying the original
        updated_step = step.copy()

        # Generate description if missing
        if 'step_description' not in updated_step or not updated_step['step_description']:
            updated_step['step_description'] = generate_step_description(updated_step)
            logger.info(f"Generated description for step {updated_step.get('step_no', 'unknown')}: {updated_step['step_description']}")

        updated_steps.append(updated_step)

    return updated_steps


@error_handler
def convert_test_case_to_step_table(test_case_json, api_key=None, website_url=None):
    """
    Convert a test case JSON to an automation-ready step table using Google AI.

    Args:
        test_case_json (dict): The test case JSON to convert
        api_key (str, optional): API key for Google AI. If None, use initialized client
        website_url (str, optional): Base website URL from Stage 2 for first step navigation

    Returns:
        tuple: (markdown_table, json_table) where:
            - markdown_table (str): The automation-ready step table in markdown format
            - json_table (list): The step table as a list of dictionaries
    """
    # Import here to avoid circular imports
    from .ai import generate_llm_response, log_ai_interaction

    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Validate input
    if not test_case_json or not isinstance(test_case_json, dict):
        logger.warning(f"Invalid test case JSON provided [Request ID: {request_id}]")
        return "Error: Invalid test case JSON provided.", []

    # Create the prompt with the test case JSON
    json_input = json.dumps(test_case_json, indent=2)
    prompt = """
Role: You are a senior QA-automation engineer.

Convert the test-case JSON that follows into an **automation-ready step table**.

____________________________________
INPUT JSON
%s
____________________________________

OUTPUT FORMAT
I need the data in JSON format first, followed by a markdown table representation:

1. First, provide a JSON array of objects, where each object represents a step with these exact keys (in this order):
   "step_no", "step_type", "action", "locator_strategy", "locator", "test_data_param", "expected_result", "assertion_type", "condition", "timeout", "step_description"

2. Second, provide the same data as a Markdown table with **one row per step** and these exact columns (in this order):
   | Step No | Step Type | Action | Locator Strategy | Locator | Test Data Param | Expected Result | Assertion Type | Condition | Timeout (s) | Description |

**Instructions**

1. **step_type** – One of:
      - `setup` (e.g. API or DB calls to prepare state)
      - `ui` (interact with the user interface)
      - `api` (direct HTTP/API calls)
      - `data` (data setup or teardown)
      - `assertion` (verifications without UI interactions)
      - `teardown` (cleanup steps)
2. **action** – Convert each test step into an imperative verb:
      - UI: `navigate`, `click`, `type`, `select`, `upload_file`, `wait_for_element`, etc.
      - API: `api_get`, `api_post`, `api_delete`, etc.
      - Data: `db_insert`, `db_delete`, etc.
3. **locator_strategy** – For UI steps choose `css`, `xpath`, `id`, `name`, `aria`, or leave blank for non-UI steps.
4. **locator** – Draft a plausible selector (e.g. `#login-button`, `//input[@name="q"]`) or leave blank if not applicable.
5. **test_data_param** – Placeholder inputs in `{{double_curly}}` form, e.g. `{{username}}`. For loops, set `action` to `for_each` and this to the collection variable.
6. **expected_result** – Succinct description (≤ 10 words), e.g. `dashboard_page`, `201_created`.
7. **assertion_type** – Oracle keywords such as `url_equals`, `element_visible`, `text_equals`, `status_code`, `no_error`.
8. **condition** – If the step runs conditionally, put a boolean expression (e.g. `element_visible("#error")`); otherwise, an empty string.
9. **timeout** – Maximum wait time in seconds (default UI = 10, API = 30).
10. **step_description** – A concise, human-readable sentence describing what the step does (e.g., "Navigate to the login page", "Enter username in the username field", "Click the login button").
11. Keep the original **step_no** and preserve step order.
12. **Do not** add any commentary between the JSON and the Markdown outputs.

Format your response exactly like this:

```json
[
  {
    "step_no": "1",
    "step_type": "setup",
    "action": "api_post",
    "locator_strategy": "",
    "locator": "",
    "test_data_param": "{api_base_url}/create-user",
    "expected_result": "201_created",
    "assertion_type": "status_code",
    "condition": "",
    "timeout": 30,
    "step_description": "Create a new user account via API call"
  }
]
```

```markdown
| Step No | Step Type | Action | Locator Strategy | Locator | Test Data Param | Expected Result | Assertion Type | Condition | Timeout (s) | Description |
|---------|-----------|--------|------------------|---------|-----------------|-----------------|----------------|-----------|-------------|-------------|
| 1 | setup | api_post | | | {api_base_url}/create-user | 201_created | status_code | | 30 | Create a new user account via API call |
```

IMPORTANT: The JSON output must come first, followed by the markdown table. Both outputs must be immediately consumable by a PyTest/Selenium generator.
"""%json_input

    # Create context information for this request
    context = {
        'test_case_id': test_case_json.get('id', 'unknown'),
        'test_case_name': test_case_json.get('name', 'unknown'),
        'step_count': len(test_case_json.get('steps', [])),
        'operation': 'convert_to_step_table'
    }

    # Log the prompt for step table generation separately with enhanced prompt generation tracing
    _, prompt_request_id = log_ai_interaction(
        function_name="convert_test_case_to_step_table_prompt",
        prompt=prompt,
        response="See generate_llm_response log for the response",
        model_name="gemini-1.5-flash",
        capture_stack=True,
        is_prompt_generation=True,  # Enable prompt generation tracing
        context=context
    )

    # Use generate_llm_response with enhanced logging and call stack tracing
    logger.info(f"Converting test case to step table [Request ID: {request_id}]")
    response_text = generate_llm_response(
        prompt=prompt,
        model_name="gemini-1.5-flash",
        api_key=api_key,
        context=context,
        category="step_table_generation",
        parent_request_id=None,
        related_request_ids=[prompt_request_id]  # Link to the prompt log
    )

    # Calculate processing time for parsing
    parsing_start_time = time.time()

    # Extract JSON and markdown from the response
    json_table = extract_json_from_response(response_text, request_id)
    markdown_table = extract_markdown_from_response(response_text, request_id)

    # If we have JSON but no markdown table, generate the markdown table from JSON
    if json_table and not markdown_table:
        markdown_table = json_to_markdown_table(json_table)
        logger.info(f"Generated markdown table from JSON [Request ID: {request_id}]")

    # If we have markdown table but no JSON, generate JSON from the markdown table
    if not json_table and markdown_table:
        json_table = markdown_table_to_json(markdown_table)
        logger.info(f"Created JSON from markdown table with {len(json_table)} entries [Request ID: {request_id}]")

    # Calculate parsing time
    parsing_end_time = time.time()
    parsing_time_ms = (parsing_end_time - parsing_start_time) * 1000

    # Calculate total processing time
    total_time_ms = (parsing_end_time - start_time) * 1000

    # Log the parsing results with detailed metrics
    parsing_context = {
        'test_case_id': test_case_json.get('id', 'unknown'),
        'json_steps_found': len(json_table),
        'markdown_table_found': bool(markdown_table),
        'parsing_time_ms': parsing_time_ms,
        'total_processing_time_ms': total_time_ms
    }

    # Log the parsing results
    log_ai_interaction(
        function_name="parse_step_table_results",
        prompt="",  # No prompt for this log entry
        response=f"JSON Steps: {len(json_table)}\nMarkdown Table: {'Found' if markdown_table else 'Not Found'}\nParsing Time: {parsing_time_ms:.2f}ms",
        model_name="N/A",
        request_id=str(uuid.uuid4()),
        parent_request_id=request_id,  # Link to the original request
        context=parsing_context,
        input_tokens=0,
        output_tokens=0,
        latency_ms=parsing_time_ms,
        category="step_table_parsing"
    )

    # CRITICAL FIX: Ensure first step always navigates to base URL
    # This ensures test execution starts from a clean, known state
    if json_table and len(json_table) > 0 and website_url:
        logger.info("=== VALIDATING FIRST STEP NAVIGATION ===")
        first_step = json_table[0]

        # Check if first step needs navigation fix
        needs_navigation_fix = _validate_and_fix_first_step_navigation(first_step, website_url, request_id)

        if needs_navigation_fix:
            logger.info(f"✅ First step navigation validated/fixed for base URL: {website_url}")
        else:
            logger.info(f"✅ First step navigation already correct for base URL: {website_url}")

    logger.info(f"Step table conversion completed in {total_time_ms:.2f}ms [Request ID: {request_id}]")
    return markdown_table, json_table


def _validate_and_fix_first_step_navigation(first_step, website_url, request_id):
    """
    Validate and fix the first step to ensure it navigates to the base URL.

    This function ensures that the first step (step_no: "1") always starts from a clean,
    known state by explicitly navigating to the base website URL configured in Stage 2.

    Args:
        first_step (dict): The first step from the converted step table
        website_url (str): The base website URL from Stage 2
        request_id (str): Request ID for logging

    Returns:
        bool: True if the step was modified, False if it was already correct
    """
    logger.info(f"Validating first step navigation [Request ID: {request_id}]")

    step_modified = False
    step_no = first_step.get('step_no', '1')

    # Ensure this is actually the first step
    if str(step_no) != '1':
        logger.warning(f"Expected first step to have step_no='1', but got '{step_no}'")
        return False

    # Check current action
    current_action = first_step.get('action', '').lower()
    logger.info(f"First step current action: '{current_action}'")

    # If the action is not a navigation action, make it one
    navigation_actions = ['navigate', 'go to', 'open', 'visit', 'load', 'browse to']
    if not any(nav_action in current_action for nav_action in navigation_actions):
        logger.info(f"First step action '{current_action}' is not a navigation action, fixing...")
        first_step['action'] = 'navigate'
        first_step['step_description'] = f"Navigate to the application base URL"
        step_modified = True

    # Ensure the test_data_param uses the base URL
    current_param = first_step.get('test_data_param', '')
    logger.info(f"First step current test_data_param: '{current_param}'")

    # Set appropriate test data parameter for base URL
    if not current_param or current_param in ['{{login_url}}', '{{website_url}}', '{{base_url}}', '{{application_url}}']:
        first_step['test_data_param'] = '{{website_url}}'
        step_modified = True
        logger.info("Set first step test_data_param to '{{website_url}}'")

    # Ensure locator strategy is appropriate for navigation
    if first_step.get('locator_strategy') not in ['url', '']:
        first_step['locator_strategy'] = 'url'
        step_modified = True
        logger.info("Set first step locator_strategy to 'url'")

    # Set the locator to the actual website URL for reference
    if first_step.get('locator') != website_url:
        first_step['locator'] = website_url
        step_modified = True
        logger.info(f"Set first step locator to base URL: {website_url}")

    # Ensure appropriate expected result
    current_expected = first_step.get('expected_result', '')
    if not current_expected or 'page' not in current_expected.lower():
        first_step['expected_result'] = 'Application page loads successfully'
        step_modified = True
        logger.info("Set first step expected_result to indicate page loading")

    # Ensure appropriate assertion type
    if first_step.get('assertion_type') not in ['url_contains', 'url_equals', 'page_title']:
        first_step['assertion_type'] = 'url_contains'
        step_modified = True
        logger.info("Set first step assertion_type to 'url_contains'")

    # Initialize URL tracking fields for the first step
    if 'current_url' not in first_step:
        first_step['current_url'] = website_url
        step_modified = True

    if 'url_history' not in first_step:
        first_step['url_history'] = []
        step_modified = True

    if 'step_execution_urls' not in first_step:
        first_step['step_execution_urls'] = {
            'start_url': None,  # No previous URL for first step
            'end_url': website_url,  # Should end at base URL
            'intermediate_urls': []
        }
        step_modified = True

    # Set appropriate timeout for navigation
    if first_step.get('timeout', 0) < 10:
        first_step['timeout'] = 10
        step_modified = True
        logger.info("Set first step timeout to 10 seconds for navigation")

    if step_modified:
        logger.info(f"✅ First step modified to ensure base URL navigation [Request ID: {request_id}]")
        logger.info(f"   Action: {first_step['action']}")
        logger.info(f"   Test Data Param: {first_step['test_data_param']}")
        logger.info(f"   Locator: {first_step['locator']}")
        logger.info(f"   Expected Result: {first_step['expected_result']}")
    else:
        logger.info(f"✅ First step already correctly configured for base URL navigation [Request ID: {request_id}]")

    return step_modified
