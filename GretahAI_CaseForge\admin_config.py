import sqlite3
import os
import hashlib
import secrets
import json
from datetime import datetime
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='admin_config_debug.log',
    filemode='a'
)
logger = logging.getLogger('admin_config')

# Define the database path
DATABASE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_cases_v2.db")
# Using absolute path for CONFIG_FILE
CONFIG_FILE = os.path.join(Path(__file__).parent.parent, "admin_config.json")
logger.info(f"Admin config file path: {CONFIG_FILE}")
logger.info(f"Database path: {DATABASE_PATH}")
logger.info(f"Admin config file exists: {os.path.exists(CONFIG_FILE)}")

def hash_password(password, salt=None):
    """Hash a password with a salt for secure storage."""
    if salt is None:
        salt = secrets.token_hex(16)
    # Combine password and salt, then hash
    hashed = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt.encode('utf-8'),
        100000  # Number of iterations
    ).hex()
    return f"{salt}${hashed}"

def verify_password(stored_password, provided_password):
    """Verify a password against its stored hash or plain text."""
    if not stored_password:
        logger.warning("No stored password provided")
        return False

    logger.info(f"Verifying password: stored_password length={len(stored_password)}, provided_password length={len(provided_password)}")

    # Check if the password is hashed (contains a '$')
    if '$' in stored_password:
        # Split the stored value to get salt and hash
        salt, stored_hash = stored_password.split('$', 1)
        logger.info(f"Password is hashed. Salt length={len(salt)}")

        # Hash the provided password with the same salt
        computed_hash = hashlib.pbkdf2_hmac(
            'sha256',
            provided_password.encode('utf-8'),
            salt.encode('utf-8'),
            100000
        ).hex()

        # Log comparison (safely)
        result = computed_hash == stored_hash
        logger.info(f"Hash comparison result: {result}")
        logger.debug(f"First 8 chars - Computed: {computed_hash[:8]}, Stored: {stored_hash[:8]}")

        return result
    else:
        # For plain text passwords, just compare them directly
        result = stored_password == provided_password
        logger.info(f"Plain text comparison result: {result}")
        return result

def init_config():
    """Initialize the configuration file if it doesn't exist."""
    if not os.path.exists(CONFIG_FILE):
        # Create default admin user
        default_admin = {
            "username": "admin",
            "password": hash_password("admin123"),
            "role": "admin",
            "created_at": datetime.now().isoformat()
        }

        default_config = {
            "users": [default_admin],
            "allow_delete_test_cases": True,
            "allow_clear_database": False,
            "last_updated": datetime.now().isoformat()
        }

        with open(CONFIG_FILE, 'w') as f:
            json.dump(default_config, f, indent=4)

        print(f"Created new configuration file: {CONFIG_FILE}")
    else:
        # Check if the config file has the new user structure
        try:
            with open(CONFIG_FILE, 'r') as f:
                config = json.load(f)

            # If the config doesn't have users, migrate it
            if "users" not in config:
                # Create default admin user from existing admin_password
                admin_password = config.get("admin_password", hash_password("admin123"))
                default_admin = {
                    "username": "admin",
                    "password": admin_password,
                    "role": "admin",
                    "created_at": datetime.now().isoformat()
                }

                # Add users list to config
                config["users"] = [default_admin]

                # Remove old admin_password if it exists
                if "admin_password" in config:
                    del config["admin_password"]

                # Update the config file
                with open(CONFIG_FILE, 'w') as f:
                    json.dump(config, f, indent=4)

                print(f"Migrated configuration file to new user structure: {CONFIG_FILE}")
            else:
                print(f"Configuration file already exists: {CONFIG_FILE}")
        except Exception as e:
            print(f"Error checking/migrating configuration: {e}")

def get_config(key=None):
    """Get configuration value(s) from the config file."""
    if not os.path.exists(CONFIG_FILE):
        logger.warning(f"Config file not found, initializing: {CONFIG_FILE}")
        init_config()

    try:
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)

        if key == "users":
            logger.info(f"Retrieved users from config. Count: {len(config.get('users', []))}")
            logger.info(f"User names: {[user.get('username') for user in config.get('users', [])]}")

        if key:
            return config.get(key)
        return config
    except Exception as e:
        logger.error(f"Error reading configuration: {e}")
        return None if key else {}

def update_config(key, value):
    """Update a configuration value in the config file."""
    if not os.path.exists(CONFIG_FILE):
        init_config()

    try:
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)

        config[key] = value
        config["last_updated"] = datetime.now().isoformat()

        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)

        return True
    except Exception as e:
        print(f"Error updating configuration: {e}")
        return False

def get_user(username):
    """Get a user by username."""
    users = get_config("users")
    if not users:
        return None

    for user in users:
        if user.get("username") == username:
            return user

    return None

def verify_user(username, password):
    """Verify if the provided username and password are valid."""
    logger.info(f"Attempting to verify user: {username}")
    user = get_user(username)

    if not user:
        logger.warning(f"User not found: {username}")
        return False

    result = verify_password(user.get("password"), password)
    logger.info(f"User verification result for {username}: {result}")
    return result

def is_admin_user(username):
    """Check if a user is an admin."""
    user = get_user(username)
    if not user:
        return False

    return user.get("role") == "admin"

def add_user(username, password, role="user", admin_username=None, admin_password=None):
    """Add a new user to the configuration.

    Args:
        username: The username for the new user
        password: The password for the new user
        role: The role for the new user (default: "user")
        admin_username: Admin username for verification (required for adding users)
        admin_password: Admin password for verification (required for adding users)

    Returns:
        True if successful, False otherwise
    """
    # Check if the admin credentials are valid
    if not verify_user(admin_username, admin_password) or not is_admin_user(admin_username):
        print("Admin authentication required to add users")
        return False

    # Check if the username already exists
    if get_user(username):
        print(f"User '{username}' already exists")
        return False

    # Create the new user
    new_user = {
        "username": username,
        "password": hash_password(password),
        "role": role,
        "created_at": datetime.now().isoformat()
    }

    # Add the user to the configuration
    users = get_config("users") or []
    users.append(new_user)

    # Update the configuration
    return update_config("users", users)

def change_user_password(username, current_password, new_password):
    """Change a user's password."""
    # Get the current users
    users = get_config("users")
    if not users:
        return False

    # Find the user
    for i, user in enumerate(users):
        if user.get("username") == username:
            # Verify the current password
            if verify_password(user.get("password"), current_password):
                # Update the password
                users[i]["password"] = hash_password(new_password)
                users[i]["updated_at"] = datetime.now().isoformat()

                # Update the configuration
                return update_config("users", users)
            else:
                return False

    return False

def delete_user(username, admin_username, admin_password):
    """Delete a user from the configuration."""
    # Check if the admin credentials are valid
    if not verify_user(admin_username, admin_password) or not is_admin_user(admin_username):
        print("Admin authentication required to delete users")
        return False

    # Get the current users
    users = get_config("users")
    if not users:
        return False

    # Find the user
    for i, user in enumerate(users):
        if user.get("username") == username:
            # Don't allow deleting the last admin user
            if user.get("role") == "admin" and len([u for u in users if u.get("role") == "admin"]) <= 1:
                print("Cannot delete the last admin user")
                return False

            # Remove the user
            users.pop(i)

            # Update the configuration
            return update_config("users", users)

    return False

def verify_admin_password(password):
    """Verify if the provided password matches any admin user's password.

    This is kept for backward compatibility.
    """
    users = get_config("users")
    if not users:
        return False

    for user in users:
        if user.get("role") == "admin" and verify_password(user.get("password"), password):
            return True

    return False

def is_operation_allowed(operation_type, admin_password=None, username=None):
    """Check if an operation is allowed based on configuration and provided credentials."""
    # If username is provided, check if the user is an admin
    if username:
        if not is_admin_user(username):
            return False

    if operation_type == "delete_test_cases":
        allow_without_password = get_config("allow_delete_test_cases")
    elif operation_type == "clear_database":
        allow_without_password = get_config("allow_clear_database")
    else:
        return False

    # If operation is allowed without password, return True
    if allow_without_password:
        return True

    # Otherwise, verify the admin password
    if admin_password and verify_admin_password(admin_password):
        return True

    return False

# Initialize the configuration when the module is imported
init_config()
