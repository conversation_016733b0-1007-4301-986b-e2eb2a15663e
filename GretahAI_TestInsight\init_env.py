"""
Environment Initialization Module.
This module sets up the necessary environment configurations for the application.
It defines base directories for storing artifacts like uploads, screenshots, logs,
page source code, raw output, and debug output. It also specifies the path
for the SQLite database used to store test results.
Key functionalities include:
- Defining constant paths for various artifact directories relative to the script's location.
- Providing `initialize_directories()` to create these directories if they do not exist.
- Providing `setup_session_state()` to initialize default values for Streamlit's
    session state, ensuring necessary variables are available throughout the user session.
- Handling the import of model lists (OFFLINE_MODELS, ONLINE_MODELS) with fallbacks
    in case the primary source (e.g., `helper.py`) is unavailable.
"""

import os
import streamlit as st  # Ensure streamlit is imported if used here
from datetime import datetime

# Define base directory
BASE_DIR = os.path.dirname(os.path.abspath(__file__))  # Correct BASE_DIR definition

# Define specific artifact directories relative to the base directory
UPLOAD_DIR = os.path.join(BASE_DIR, "uploads")
SCREENSHOTS_DIR = os.path.join(BASE_DIR, "screenshots")
LOGS_DIR = os.path.join(BASE_DIR, "logs")
PAGE_SOURCE_DIR = os.path.join(BASE_DIR, "page_source")
RAW_OUTPUT_DIR = os.path.join(BASE_DIR, "raw_output")
DEBUG_OUTPUT_DIR = os.path.join(BASE_DIR, "debug_output")

# Database path
DATABASE_PATH = os.path.join(BASE_DIR, "test_results.db")

# Default directory (can be adjusted if needed)
DEFAULT_DIR = LOGS_DIR

def initialize_directories():
    """Creates necessary directories if they don't exist."""
    dirs_to_create = [
        UPLOAD_DIR,
        SCREENSHOTS_DIR,
        LOGS_DIR,
        PAGE_SOURCE_DIR,  # Ensure main logs dir exists if different
        RAW_OUTPUT_DIR,
        DEBUG_OUTPUT_DIR
    ]
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)
    print("Initialized directories.")  # Optional: for confirmation

def setup_session_state():
    """Initializes Streamlit session state variables if they don't exist."""
    # Determine default model based on default model_type
    default_model_type = "Offline"
    default_model = OFFLINE_MODELS[0] if OFFLINE_MODELS else "llama3"  # Example default offline model

    defaults = {
        "process": None,
        "current_test_run": None,
        "last_execution_time": None,
        "process_finished_flag": False,
        "selected_run_timestamp": None,
        "api_key": None,
        "model_type": default_model_type,  # Initialize model_type
        "model": default_model,  # Initialize model based on default type
        "google_request_timestamps": [],
        "google_token_usage": [],
        "dark_mode": False
        # Add other session state variables as needed
    }
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

# Import models needed for default initialization (adjust if helper is different)
# This might need adjustment based on where OFFLINE_MODELS is defined.
# If it's in helper.py, import it here.
try:
    from helper import OFFLINE_MODELS, ONLINE_MODELS
except ImportError:
    # Define fallbacks if helper isn't available during init_env execution context
    OFFLINE_MODELS = ["llama3"]
    ONLINE_MODELS = ["gemini-pro"]
