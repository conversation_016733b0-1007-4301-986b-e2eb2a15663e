#!/usr/bin/env python3
import base64
import io
from pathlib import Path
from datetime import datetime
import os
import re
import traceback
import pandas as pd
import streamlit as st

# Import database helper when needed in functions
from Test_case_db_helper import *

import sqlite3
# Import database helper when needed in functions

# Import utility functions from gui.utils
from gui.utils import (
    generate_test_scenarios,
    merge_excel_files,
    load_usage_data,
    save_usage_data
)

# Import functions directly from CaseForge helpers
from helpers import (
    create_formatted_excel_from_scenarios,
    get_latest_test_case_file,
    is_ollama_running,
    upload_edited_excel,
    run_ollama_with_chat,
    run_google_ai_studio  # Import CaseForge's version with temperature support
)
import Test_case_db_helper as db_helper

# --- Initialize Session State ---
# Store generated data
if "scenario_data" not in st.session_state:
    st.session_state.scenario_data = None

# Load Google AI usage data from file
initial_request_timestamps, initial_token_usage = load_usage_data()

# Store Google AI usage data
if "google_request_timestamps" not in st.session_state:
    st.session_state.google_request_timestamps = initial_request_timestamps # Store datetime objects
if "google_token_usage" not in st.session_state:
    st.session_state.google_token_usage = initial_token_usage # Store tuples of (datetime, token_count)

# Initialize generation issues list if it doesn't exist
if "generation_issues" not in st.session_state:
    st.session_state.generation_issues = []

def extract_jira_issue(jira_client, case_id):
    """
    Extract the JIRA issue details based on the provided case ID.

    Args:
        jira_client: JIRA client instance
        case_id (str): JIRA case ID

    Returns:
        tuple: (success, issue or error_message)
    """
    try:
        if not jira_client:
            return False, "JIRA client not available. Please check your connection."

        # Fetch the issue from JIRA
        issue = jira_client.issue(case_id)
        return True, issue
    except Exception as e:
        error_message = f"Error fetching JIRA issue: {str(e)}"
        return False, error_message

def render_jira_details(issue):
    """
    Render the JIRA issue details in the UI.

    Args:
        issue: JIRA issue object
    """
    # Add custom CSS for consistent font sizes in JIRA details
    st.markdown("""    <style>
    .jira-details {
        font-size: 14px !important;
        line-height: 1.5 !important;
        margin-bottom: 10px !important;
    }
    .jira-details h2 {
        font-size: 20px !important;
        font-weight: bold !important;
        margin-bottom: 15px !important;
    }
    .jira-details h3 {
        font-size: 16px !important;
        font-weight: bold !important;
        margin-top: 20px !important;
        margin-bottom: 10px !important;
    }
    .jira-details .timestamp {
        color: #666;
        font-style: italic;
        font-size: 12px !important;
        margin-top: 5px !important;
        margin-bottom: 15px !important;
    }
    /* Style the toggle switch */
    .stToggle {
        margin-top: 5px !important;
    }
    .jira-details p {
        font-size: 14px !important;
        margin-bottom: 10px !important;
    }
    .jira-details ul, .jira-details ol {
        font-size: 14px !important;
        margin-left: 20px !important;
        margin-bottom: 10px !important;
    }
    .jira-details li {
        margin-bottom: 5px !important;
        font-size: 14px !important;
    }
    .jira-details strong {
        font-weight: bold !important;
        font-size: 14px !important;
    }
    .jira-details * {
        font-size: 14px !important;
    }
    </style>
    <div class="jira-details">
    <h2>JIRA Details</h2>
    </div>
    """, unsafe_allow_html=True)

    c1, c2 = st.columns(2)
    with c1:
        st.markdown('<div class="jira-details"><p><strong>Issue Key:</strong> ' + issue.key + '</p></div>', unsafe_allow_html=True)
        st.markdown('<div class="jira-details"><p><strong>Summary:</strong> ' + issue.fields.summary + '</p></div>', unsafe_allow_html=True)
    with c2:
        st.markdown('<div class="jira-details"><p><strong>Status:</strong> ' + issue.fields.status.name + '</p></div>', unsafe_allow_html=True)
        created_date = datetime.strptime(issue.fields.created, '%Y-%m-%dT%H:%M:%S.%f%z').strftime('%Y-%m-%d')
        st.markdown('<div class="jira-details"><p><strong>Created:</strong> ' + created_date + '</p></div>', unsafe_allow_html=True)

    # Display Description or Acceptance Criteria (Check common fields)
    description = issue.fields.description or "No description found."
    # Often acceptance criteria is in a custom field, e.g., 'customfield_10005'
    # You might need to inspect your JIRA instance to find the correct field ID
    acceptance_criteria = getattr(issue.fields, 'customfield_10005', None) # Example field ID    # Add toggle for enhanced description before any content rendering
    show_enhanced = False
    if "enhanced_description" in st.session_state and st.session_state.enhanced_description:
        col1, col2, col3 = st.columns([2, 2, 1])
        with col1:
            st.markdown('<div class="jira-details"><h3>Description</h3></div>', unsafe_allow_html=True)
        with col2:
            enhanced_at = st.session_state.get("enhanced_timestamp", "")
            if enhanced_at:
                st.info(f"✨ Enhanced version available from {enhanced_at}")
        with col3:
            show_enhanced = st.toggle("📝 Use Enhanced", key="use_enhanced_description")
    else:
        st.markdown('<div class="jira-details"><h3>Description</h3></div>', unsafe_allow_html=True)    # Format the description for better readability
    # Replace JIRA markdown with proper HTML    # Determine which description to use based on toggle state
    if show_enhanced and st.session_state.get("enhanced_description"):
        formatted_description = st.session_state.enhanced_description
    else:
        formatted_description = description
    # Format bullet points and numbered lists
    formatted_description = re.sub(r'^\s*\*\s+(.+?)$', r'<li>\1</li>', formatted_description, flags=re.MULTILINE)
    formatted_description = re.sub(r'^\s*#\s+(.+?)$', r'<li>\1</li>', formatted_description, flags=re.MULTILINE)
    # Wrap lists in ul/ol tags
    if '<li>' in formatted_description:
        formatted_description = re.sub(r'(<li>.*?</li>)', r'<ul>\1</ul>', formatted_description, flags=re.DOTALL)
        # Fix nested lists (remove extra ul tags)
        formatted_description = formatted_description.replace('</li>\n<ul>', '')
        formatted_description = formatted_description.replace('</ul>\n<li>', '</li>')

    # Format headings
    formatted_description = re.sub(r'^h([1-6])\.\s+(.+?)$', r'<h\1>\2</h\1>', formatted_description, flags=re.MULTILINE)

    # Format bold and italic text
    formatted_description = re.sub(r'\*([^*]+)\*', r'<strong>\1</strong>', formatted_description)
    formatted_description = re.sub(r'_([^_]+)_', r'<em>\1</em>', formatted_description)

    # Handle paragraphs
    formatted_description = '<p>' + formatted_description.replace('\n\n', '</p><p>') + '</p>'
    # Clean up any double paragraph tags
    formatted_description = formatted_description.replace('<p><p>', '<p>')
    formatted_description = formatted_description.replace('</p></p>', '</p>')

    # Display the formatted description
    st.markdown('<div class="jira-details">' + formatted_description + '</div>', unsafe_allow_html=True)

    if acceptance_criteria:
        st.markdown('<div class="jira-details"><h3>Acceptance Criteria</h3></div>', unsafe_allow_html=True)

        # Format the acceptance criteria for better readability
        formatted_criteria = acceptance_criteria
        # Format bullet points and numbered lists
        formatted_criteria = re.sub(r'^\s*\*\s+(.+?)$', r'<li>\1</li>', formatted_criteria, flags=re.MULTILINE)
        formatted_criteria = re.sub(r'^\s*#\s+(.+?)$', r'<li>\1</li>', formatted_criteria, flags=re.MULTILINE)
        # Wrap lists in ul/ol tags
        if '<li>' in formatted_criteria:
            formatted_criteria = re.sub(r'(<li>.*?</li>)', r'<ul>\1</ul>', formatted_criteria, flags=re.DOTALL)
            # Fix nested lists (remove extra ul tags)
            formatted_criteria = formatted_criteria.replace('</li>\n<ul>', '')
            formatted_criteria = formatted_criteria.replace('</ul>\n<li>', '</li>')

        # Format headings
        formatted_criteria = re.sub(r'^h([1-6])\.\s+(.+?)$', r'<h\1>\2</h\1>', formatted_criteria, flags=re.MULTILINE)

        # Format bold and italic text
        formatted_criteria = re.sub(r'\*([^*]+)\*', r'<strong>\1</strong>', formatted_criteria)
        formatted_criteria = re.sub(r'_([^_]+)_', r'<em>\1\em>', formatted_criteria)

        # Handle paragraphs
        formatted_criteria = '<p>' + formatted_criteria.replace('\n\n', '</p><p>') + '</p>'
        # Clean up any double paragraph tags
        formatted_criteria = formatted_criteria.replace('<p><p>', '<p>')
        formatted_criteria = formatted_criteria.replace('</p></p>', '</p>')

        # Display the formatted acceptance criteria
        st.markdown('<div class="jira-details">' + formatted_criteria + '</div>', unsafe_allow_html=True)

    # Display attachments if available
    if issue and hasattr(issue, 'fields') and hasattr(issue.fields, 'attachment') and issue.fields.attachment:
        st.markdown('<div class="jira-details"><h3>Attachments</h3></div>', unsafe_allow_html=True)

        # Create a directory for storing attachments if it doesn't exist
        attached_images_dir = Path("attached_images")
        attached_images_dir.mkdir(exist_ok=True)

        # Count image attachments first
        image_attachments = [
            att for att in issue.fields.attachment
            if any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp'])
        ]

        if image_attachments:
            # Create a container with custom styling for attachments
            with st.container():
                st.markdown("""
                <style>
                .attachment-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                    gap: 20px;
                    padding: 20px;
                }
                .attachment-item {
                    border: 1px solid #ddd;
                    border-radius: 10px;
                    padding: 10px;
                    text-align: center;
                    background: white;
                }
                .attachment-item img {
                    max-width: 100%;
                    height: auto;
                    border-radius: 5px;
                }
                .attachment-item p {
                    margin: 10px 0;
                    font-size: 14px;
                    color: #666;
                }
                </style>
                """, unsafe_allow_html=True)

                # Start the grid container
                st.markdown('<div class="attachment-grid">', unsafe_allow_html=True)

                for att in image_attachments:
                    # Download and save the image
                    img_data = att.get()
                    img_path = attached_images_dir / att.filename
                    try:
                        with open(img_path, 'wb') as f:
                            f.write(img_data)

                        # Create a unique key for each image based on filename and timestamp
                        image_key = f"{att.filename}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

                        # Display the image with a preview button and download link
                        st.markdown(f"""
                        <div class="attachment-item">
                            <img src="data:image/{os.path.splitext(att.filename)[1][1:]};base64,{base64.b64encode(img_data).decode()}"
                                 alt="{att.filename}"/>
                            <p>{att.filename}</p>
                        </div>
                        """, unsafe_allow_html=True)

                        # Add buttons in columns
                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button("🔍 View", key=f"view_{image_key}"):
                                st.session_state.modal_image = {
                                    "show": True,
                                    "path": str(img_path),
                                    "filename": att.filename
                                }
                                st.rerun()

                        with col2:
                            with open(img_path, "rb") as file:
                                st.download_button(
                                    label="💾 Download",
                                    data=file,
                                    file_name=att.filename,
                                    mime=f"image/{os.path.splitext(att.filename)[1][1:]}",
                                    key=f"download_{image_key}"
                                )
                    except Exception as e:
                        st.error(f"Error processing image {att.filename}: {str(e)}")

                # Close the grid container
                st.markdown('</div>', unsafe_allow_html=True)

        # Display non-image attachments
        non_image_attachments = [
            att for att in issue.fields.attachment
            if not any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp'])
        ]

        if non_image_attachments:
            st.markdown("### Other Attachments")
            for att in non_image_attachments:
                col1, col2 = st.columns([4, 1])
                with col1:
                    st.markdown(f"📎 {att.filename}")
                with col2:
                    try:
                        # Download the attachment
                        attachment_data = att.get()
                        st.download_button(
                            label="💾 Download",
                            data=attachment_data,
                            file_name=att.filename,
                            mime="application/octet-stream",
                            key=f"download_other_{att.filename}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                        )
                    except Exception as e:
                        st.error(f"Error processing attachment {att.filename}: {str(e)}")

def render_test_generator(jira_client, ai_provider, google_api_key, selected_model, config=None):
    global db_helper  # ensure db_helper refers to module-level import, not local

    # Initialize session state variables
    if "google_request_timestamps" not in st.session_state:
        st.session_state.google_request_timestamps = []

    if "jira_issue_extracted" not in st.session_state:
        st.session_state.jira_issue_extracted = False
        st.session_state.jira_issue = None

    """
    Render the test generator page.

    Args:
        jira_client: JIRA client instance
        ai_provider (str): AI provider (Local or Google AI Studio)
        google_api_key (str): Google API key
        selected_model (str): Selected AI model
        config (dict, optional): Configuration dictionary
    """
    # Check if we need to display an image modal
    if "modal_image" in st.session_state and st.session_state.modal_image.get("show", False):
        # Add custom CSS for the modal
        st.markdown("""
        <style>
        .modal-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #ddd;
            position: relative;
            z-index: 1000;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .modal-title {
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        }
        .modal-content {
            padding: 10px 0;
        }
        .modal-footer {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #eee;
            text-align: right;
        }
        </style>
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">{}</h3>
            </div>
            <div class="modal-content">
            </div>
        </div>
        """.format(st.session_state.modal_image['filename']), unsafe_allow_html=True)

        # Create a modal-like display for the image
        with st.container():
            # Add a close button at the top
            col1, col2 = st.columns([6, 1])
            with col2:
                if st.button("❌ Close", key="close_modal_button"):
                    st.session_state.modal_image["show"] = False
                    st.rerun()

            # Display the image at full width with high quality
            st.image(
                st.session_state.modal_image["path"],
                use_container_width=True
            )

            # Add a download button
            with open(st.session_state.modal_image["path"], "rb") as file:
                st.download_button(
                    label=f"💾 Download {st.session_state.modal_image['filename']}",
                    data=file,
                    file_name=st.session_state.modal_image["filename"],
                    mime=f"image/{os.path.splitext(st.session_state.modal_image['filename'])[1][1:]}",
                    key=f"download_modal_image_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                )

    # Check if we need to display a PDF preview
    if "pdf_preview" in st.session_state and st.session_state.pdf_preview.get("show", False):
        # Add custom CSS for the PDF modal
        st.markdown("""
        <style>
        .pdf-modal-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #ddd;
            position: relative;
            z-index: 1000;
        }
        .pdf-frame {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        </style>
        <div class="pdf-modal-container">
            <h3>{}</h3>
        </div>
        """.format(st.session_state.pdf_preview['filename']), unsafe_allow_html=True)

        # Create a modal-like display for the PDF
        with st.container():
            # Add a close button at the top
            col1, col2 = st.columns([6, 1])
            with col2:
                if st.button("❌ Close", key="close_pdf_button"):
                    st.session_state.pdf_preview["show"] = False
                    st.rerun()

            # Display the PDF using an iframe
            pdf_path = st.session_state.pdf_preview["path"]

            # Create an iframe to display the PDF
            st.markdown(f"""
            <iframe
                src="{pdf_path}"
                class="pdf-frame"
                title="{st.session_state.pdf_preview['filename']}"
            ></iframe>
            """, unsafe_allow_html=True)

            # Add a download button
            try:
                with open(pdf_path, "rb") as file:
                    st.download_button(
                        label=f"💾 Download {st.session_state.pdf_preview['filename']}",
                        data=file,
                        file_name=st.session_state.pdf_preview['filename'],
                        mime="application/pdf",
                        key=f"download_pdf_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                    )
            except Exception as e:
                st.warning(f"Could not create download button: {str(e)}")
                st.markdown(f"Download the file manually from: {pdf_path}")
    # Add file upload section to the sidebar if we're on the Generator page
    if st.session_state["current_page"] == "generator":
        with st.sidebar:
            st.markdown("---")
            st.markdown("### Upload Edited Test Cases")
            uploaded_file = st.file_uploader("Upload edited Excel file", type=["xlsx"], help="Upload an edited test case Excel file to store it in the system", key="test_generator_file_uploader")

            # Only show upload options if we have an uploaded file
            if uploaded_file is not None:
                # Add information about the upload options
                with st.expander("About Upload Options"):
                    st.markdown("### Update Database")
                    st.info("This option will save the edited test cases to the database with the 'is_edited' flag set to True. When viewing test cases, edited versions will be prioritized over original versions.")

                # Store the uploaded file in session state for later use
                st.session_state["uploaded_excel_file"] = uploaded_file
                st.success(f"Excel file '{uploaded_file.name}' uploaded successfully. Choose an action below:")

                col1, col2 = st.columns(2)

                with col1:
                    if st.button("Update Database", key="test_generator_update_db_button"):
                        with st.spinner("Updating database..."):
                            # Get the current logged-in user
                            current_user = st.session_state.get("admin_username", "admin")
                            success, message, file_path = upload_edited_excel(st.session_state["uploaded_excel_file"], current_user)
                            if success:
                                st.success(message)
                            else:
                                st.error(message)

                with col2:
                    # Jira Integration button (disabled for now)
                    if st.button("Jira Integration", disabled=False, help="Upload test cases to Jira Zephyr Scale", key="test_generator_jira_integration_button"):
                        if "uploaded_excel_file" in st.session_state:
                            with st.spinner("Uploading test cases to Jira Zephyr Scale..."):
                                try:
                                    # Read the Excel file into a DataFrame
                                    df = pd.read_excel(st.session_state["uploaded_excel_file"])

                                    # Extract user story IDs from the DataFrame
                                    user_story_ids = df["User Story ID"].dropna().unique().tolist()

                                    if config:
                                        # Get Jira credentials from config
                                        jira_url = config.get("jira_server", "")
                                        jira_username = config.get("jira_user", "")
                                        jira_api_token = config.get("jira_api_token", "")

                                        # Import the Zephyr integration module
                                        from helpers import load_zephyr_integration
                                        if load_zephyr_integration():
                                            # This will import the upload_test_cases_to_jira function
                                            from zephyr_integration import upload_test_cases_to_jira

                                            # Upload test cases to Jira
                                            success, message = upload_test_cases_to_jira(
                                                dataframe=df,
                                                jira_url=jira_url,
                                                jira_username=jira_username,
                                                jira_password=jira_api_token,
                                                user_story_ids=user_story_ids
                                            )

                                            if success:
                                                st.success(message)
                                            else:
                                                st.error(message)
                                        else:
                                            st.error("Zephyr integration module not found. Please install the required dependencies.")
                                    else:
                                        st.error("Config not provided. Cannot access Jira credentials.")
                                except Exception as e:
                                    st.error(f"Error uploading test cases to Jira: {e}")
                                    import traceback
                                    st.code(traceback.format_exc())    # Initialize variables that need to be available globally
    # Test Case Generator Page
    # Main content area header
    st.markdown('<h1 class="main-header">🤖 GretahAI Test Case Generator</h1>', unsafe_allow_html=True)

    # Initialize session state for JIRA issue if not exists
    if "jira_issue_extracted" not in st.session_state:
        st.session_state.jira_issue_extracted = False
        st.session_state.jira_issue = None    # First, let's create the JIRA ID input and extract button section
    col1, col2 = st.columns([3, 1])
    with col1:
        case_id = st.text_input("Enter JIRA Case ID", "TP-1", key="jira_case_id_input")

    with col2:
        jira_connected = jira_client is not None
        extract_button = st.button(
            "📄 Extract JIRA Story",
            use_container_width=True,
            disabled=not jira_connected,
            key="extract_jira_button"
        )

        # Add temperature control that's always visible
        temperature = st.slider(
            "Format strictness",
            min_value=0.1,
            max_value=1.0,
            value=0.7,
            step=0.1,
            help="Controls how close the AI stays to the original format:\n\n" +
                 "• Lower (0.1-0.3): Strict formatting, minimal changes\n" +
                 "• Medium (0.4-0.6): Balanced formatting with some improvements\n" +
                 "• Higher (0.7-1.0): More creative reformatting"
        )
        st.session_state["format_temperature"] = temperature

        # Add enhance button next to extract
        enhance_button = st.button(
            "✏️ Enhance JIRA Story",
            use_container_width=True,
            disabled=not jira_connected,  # Only check for JIRA connection
            key="enhance_jira_button"
        )
        if not jira_connected:
            st.error("JIRA connection failed. Cannot extract issue details.")    # Handle enhancement
    if enhance_button and st.session_state.get("jira_issue", None) is not None:
        with st.spinner("Enhancing description..."):
            issue = st.session_state.jira_issue
            if not issue or not hasattr(issue, 'fields'):
                st.error("Invalid JIRA issue. Please try extracting the issue again.")
                return

            orig_desc = issue.fields.description or ""
            # Get the temperature value from session state for enhancement only            temperature = st.session_state.get("format_temperature", 0.7)
              # Build the enhanced prompt with role definition and temperature-specific guidance
            prompt = (
                "Role: You are an expert technical writer specializing in JIRA documentation enhancement. "
                f"Your current enhancement level is {temperature:.1f} on a scale of 0.1 to 1.0.\n\n"
                # Context and temperature-specific guidance"Context: You are enhancing a JIRA issue description while carefully maintaining its technical accuracy. "
                "Your enhancement level is controlled by a temperature parameter where:\n"
                "- Low (0.1-0.3): Conservative enhancements:\n"
                "  • Fix only critical typos and obvious spelling errors\n"
                "  • Standardize bullet points/numbering symbols\n"
                "  • Remove double spaces and extra blank lines\n"
                "  • Never change sentence structure or content\n\n"
                "- Medium (0.4-0.6): Moderate improvements:\n"
                "  • All low-temperature improvements\n"
                "  • Improve unclear sentences while keeping meaning\n"
                "  • Add bullet points for clear list-like content\n"
                "  • Fix subject-verb agreement issues\n"
                "  • Standardize capitalizations in headings/lists\n"
                "  • Fix punctuation and spacing consistency\n\n"
                "- High (0.7-1.0): Comprehensive enhancements:\n"
                "  • All medium-temperature improvements\n"
                "  • Restructure unclear sentences for better flow\n"
                "  • Convert run-on sentences into proper lists\n"
                "  • Add helpful transitions between sections\n"
                "  • Improve overall readability and clarity\n"
                "  • Format technical terms consistently\n\n"
                "Instructions:\n"
                "1. Structure Preservation Rules:\n"
                "   - Keep all headings, sections and their exact order\n"
                "   - Preserve all technical details precisely\n"
                "   - Maintain existing paragraph boundaries\n"
                "   - Keep all bullet points and lists intact\n\n"
                "2. Global Restrictions:\n"
                "   - NO markdown formatting changes (###, **, etc.)\n"
                "   - NO removal or addition of technical information\n"
                "   - NO changing of numbers, IDs, or specific terms\n"
                "   - NO merging/splitting of major sections\n\n"                "Original Description:\n"
                f"{orig_desc}\n\n"
                f"Apply temperature {temperature:.1f} guidelines to enhance the above description. "
                "Focus on the specific improvements allowed at your temperature level while "
                "strictly following the structure preservation rules.\n\n"
                "Enhanced Description:"
            )
              # Call the appropriate LLM with temperature for enhancement
            try:
                if ai_provider == "Local":
                    try:
                        enhanced_desc = run_ollama_with_chat(prompt, model=selected_model, temperature=temperature)
                    except TypeError as e:
                        if "unexpected keyword argument 'temperature'" in str(e):
                            # Try again without temperature as a fallback
                            enhanced_desc = run_ollama_with_chat(prompt, model=selected_model)
                else:
                    # For Google AI Studio, we include the temperature in the prompt instead
                    # since the API doesn't support the temperature parameter directly
                    temp_prompt = f"Use a temperature setting of {temperature} (on a scale of 0.1 to 1.0) for this response.\n\n{prompt}"
                    result = run_google_ai_studio(temp_prompt, api_key=google_api_key, model=selected_model)
                    enhanced_desc = result[0] if isinstance(result, tuple) else str(result)

                # Store in session state
                st.session_state.enhanced_description = enhanced_desc
                ts = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                st.session_state.enhanced_timestamp = ts

                # Save to database
                db_helper.update_jira_issue_enhancement(
                    db_helper.DATABASE_PATH,
                    case_id,
                    enhanced_desc,
                    ts
                )
            except Exception as e:
                st.error(f"Error during enhancement: {str(e)}")
                return

    # Handle JIRA extraction
    if extract_button and jira_connected:
        with st.spinner("Extracting JIRA issue..."):
            success, result = extract_jira_issue(jira_client, case_id)
            if success:
                st.session_state.jira_issue_extracted = True
                st.session_state.jira_issue = result
                # Load existing enhanced description from DB
                ed, ts = db_helper.get_jira_issue_enhancement(db_helper.DATABASE_PATH, case_id)
                if ed:
                    st.session_state.enhanced_description = ed
                    st.session_state.enhanced_timestamp = ts
                else:
                    st.session_state.enhanced_description = None
                    st.session_state.enhanced_timestamp = None
                st.success(f"Successfully extracted JIRA issue: {result.key}")
            else:
                st.error(f"Failed to extract JIRA issue: {result}")
                st.session_state.jira_issue_extracted = False
                st.session_state.jira_issue = None

    # Display JIRA details in an expandable section if extraction was successful
    if st.session_state.jira_issue_extracted and st.session_state.jira_issue:
        with st.expander("JIRA Issue Details", expanded=True):
            render_jira_details(st.session_state.jira_issue)

        # Only show the rest of the form if JIRA issue is extracted
        col_type, col_num = st.columns(2)
        with col_type:
            # Create a format function for test types
            def format_test_type(test_type):
                test_type_labels = {
                    "all": "All Test Case Types",
                    "positive": "Positive Scenarios",
                    "negative": "Negative Scenarios",
                    "security": "Security Testing",
                    "performance": "Performance Testing"
                }
                return test_type_labels.get(test_type, test_type)

            test_type = st.selectbox(
                "Test Case Type",
                options=["all", "positive", "negative", "security", "performance"],
                format_func=format_test_type,
                key="test_type_select"
            )
        with col_num:
            num_scenarios = st.number_input("Number of Test cases", min_value=1, max_value=20, value=5, key="num_scenarios_input")

        # Add the generate button
        disable_generate = (ai_provider == "Google AI Studio" and not google_api_key)
        generate_button = st.button(
            "🚀 Generate Test Cases",
            use_container_width=True,
            disabled=disable_generate,
            key="test_generator_generate_button"
        )
        if disable_generate and ai_provider == "Google AI Studio" and not google_api_key:
            st.warning("Enter Google API Key to enable generation.")
    else:
        # If JIRA issue not extracted yet, show a message to guide the user
        if not extract_button:
            st.info("Enter a JIRA Case ID and click 'Extract JIRA Issue' to proceed.")

    # We'll only show the issues expander inside the tabs, not at the top level    # Generate test cases when the button is clicked
    if st.session_state["current_page"] == "generator" and "test_generator_generate_button" in st.session_state and st.session_state.test_generator_generate_button and st.session_state.jira_issue_extracted:
        # Clear any previous issues
        st.session_state.generation_issues = []

        with st.spinner(f"🔄 Generating test cases-- Please wait."):
            try:
                if test_type == "all":
                    all_test_types = ["positive", "negative", "security", "performance"]
                    all_responses = []
                    all_output_files = []
                    all_issues = []
                    total_processing_time = 0
                    total_tokens_used = 0

                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    # We'll store raw responses in the Test_cases folder
                    test_cases_dir = Path("Test_cases")
                    test_cases_dir.mkdir(exist_ok=True)

                    # Import database helper
                    import Test_case_db_helper as db_helper

                    # We'll use the database to track test case IDs, no need to check Excel files
                    # These variables are kept for consistency with gui.py
                    _ = 0  # highest_id (unused)
                    _ = False  # main_file_exists (unused)
                    _ = None  # main_all_file (unused)

                    # Create a test run for all test types
                    # Get the current user from session state (if logged in)
                    current_user = st.session_state.get("admin_username", "anonymous")
                    test_run_id = db_helper.create_test_run(
                        db_helper.DATABASE_PATH,
                        case_id,
                        "all",  # Use "all" as the test type for the test run
                        user_name=current_user,
                        notes=f"Generated with {ai_provider} using {selected_model} - All test types"
                    )

                    # Process each test type
                    for i, tt in enumerate(all_test_types):
                        # Update progress
                        progress = (i) / len(all_test_types)
                        progress_bar.progress(progress)

                        # Get test type display name
                        test_type_name = {
                            "positive": "POSITIVE - Success Cases",
                            "negative": "NEGATIVE - Error Cases",
                            "security": "SECURITY - Security Tests",
                            "performance": "PERFORMANCE - Speed & Load"
                        }.get(tt, tt.upper())

                        status_text.info(f"Generating {test_type_name} ({i+1}/{len(all_test_types)})...")

                        try:
                            # Generate test cases for this test type
                            issue, response, output_filename, processing_time, tokens_used = generate_test_scenarios(
                                case_id, tt, num_scenarios, selected_model, jira_client, ai_provider, google_api_key,
                                is_all_test_types=True, continue_numbering=True, test_run_id=test_run_id
                            )

                            # If generation failed but didn't raise an exception, add a warning
                            if not response or not output_filename:
                                error_msg = f"Failed to generate {tt.upper()} test cases. Please check the AI response for details."
                                if "generation_issues" not in st.session_state:
                                    st.session_state.generation_issues = []
                                st.session_state.generation_issues.append(error_msg)
                                print(f"Added issue: {error_msg}")
                            else:
                                # Only append results if generation was successful
                                all_responses.append(response)
                                all_output_files.append(output_filename)
                                all_issues.append(issue)
                                total_processing_time += processing_time
                                if tokens_used:
                                    total_tokens_used += tokens_used

                        except Exception as e:
                            # If an exception occurred, add it to the generation_issues list
                            error_msg = f"Error generating {tt.upper()} test cases: {str(e)}"
                            if "generation_issues" not in st.session_state:
                                st.session_state.generation_issues = []
                            st.session_state.generation_issues.append(error_msg)
                            print(f"Added issue: {error_msg}")
                            st.error(f"Error generating {tt.upper()} test cases: {str(e)}")

                            # Add empty/null values when generation fails
                            all_responses.append(None)
                            all_output_files.append(None)
                            if 'issue' in locals():
                                all_issues.append(issue)  # Use the extracted issue if available
                            else:
                                all_issues.append(st.session_state.jira_issue)  # Fall back to session state issue
                            total_processing_time += 0
                            total_tokens_used += 0

                    # Complete the progress bar
                    progress_bar.progress(1.0)
                    # Success message removed as requested
                    status_text.empty()

                    # Update the test run with the total number of test cases and any errors
                    if test_run_id:
                        # Count successful and failed test types
                        successful_types = []
                        failed_types = []
                        total_test_cases = 0

                        for _, (tt, resp, out_file) in enumerate(zip(all_test_types, all_responses, all_output_files)):
                            if resp and out_file:
                                successful_types.append(tt)

                                # Count the actual number of test cases generated for this test type
                                try:
                                    if out_file and out_file.startswith("database://"):
                                        # Extract JIRA ID and test type from the database URL
                                        parts = out_file.split("/")
                                        if len(parts) >= 4:
                                            db_jira_id = parts[2]
                                            db_test_type = parts[3]

                                            # Get the test cases from the database
                                            import Test_case_db_helper as db_helper
                                            df = db_helper.get_test_cases_from_database(db_helper.DATABASE_PATH, db_jira_id, tt)

                                            # Count unique test case IDs
                                            if not df.empty and "Test Case ID" in df.columns:
                                                # Filter out empty strings and count only valid test case IDs (TC_XXX format)
                                                valid_test_case_ids = df['Test Case ID'].dropna().astype(str)
                                                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                                                # Only count IDs that match the TC_XXX pattern
                                                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                                                unique_test_case_ids = valid_test_case_ids.unique()
                                                test_cases_count = len(unique_test_case_ids)

                                                # Add to total
                                                total_test_cases += test_cases_count
                                                print(f"Counted {test_cases_count} test cases for {tt.upper()} from database")
                                            else:
                                                # Fallback to expected count
                                                total_test_cases += num_scenarios
                                                print(f"Using expected count of {num_scenarios} for {tt.upper()} (no valid test cases found in database)")
                                    elif out_file and os.path.exists(out_file):
                                        # Read the Excel file
                                        df = pd.read_excel(out_file)

                                        # Count unique test case IDs
                                        if "Test Case ID" in df.columns:
                                            # Filter out empty strings and count only valid test case IDs (TC_XXX format)
                                            valid_test_case_ids = df['Test Case ID'].dropna().astype(str)
                                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                                            # Only count IDs that match the TC_XXX pattern
                                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                                            unique_test_case_ids = valid_test_case_ids.unique()
                                            test_cases_count = len(unique_test_case_ids)

                                            # Add to total
                                            total_test_cases += test_cases_count
                                            print(f"Counted {test_cases_count} test cases for {tt.upper()} from Excel file")
                                        else:
                                            # Fallback to expected count
                                            total_test_cases += num_scenarios
                                            print(f"Using expected count of {num_scenarios} for {tt.upper()} (no Test Case ID column in Excel file)")
                                    else:
                                        # Fallback to expected count
                                        total_test_cases += num_scenarios
                                        print(f"Using expected count of {num_scenarios} for {tt.upper()} (no valid output file)")
                                except Exception as e:
                                    # If counting fails, use the expected count
                                    total_test_cases += num_scenarios
                                    print(f"Error counting test cases for {tt.upper()}: {e}. Using expected count of {num_scenarios}")

                                    # Add the error to the generation issues
                                    error_msg = f"Error counting test cases for {tt.upper()}: {str(e)}"
                                    if "generation_issues" not in st.session_state:
                                        st.session_state.generation_issues = []
                                    st.session_state.generation_issues.append(error_msg)
                            else:
                                failed_types.append(tt)
                                # Add a message to the generation issues
                                error_msg = f"Failed to generate {tt.upper()} test cases"
                                if "generation_issues" not in st.session_state:
                                    st.session_state.generation_issues = []
                                st.session_state.generation_issues.append(error_msg)

                        # Create a detailed note about the generation
                        if failed_types:
                            notes = f"Generated {total_test_cases} test cases with {ai_provider} using {selected_model}. "
                            notes += f"Successful types: {', '.join(successful_types)}. "
                            notes += f"Failed types: {', '.join(failed_types)}."
                        else:
                            notes = f"Successfully generated {total_test_cases} test cases with {ai_provider} using {selected_model}."

                        # Update the test run with the final status
                        db_helper.update_test_run(
                            db_helper.DATABASE_PATH,
                            test_run_id,
                            num_test_cases=total_test_cases,
                            status="completed" if not failed_types else "partial",
                            notes=notes
                        )

                    # Combine all responses into a single string
                    combined_response = "\n\n".join([f"=== {tt.upper()} TEST CASES ===\n{resp}" for tt, resp in zip(all_test_types, all_responses)])

                    # Use the last output file as the combined output file
                    combined_output_file = all_output_files[-1] if all_output_files else None

                    # Save results to session state
                    st.session_state.scenario_data = {
                        "issue": all_issues[0],  # Use the first issue (they should all be the same)
                        "response": combined_response,
                        "output_file": combined_output_file,
                        "processing_time": total_processing_time,
                        "ai_provider": ai_provider,
                        "model_used": selected_model,
                        "tokens_used": total_tokens_used,
                        "all_files": all_output_files,  # Store all individual files as well
                        "test_types": all_test_types    # Store the test types
                    }

                    # Success message removed as requested
                else:
                    issue, response, output_file, processing_time, tokens_used = generate_test_scenarios(
                                                case_id, test_type, num_scenarios, selected_model, jira_client, ai_provider, google_api_key
                    )

                    # Save results to session state
                    st.session_state.scenario_data = {
                        "issue": issue,
                        "response": response,
                        "output_file": output_file,
                        "processing_time": processing_time,
                        "ai_provider": ai_provider,
                        "model_used": selected_model,
                        "tokens_used": tokens_used  # Store token count if available
                    }

            except Exception as e:
                st.error(f"⚠️ An error occurred during generation: {e}")
                import traceback
                st.code(traceback.format_exc())
                st.session_state.scenario_data = {
                    "issue": None,
                    "response": None,
                    "output_file": None,
                    "processing_time": None,
                    "ai_provider": st.session_state.get("ai_provider_radio", "Local"),  # Default to "Local"
                    "model_used": None,
                    "tokens_used": None
                }

    # Force update the session state if it doesn't exist or doesn't have an output file
    if "scenario_data" not in st.session_state or not st.session_state.scenario_data:
        # Get the current JIRA ID and test type from the input fields
        current_jira_id = st.session_state.get("jira_case_id_input", "")
        current_test_type = st.session_state.get("test_type_select", "all")

        # Initialize with default values
        st.session_state.scenario_data = {
            "issue": None,
            "response": None,
            "output_file": f"database://{current_jira_id}/{current_test_type}/latest" if current_jira_id else None,
            "processing_time": None,
            "ai_provider": st.session_state.get("ai_provider_radio", "Local"),
            "model_used": None,
            "tokens_used": None
        }

    # Display results if available
    if st.session_state.scenario_data and st.session_state["current_page"] == "generator": # Only display tabs on Generator page
        data = st.session_state.scenario_data
        issue = data["issue"]
        response = data["response"]
        output_file = data["output_file"]
        processing_time = data.get("processing_time", 0)
        tokens_used = data.get("tokens_used", 0)

        # Create tabs for organization - removed Input Details tab as it's redundant with the expandable JIRA details section
        tab1, tab2, tab3 = st.tabs(["💡 Raw AI Output", "📊 Generated Test Cases", "🕒 Most Recent Test Cases"])        # Raw AI Output tab
        with tab1:
            if response:
                st.markdown('<h2 class="sub-header">AI Response</h2>', unsafe_allow_html=True)
                # Display generation info
                if processing_time:
                    using_enhanced = st.session_state.get("use_enhanced_description", False) and st.session_state.get("enhanced_description") is not None
                    model_info = f"{data.get('ai_provider', 'AI')} ({data.get('model_used', 'model')})"
                    if using_enhanced:
                        enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")
                        col1, col2 = st.columns([2, 1])
                        with col1:
                            st.info(f"✨ Generated in {processing_time:.1f} seconds using {model_info}")
                        with col2:
                            st.success(f"Using enhanced description from {enhanced_timestamp}")
                    else:
                        st.info(f"Generated in {processing_time:.1f} seconds using {model_info} (Original description)")

                # Display token usage if available
                if tokens_used and data.get('ai_provider') == "Google AI Studio":
                    st.info(f"Used approximately {tokens_used:,} tokens")

                # Display the raw response in a text area
                st.text_area("Raw Output", response, height=500)
            else:
                st.info("No AI response available. Generate test cases to see the raw output.")

            # Display attachments if available
            if issue and hasattr(issue, 'fields') and hasattr(issue.fields, 'attachment') and issue.fields.attachment:
                st.markdown('<div class="jira-details"><h3>Attachments</h3></div>', unsafe_allow_html=True)

                # Create a directory for storing attachments if it doesn't exist
                attached_images_dir = Path("attached_images")
                attached_images_dir.mkdir(exist_ok=True)

                # Count image attachments first
                image_attachments = [
                    att for att in issue.fields.attachment
                    if any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp'])
                ]

                if image_attachments:
                    # Create a container with custom styling for attachments
                    with st.container():
                        st.markdown("""
                        <style>
                        .attachment-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                            gap: 20px;
                            padding: 20px;
                        }
                        .attachment-item {
                            border: 1px solid #ddd;
                            border-radius: 10px;
                            padding: 10px;
                            text-align: center;
                            background: white;
                        }
                        .attachment-item img {
                            max-width: 100%;
                            height: auto;
                            border-radius: 5px;
                        }
                        .attachment-item p {
                            margin: 10px 0;
                            font-size: 14px;
                            color: #666;
                        }
                        </style>
                        """, unsafe_allow_html=True)

                        # Start the grid container
                        st.markdown('<div class="attachment-grid">', unsafe_allow_html=True)

                        for att in image_attachments:
                            # Download and save the image
                            img_data = att.get()
                            img_path = attached_images_dir / att.filename
                            try:
                                with open(img_path, 'wb') as f:
                                    f.write(img_data)

                                # Create a unique key for each image based on filename and timestamp
                                image_key = f"{att.filename}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

                                # Display the image with a preview button and download link
                                st.markdown(f"""
                                <div class="attachment-item">
                                    <img src="data:image/{os.path.splitext(att.filename)[1][1:]};base64,{base64.b64encode(img_data).decode()}"
                                         alt="{att.filename}"/>
                                    <p>{att.filename}</p>
                                </div>
                                """, unsafe_allow_html=True)

                                # Add buttons in columns
                                col1, col2 = st.columns(2)
                                with col1:
                                    if st.button("🔍 View", key=f"view_{image_key}"):
                                        st.session_state.modal_image = {
                                            "show": True,
                                            "path": str(img_path),
                                            "filename": att.filename
                                        }
                                        st.rerun()

                                with col2:
                                    with open(img_path, "rb") as file:
                                        st.download_button(
                                            label="💾 Save",
                                            data=file,
                                            file_name=att.filename,
                                            mime=f"image/{os.path.splitext(att.filename)[1][1:]}",
                                            key=f"download_{image_key}"
                                        )
                            except Exception as e:
                                st.error(f"Error processing image {att.filename}: {str(e)}")

                        # Close the grid container
                        st.markdown('</div>', unsafe_allow_html=True)

                # Display non-image attachments
                non_image_attachments = [
                    att for att in issue.fields.attachment
                    if not any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp'])
                ]

                if non_image_attachments:
                    st.markdown("### Other Attachments")
                    for att in non_image_attachments:
                        col1, col2 = st.columns([4, 1])
                        with col1:
                            st.markdown(f"📎 {att.filename}")
                        with col2:
                            try:
                                # Download the attachment
                                attachment_data = att.get()
                                st.download_button(
                                    label="💾 Download",
                                    data=attachment_data,
                                    file_name=att.filename,
                                    mime="application/octet-stream",
                                    key=f"download_other_{att.filename}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                                )
                            except Exception as e:
                                st.error(f"Error processing attachment {att.filename}: {str(e)}")

        # Generated Test Cases tab
        with tab2:
            st.markdown('<h2 class="sub-header">Generated Test Cases</h2>', unsafe_allow_html=True)

            try:
                # Get the current JIRA ID and test type from the input fields
                current_jira_id = st.session_state.get("jira_case_id_input", "")
                current_test_type = st.session_state.get("test_type_select", "all")

                # Check if the user is logged in
                is_admin_logged_in = st.session_state.get("is_admin_logged_in", False)
                current_user = st.session_state.get("admin_username", "")

                if not is_admin_logged_in or not current_user:
                    st.warning("Please log in through the Admin Panel to view your test cases.")
                    return

                # Initialize parsed_df as an empty DataFrame
                parsed_df = pd.DataFrame()

                # Check if we have an output file
                if output_file:
                    # If the output file is a database URL, get the data from the database
                    if output_file.startswith("database://"):
                        parts = output_file.split("/")
                        if len(parts) >= 4:
                            db_jira_id = parts[2]
                            db_test_type = parts[3]
                            # Get the test cases from the database
                            import Test_case_db_helper as db_helper
                            parsed_df = db_helper.get_test_cases_from_database(db_helper.DATABASE_PATH, db_jira_id, db_test_type)
                    # Otherwise, try to read from the Excel file
                    elif os.path.exists(output_file):
                        parsed_df = pd.read_excel(output_file)

                    # Display the dataframe if it's not empty
                    if not parsed_df.empty:
                        # Display test case count - only count valid test case IDs (TC_XXX format)
                        if "Test Case ID" in parsed_df.columns:
                            # Filter out empty strings and count only valid test case IDs (TC_XXX format)
                            valid_test_case_ids = parsed_df['Test Case ID'].dropna().astype(str)
                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                            # Only count IDs that match the TC_XXX pattern
                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                            unique_test_case_ids = valid_test_case_ids.unique()
                            test_case_count = len(unique_test_case_ids)

                            # Get the min and max test case IDs to show the range
                            if len(unique_test_case_ids) > 0:
                                # No need to extract ID numbers anymore

                                # Display total test cases and range
                                # Get the actual test type from the dataframe
                                actual_test_type = current_test_type
                                if "dashboard_test_type" in parsed_df.columns:
                                    # Get the most common test type in the dataframe
                                    test_types = parsed_df["dashboard_test_type"].dropna().unique()
                                    if len(test_types) == 1:
                                        actual_test_type = test_types[0]
                                # Create an expandable summary section
                                with st.expander("📊 Test Case Info", expanded=True):
                                    # Create a more compact summary with columns
                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.markdown(f"👤 **User:** {current_user}")
                                        st.markdown(f"🔑 **JIRA ID:** {current_jira_id}")
                                    with col2:
                                        st.markdown(f"🏷️ **Test Type:** {actual_test_type.upper()}")
                                        st.markdown(f"📝 **Total Test Cases:** {test_case_count}")
                                    with col3:
                                        # Show which description was used for generation
                                        using_enhanced = st.session_state.get("use_enhanced_description", False) and st.session_state.get("enhanced_description") is not None
                                        if using_enhanced:
                                            enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")
                                            st.success("✨ Using Enhanced Description")
                                            st.caption(f"Enhanced on: {enhanced_timestamp}")
                                        else:
                                            st.info("Using Original Description")
                            else:
                                # Create an expandable summary section
                                with st.expander("📊 Test Case Info", expanded=True):
                                    # Create a more compact summary with columns
                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.markdown(f"👤 **User:** {current_user}")
                                        st.markdown(f"🔑 **JIRA ID:** {current_jira_id}")
                                    with col2:
                                        st.markdown(f"🏷️ **Test Type:** {current_test_type.upper()}")
                                        st.markdown(f"📝 **Total Test Cases:** {test_case_count}")
                                    with col3:
                                        # Show which description was used for generation
                                        using_enhanced = st.session_state.get("use_enhanced_description", False) and st.session_state.get("enhanced_description") is not None
                                        if using_enhanced:
                                            enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")
                                            st.success("✨ Using Enhanced Description")
                                            st.caption(f"Enhanced on: {enhanced_timestamp}")
                                        else:
                                            st.info("Using Original Description")
                        else:
                            test_case_count = 0
                            # Create an expandable summary section
                            with st.expander("📊 Test Case Info", expanded=True):
                                # Create a more compact summary with columns
                                col1, col2, col3 = st.columns(3)
                                with col1:
                                    st.markdown(f"👤 **User:** {current_user}")
                                    st.markdown(f"🔑 **JIRA ID:** {current_jira_id}")
                                with col2:
                                    st.markdown(f"🏷️ **Test Type:** {current_test_type.upper()}")
                                    st.markdown(f"📝 **Total Test Cases:** {test_case_count}")
                                with col3:
                                    # Show which description was used for generation
                                    using_enhanced = st.session_state.get("use_enhanced_description", False) and st.session_state.get("enhanced_description") is not None
                                    if using_enhanced:
                                        enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")
                                        st.success("✨ Using Enhanced Description")
                                        st.caption(f"Enhanced on: {enhanced_timestamp}")
                                    else:
                                        st.info("Using Original Description")

                        # Display the dataframe as an editable table
                        edited_df = st.data_editor(
                            parsed_df,
                            use_container_width=True,
                            num_rows="dynamic",
                            key="generated_test_cases_editor"
                        )

                        # Add a save button to update the database with edited data
                        if st.button("💾 Save Changes to Database", key="save_generated_test_cases"):
                            with st.spinner("Saving changes to database..."):
                                try:
                                    # Check if there are changes
                                    if not edited_df.equals(parsed_df):
                                        # Import database helper
                                        import Test_case_db_helper as db_helper

                                        # Get the current JIRA ID and test type
                                        db_jira_id = current_jira_id
                                        db_test_type = current_test_type

                                        # Add debug information
                                        st.info(f"Preparing to save changes for JIRA ID: {db_jira_id}, Test Type: {db_test_type}, User: {current_user}")

                                        # Validate the dataframe before saving
                                        if edited_df.empty:
                                            st.error("Cannot save empty dataframe to database.")
                                            return

                                        # Check for required columns
                                        required_columns = ["Test Case ID", "Test Case Objective"]
                                        missing_columns = [col for col in required_columns if col not in edited_df.columns]
                                        if missing_columns:
                                            st.error(f"Missing required columns in dataframe: {', '.join(missing_columns)}")
                                            return

                                        # Log the shape of the dataframe
                                        st.info(f"Dataframe shape: {edited_df.shape[0]} rows, {edited_df.shape[1]} columns")

                                        # Update the database with the edited data
                                        try:
                                            success, message = db_helper.update_test_cases_in_database(
                                                db_helper.DATABASE_PATH,
                                                edited_df,
                                                db_jira_id,
                                                db_test_type,
                                                current_user,
                                                is_edited=True
                                            )

                                            if success:
                                                st.success(f"✅ {message}")
                                            else:
                                                st.error(f"❌ {message}")
                                                # Add to generation issues
                                                if "generation_issues" not in st.session_state:
                                                    st.session_state.generation_issues = []
                                                if "generation_issues" not in st.session_state:
                                                    st.session_state.generation_issues = []
                                                st.session_state.generation_issues.append(f"Failed to save test cases to database: {message}")
                                        except Exception as db_error:
                                            st.error(f"Database error: {str(db_error)}")
                                            import traceback
                                            error_details = traceback.format_exc()
                                            st.code(error_details)

                                            # Add to generation issues
                                            if "generation_issues" not in st.session_state:
                                                st.session_state.generation_issues = []
                                            st.session_state.generation_issues.append(f"Database error: {str(db_error)}")
                                    else:
                                        st.info("No changes detected. Nothing to save.")
                                except Exception as e:
                                    st.error(f"Error preparing to save changes: {str(e)}")
                                    import traceback
                                    error_details = traceback.format_exc()
                                    st.code(error_details)

                                    # Add to generation issues
                                    if "generation_issues" not in st.session_state:
                                        st.session_state.generation_issues = []
                                    st.session_state.generation_issues.append(f"Error saving test cases: {str(e)}")

                        # Add a download button for the Excel file
                        if output_file and not output_file.startswith("database://") and os.path.exists(output_file):
                            with open(output_file, "rb") as file:
                                st.download_button(
                                    label="📥 Download Excel File",
                                    data=file,
                                    file_name=os.path.basename(output_file),
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                    key="test_generator_download_excel_file"
                                )
                        # If it's a database URL, create a temporary Excel file for download
                        elif output_file and output_file.startswith("database://"):
                            # Create a temporary Excel file with proper formatting
                            try:
                                # Create a temporary directory if it doesn't exist
                                temp_dir = Path("temp_excel")
                                temp_dir.mkdir(exist_ok=True)

                                # Generate a unique filename
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                temp_file_path = temp_dir / f"{db_jira_id}_{db_test_type}_test_cases_{timestamp}.xlsx"

                                # Process the dataframe to group test steps with their parent test cases
                                # This creates a more readable format similar to the dashboard display
                                display_df = parsed_df.copy()

                                # Identify all unique test cases
                                unique_test_cases = display_df['Test Case ID'].dropna().unique()

                                # For each test case, make sure only the first row has the header information
                                for test_case in unique_test_cases:
                                    # Get all rows for this test case
                                    test_case_rows = display_df[display_df['Test Case ID'] == test_case].index.tolist()

                                    if len(test_case_rows) > 1:
                                        # The first row should have step number 1
                                        first_row = None
                                        for idx in test_case_rows:
                                            step_no = display_df.loc[idx, 'Step No']
                                            if pd.notna(step_no) and (step_no == 1 or step_no == '1'):
                                                first_row = idx
                                                break

                                        # If we found the first row, clear header info from all other rows
                                        if first_row is not None:
                                            for idx in test_case_rows:
                                                if idx != first_row:
                                                    # Clear fields that should only appear in the test case header
                                                    display_df.loc[idx, 'Timestamp'] = ''
                                                    display_df.loc[idx, 'Project'] = ''
                                                    display_df.loc[idx, 'Feature'] = ''
                                                    display_df.loc[idx, 'User Story ID'] = ''
                                                    display_df.loc[idx, 'Test Case ID'] = ''
                                                    display_df.loc[idx, 'Test Case Objective'] = ''
                                                    display_df.loc[idx, 'Prerequisite'] = ''
                                                    display_df.loc[idx, 'Priority'] = ''
                                                    display_df.loc[idx, 'Test Type'] = ''
                                                    display_df.loc[idx, 'Test Group'] = ''
                                                    if 'User Name' in display_df.columns:
                                                        display_df.loc[idx, 'User Name'] = ''
                                                    if 'Dashboard Test Type' in display_df.columns:
                                                        display_df.loc[idx, 'Dashboard Test Type'] = ''

                                # Select only the columns that should be displayed in the Excel file
                                # This matches the exact format requested
                                excel_columns = [
                                    'Timestamp', 'Project', 'Feature', 'User Story ID',
                                    'Test Case ID', 'Test Case Objective', 'Prerequisite',
                                    'Step No', 'Test Steps', 'Expected Result', 'Actual Result',
                                    'Test Status', 'Priority', 'Defect ID', 'Comments',
                                    'Test Type', 'Test Group'
                                ]

                                # Filter the dataframe to only include the selected columns
                                # Only include columns that exist in the dataframe
                                excel_columns = [col for col in excel_columns if col in display_df.columns]
                                excel_df = display_df[excel_columns]

                                # Use the helper function to create a formatted Excel file with the processed dataframe
                                from helpers import create_formatted_excel_from_scenarios
                                create_formatted_excel_from_scenarios(
                                    excel_df,  # Use the filtered dataframe
                                    str(temp_file_path),
                                    is_dataframe=True,
                                    create_excel=True  # Explicitly create Excel file
                                )

                                # Create download button for the formatted Excel file
                                with open(temp_file_path, "rb") as file:
                                    st.download_button(
                                        label="📥 Download Excel File",
                                        data=file,
                                        file_name=f"{db_jira_id}_{db_test_type}_test_cases.xlsx",
                                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                        key="test_generator_download_db_excel_file"
                                    )
                            except Exception as e:
                                st.error(f"Error creating formatted Excel file: {str(e)}")
                                # Fallback to simple Excel file if formatting fails
                                temp_excel = io.BytesIO()
                                parsed_df.to_excel(temp_excel, index=False)
                                temp_excel.seek(0)
                                st.download_button(
                                    label="📥 Download Excel File (Simple Format)",
                                    data=temp_excel,
                                    file_name=f"{db_jira_id}_{db_test_type}_test_cases.xlsx",
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                    key="test_generator_download_db_excel_file"
                                )
                    else:
                        st.info("No test cases found in the output file.")
                else:
                    st.info("No test cases have been generated yet.")
            except Exception as e:
                st.error(f"Error displaying test cases: {str(e)}")
                import traceback
                st.code(traceback.format_exc())        # Most Recent Test Cases tab
        with tab3:
            st.markdown('<h2 class="sub-header">Most Recent Test Cases</h2>', unsafe_allow_html=True)

            try:
                import time
                # Get the current JIRA ID and test type from the input fields
                current_jira_id = st.session_state.get("jira_case_id_input", "")
                current_test_type = st.session_state.get("test_type_select", "all")

                # Check if the user is logged in
                is_admin_logged_in = st.session_state.get("is_admin_logged_in", False)
                current_user = st.session_state.get("admin_username", "")

                if not is_admin_logged_in or not current_user:
                    st.warning("Please log in through the Admin Panel to view your test cases.")
                    return

                # Use the same styled container as in the Generated Test Cases tab

                # Get the latest test cases from the database
                if current_jira_id:
                    # Import database helper
                    import Test_case_db_helper as db_helper

                    # For "all" test type, we need a special approach to get all test cases
                    if current_test_type == "all":
                        # First try to get the test run with type "all"
                        latest_test_run_id = db_helper.get_latest_test_run_id(db_helper.DATABASE_PATH, current_jira_id, "all", current_user)

                        if latest_test_run_id:
                            # For "all" test runs, get all test cases associated with this test run ID
                            # regardless of their individual dashboard_test_type
                            query = """
                            SELECT
                                tc.id,
                                tc.test_case_id as "Test Case ID",
                                tc.test_case_objective as "Test Case Objective",
                                tc.feature as "Feature",
                                tc.prerequisite as "Prerequisite",
                                tc.test_group as "Test Group",
                                tc.priority as "Priority",
                                '' as "Test Status",
                                '' as "Notes",
                                tc.dashboard_test_type,
                                tc.jira_id as "User Story ID",
                                tc.timestamp as "Timestamp",
                                tc.user_name,
                                'header' as "row_type",
                                ts.step_number as "Step No",
                                ts.test_step as "Test Steps",
                                ts.expected_result as "Expected Result",
                                '' as "Actual Result",
                                '' as "Defect ID",
                                '' as "Comments",
                                tc.is_edited,
                                tc.test_type as "Test Type",
                                tc.project as "Project",
                                tc.test_run_id
                            FROM test_cases tc
                            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                            WHERE tc.test_run_id = ? AND tc.user_name = ?
                            ORDER BY tc.test_case_id, ts.step_number
                            """

                            conn = db_helper.get_thread_local_connection(db_helper.DATABASE_PATH)
                            cursor = conn.cursor()
                            cursor.execute(query, (latest_test_run_id, current_user))

                            # Convert the results to a DataFrame
                            columns = [description[0] for description in cursor.description]
                            rows = cursor.fetchall()

                            if rows:
                                latest_df = pd.DataFrame(rows, columns=columns)
                            else:
                                # If no test cases found with test_run_id, fall back to getting all test cases for this JIRA ID
                                query = """
                                SELECT
                                    tc.id,
                                    tc.test_case_id as "Test Case ID",
                                    tc.test_case_objective as "Test Case Objective",
                                    tc.feature as "Feature",
                                    tc.prerequisite as "Prerequisite",
                                    tc.test_group as "Test Group",
                                    tc.priority as "Priority",
                                    '' as "Test Status",
                                    '' as "Notes",
                                    tc.dashboard_test_type,
                                    tc.jira_id as "User Story ID",
                                    tc.timestamp as "Timestamp",
                                    tc.user_name,
                                    'header' as "row_type",
                                    ts.step_number as "Step No",
                                    ts.test_step as "Test Steps",
                                    ts.expected_result as "Expected Result",
                                    tc.is_edited,
                                    tc.test_run_id
                                FROM test_cases tc
                                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                                WHERE tc.jira_id = ? AND tc.user_name = ?
                                ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number
                                """

                                cursor.execute(query, (current_jira_id, current_user))

                                # Convert the results to a DataFrame
                                columns = [description[0] for description in cursor.description]
                                rows = cursor.fetchall()

                                if rows:
                                    latest_df = pd.DataFrame(rows, columns=columns)
                                else:
                                    latest_df = pd.DataFrame()
                        else:
                            # If no test run with type "all" found, try to find test runs for individual test types
                            all_test_types = ["positive", "negative", "security", "performance"]
                            combined_df = pd.DataFrame()

                            # Get the latest test run ID for each test type
                            for tt in all_test_types:
                                tt_test_run_id = db_helper.get_latest_test_run_id(db_helper.DATABASE_PATH, current_jira_id, tt, current_user)

                                if tt_test_run_id:
                                    tt_df = db_helper.get_test_cases_for_test_run(db_helper.DATABASE_PATH, tt_test_run_id)

                                    if not tt_df.empty:
                                        # Append to the combined dataframe
                                        combined_df = pd.concat([combined_df, tt_df], ignore_index=True)

                            if not combined_df.empty:
                                latest_df = combined_df
                            else:
                                # If still no test cases found, try to get all test cases for this JIRA ID
                                query = """
                                SELECT
                                    tc.id,
                                    tc.test_case_id as "Test Case ID",
                                    tc.test_case_objective as "Test Case Objective",
                                    tc.feature as "Feature",
                                    tc.prerequisite as "Prerequisite",
                                    tc.test_group as "Test Group",
                                    tc.priority as "Priority",
                                    '' as "Test Status",
                                    '' as "Notes",
                                    tc.dashboard_test_type,
                                    tc.jira_id as "User Story ID",
                                    tc.timestamp as "Timestamp",
                                    tc.user_name,
                                    'header' as "row_type",
                                    ts.step_number as "Step No",
                                    ts.test_step as "Test Steps",
                                    ts.expected_result as "Expected Result",
                                    tc.is_edited,
                                    tc.test_run_id
                                FROM test_cases tc
                                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                                WHERE tc.jira_id = ? AND tc.user_name = ?
                                ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number
                                """

                                conn = db_helper.get_thread_local_connection(db_helper.DATABASE_PATH)
                                cursor = conn.cursor()
                                cursor.execute(query, (current_jira_id, current_user))

                                # Convert the results to a DataFrame
                                columns = [description[0] for description in cursor.description]
                                rows = cursor.fetchall()

                                if rows:
                                    latest_df = pd.DataFrame(rows, columns=columns)
                                else:
                                    latest_df = pd.DataFrame()
                    else:
                        # For specific test types, use the normal flow
                        # Get the latest test run ID for this JIRA ID and test type
                        latest_test_run_id = db_helper.get_latest_test_run_id(db_helper.DATABASE_PATH, current_jira_id, current_test_type, current_user)


                        if latest_test_run_id:
                            # Try to get test cases using the test run ID
                            try:
                                conn = db_helper.get_thread_local_connection(db_helper.DATABASE_PATH)
                                cursor = conn.cursor()

                                # First, check if any test cases are directly associated with this test run
                                # This count is not used directly but could be useful for debugging
                                # cursor.execute(
                                #     """
                                #     SELECT COUNT(*) FROM test_cases WHERE test_run_id = ?
                                #     """,
                                #     (latest_test_run_id,)
                                # )
                                # direct_count = cursor.fetchone()[0]

                                # Query for test cases with this test run ID
                                query = """
                                SELECT
                                    tc.id,
                                    tc.test_case_id as "Test Case ID",
                                    tc.test_case_objective as "Test Case Objective",
                                    tc.feature as "Feature",
                                    tc.prerequisite as "Prerequisite",
                                    tc.test_group as "Test Group",
                                    tc.priority as "Priority",
                                    '' as "Test Status",
                                    '' as "Notes",
                                    tc.dashboard_test_type,
                                    tc.jira_id as "User Story ID",
                                    tc.timestamp as "Timestamp",
                                    tc.user_name,
                                    'header' as "row_type",
                                    ts.step_number as "Step No",
                                    ts.test_step as "Test Steps",
                                    ts.expected_result as "Expected Result",
                                    '' as "Actual Result",
                                    '' as "Defect ID",
                                    '' as "Comments",
                                    tc.is_edited,
                                    tc.test_type as "Test Type",
                                    tc.project as "Project"
                                FROM test_cases tc
                                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                                WHERE tc.test_run_id = ?
                                ORDER BY tc.test_case_id, ts.step_number
                                """

                                cursor.execute(query, (latest_test_run_id,))

                                # Convert the results to a DataFrame
                                columns = [description[0] for description in cursor.description]
                                rows = cursor.fetchall()

                                if rows:
                                    latest_df = pd.DataFrame(rows, columns=columns)

                                else:
                                    # Fallback to getting test cases by JIRA ID and test type
                                    st.warning(f"No test cases found for test run {latest_test_run_id}, trying by JIRA ID and test type")

                                    # Query for test cases with this JIRA ID and test type
                                    query = """
                                    SELECT
                                        tc.id,
                                        tc.test_case_id as "Test Case ID",
                                        tc.test_case_objective as "Test Case Objective",
                                        tc.feature as "Feature",
                                        tc.prerequisite as "Prerequisite",
                                        tc.test_group as "Test Group",
                                        tc.priority as "Priority",
                                        '' as "Test Status",
                                        '' as "Notes",
                                        tc.dashboard_test_type,
                                        tc.jira_id as "User Story ID",
                                        tc.timestamp as "Timestamp",
                                        tc.user_name,
                                        'header' as "row_type",
                                        ts.step_number as "Step No",
                                        ts.test_step as "Test Steps",
                                        ts.expected_result as "Expected Result",
                                        '' as "Actual Result",
                                        '' as "Defect ID",
                                        '' as "Comments",
                                        tc.is_edited,
                                        tc.test_type as "Test Type",
                                        tc.project as "Project"
                                    FROM test_cases tc
                                    LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                                    WHERE tc.jira_id = ?
                                    """

                                    # Add test type filter if not 'all'
                                    params = [current_jira_id]
                                    if current_test_type.lower() != 'all':
                                        query += " AND tc.dashboard_test_type = ?"
                                        params.append(current_test_type.lower())

                                    # Add user filter
                                    if current_user:
                                        query += " AND tc.user_name = ?"
                                        params.append(current_user)

                                    # Order by timestamp to get the most recent test cases
                                    query += " ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number"

                                    cursor.execute(query, params)

                                    # Convert the results to a DataFrame
                                    columns = [description[0] for description in cursor.description]
                                    rows = cursor.fetchall()

                                    if rows:
                                        latest_df = pd.DataFrame(rows, columns=columns)

                                    else:
                                        latest_df = pd.DataFrame()
                                        st.warning(f"No test cases found for {current_jira_id} ({current_test_type})")
                            except Exception as e:
                                st.error(f"Error querying database: {e}")
                                latest_df = pd.DataFrame()
                        else:
                            # If no test run found, try getting test cases by JIRA ID and test type
                            st.warning(f"No test run found for {current_jira_id} ({current_test_type}), trying by JIRA ID and test type")

                            try:
                                conn = db_helper.get_thread_local_connection(db_helper.DATABASE_PATH)
                                cursor = conn.cursor()

                                # Query for test cases with this JIRA ID and test type
                                query = """
                                SELECT tc.*, ji.jira_id as jira_issue_id
                                FROM test_cases tc
                                JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                                WHERE tc.jira_id = ? AND tc.dashboard_test_type = ?
                                ORDER BY tc.timestamp DESC"""
                                cursor.execute(query, (current_jira_id, current_test_type))
                            except Exception as e:
                                st.error(f"Error querying database: {str(e)}")
                                if conn:
                                    conn.close()
                                return
                            finally:
                                if conn:
                                    conn.close()
                    if not latest_df.empty:
                        # Display test case count - only count valid test case IDs (TC_XXX format)
                        if "Test Case ID" in latest_df.columns:
                            # Filter out empty strings and count only valid test case IDs (TC_XXX format)
                            valid_test_case_ids = latest_df['Test Case ID'].dropna().astype(str)
                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                            # Only count IDs that match the TC_XXX pattern
                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                            unique_test_case_ids = valid_test_case_ids.unique()
                            test_case_count = len(unique_test_case_ids)

                            # Get the min and max test case IDs to show the range
                            if len(unique_test_case_ids) > 0:
                                # No need to extract ID numbers anymore

                                # Display total test cases and range
                                # Get the actual test type from the dataframe
                                actual_test_type = current_test_type
                                if "dashboard_test_type" in latest_df.columns:
                                    # Get the most common test type in the dataframe
                                    test_types = latest_df["dashboard_test_type"].dropna().unique()
                                    if len(test_types) == 1:
                                        actual_test_type = test_types[0]

                                # If we have "Test Type" column, check for any test cases with "ALL" type
                                if "Test Type" in latest_df.columns:
                                    # Count test cases by test type
                                    test_type_counts = {}
                                    for test_type in latest_df["Test Type"].dropna().unique():
                                        if test_type:
                                            test_type_counts[test_type] = len(latest_df[latest_df["Test Type"] == test_type]["Test Case ID"].dropna().unique())

                                    # If we have test cases with "ALL" type, add a note
                                    if "ALL" in test_type_counts and test_type_counts["ALL"] > 0:
                                        st.info(f"Note: {test_type_counts['ALL']} test cases have 'ALL' as their test type. These should be categorized as NEGATIVE test cases.")
                                # Create an expandable summary section
                                with st.expander("📊 Test Case Info", expanded=True):
                                    # Create a more compact summary with columns
                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.markdown(f"👤 **User:** {current_user}")
                                        st.markdown(f"🔑 **JIRA ID:** {current_jira_id}")
                                    with col2:
                                        st.markdown(f"🏷️ **Test Type:** {actual_test_type.upper()}")
                                        st.markdown(f"📝 **Total Test Cases:** {test_case_count}")
                                    with col3:
                                        # Show which description was used for generation
                                        using_enhanced = st.session_state.get("use_enhanced_description", False) and st.session_state.get("enhanced_description") is not None
                                        if using_enhanced:
                                            enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")
                                            st.success("✨ Using Enhanced Description")
                                            st.caption(f"Enhanced on: {enhanced_timestamp}")
                                        else:
                                            st.info("Using Original Description")
                            else:
                                # Create an expandable summary section
                                with st.expander("📊 Test Case Info", expanded=True):
                                    # Create a more compact summary with columns
                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.markdown(f"👤 **User:** {current_user}")
                                        st.markdown(f"🔑 **JIRA ID:** {current_jira_id}")
                                    with col2:
                                        st.markdown(f"🏷️ **Test Type:** {current_test_type.upper()}")
                                        st.markdown(f"📝 **Total Test Cases:** {test_case_count}")
                                    with col3:
                                        # Show which description was used for generation
                                        using_enhanced = st.session_state.get("use_enhanced_description", False) and st.session_state.get("enhanced_description") is not None
                                        if using_enhanced:
                                            enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")
                                            st.success("✨ Using Enhanced Description")
                                            st.caption(f"Enhanced on: {enhanced_timestamp}")
                                        else:
                                            st.info("Using Original Description")
                        else:
                            test_case_count = 0
                            # Create an expandable summary section
                            with st.expander("📊 Test Case Info", expanded=True):
                                # Create a more compact summary with columns
                                col1, col2, col3 = st.columns(3)
                                with col1:
                                    st.markdown(f"👤 **User:** {current_user}")
                                    st.markdown(f"🔑 **JIRA ID:** {current_jira_id}")
                                with col2:
                                    st.markdown(f"🏷️ **Test Type:** {current_test_type.upper()}")
                                    st.markdown(f"📝 **Total Test Cases:** {test_case_count}")
                                with col3:
                                    # Show which description was used for generation
                                    using_enhanced = st.session_state.get("use_enhanced_description", False) and st.session_state.get("enhanced_description") is not None
                                    if using_enhanced:
                                        enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")
                                        st.success("✨ Using Enhanced Description")
                                        st.caption(f"Enhanced on: {enhanced_timestamp}")
                                    else:
                                        st.info("Using Original Description")

                        # Process the dataframe to format it like the Generated Test Cases tab
                        # First, ensure we have all the necessary columns
                        required_columns = [
                            "Timestamp", "Project", "Feature", "User Story ID", "Test Case ID",
                            "Test Case Objective", "Prerequisite", "Step No", "Test Steps",
                            "Expected Result", "Test Status", "Actual Result", "Priority",
                            "Defect ID", "Comments", "Test Type", "Test Group"
                        ]

                        # Create a new dataframe with the required columns
                        display_df = pd.DataFrame(columns=required_columns)
                        # Get all unique test case IDs
                        unique_test_cases = latest_df['Test Case ID'].dropna().unique()

                        # For each test case, format the data properly
                        for tc_id in unique_test_cases:
                            # Get all rows for this test case
                            tc_rows = latest_df[latest_df["Test Case ID"] == tc_id]
                            if tc_rows.empty:
                                continue

                            # Get the header row (first row with this test case ID)
                            header_row = tc_rows.iloc[0]

                            # Create a new row for the header
                            header_data = {}
                            for col in required_columns:
                                if col in header_row:
                                    header_data[col] = header_row[col]
                                else:
                                    header_data[col] = ""

                            # Add the header row to the display dataframe
                            display_df = pd.concat([display_df, pd.DataFrame([header_data])], ignore_index=True)

                            # For each step, create a row with only step information
                            # Skip the first step if it's the same as the header step
                            step_rows = tc_rows.iloc[1:] if len(tc_rows) > 1 else pd.DataFrame()

                            for _, step_row in step_rows.iterrows():
                                if pd.isna(step_row.get('Step No')) or step_row.get('Step No') == "":
                                    continue

                                # Create a new row for the step with empty values for header fields
                                step_data = {col: "" for col in required_columns}

                                # Only fill in step-specific columns
                                step_data["Step No"] = step_row.get('Step No', "")
                                step_data["Test Steps"] = step_row.get('Test Steps', "")
                                step_data["Expected Result"] = step_row.get('Expected Result', "")

                                # Add the step row to the display dataframe
                                display_df = pd.concat([display_df, pd.DataFrame([step_data])], ignore_index=True)

                        # Apply styling to match Excel format
                        styled_df = display_df.style.apply(lambda x: [
                            'background-color: #E3F2FD' if x.name == 0 or
                                                         (pd.notna(x['Test Case ID']) and x['Test Case ID'] != '') else
                            '' for _ in x
                        ], axis=1)

                        # Apply additional styling
                        styled_df = styled_df.set_properties(**{
                            'text-align': 'center',
                            'font-family': 'Calibri',
                            'font-size': '11pt',
                            'border': '1px solid #BDBDBD'
                        })

                        # Create column configuration
                        column_config = {
                            "Timestamp": st.column_config.TextColumn("Timestamp", width="small"),
                            "Project": st.column_config.TextColumn("Project", width="small"),
                            "Feature": st.column_config.TextColumn("Feature", width="medium"),
                            "User Story ID": st.column_config.TextColumn("User Story ID", width="small"),
                            "Test Case ID": st.column_config.TextColumn("Test Case ID", width="small"),
                            "Test Case Objective": st.column_config.TextColumn("Test Case Objective", width="medium"),
                            "Prerequisite": st.column_config.TextColumn("Prerequisite", width="medium"),
                            "Step No": st.column_config.NumberColumn("Step No", width="small"),
                            "Test Steps": st.column_config.TextColumn("Test Steps", width="medium"),
                            "Expected Result": st.column_config.TextColumn("Expected Result", width="medium"),
                            "Test Status": st.column_config.SelectboxColumn(
                                "Test Status",
                                width="small",
                                options=["PASS", "FAIL", "BLOCKED", "NOT RUN"],
                                required=False
                            ),
                            "Actual Result": st.column_config.TextColumn("Actual Result", width="medium"),
                            "Priority": st.column_config.SelectboxColumn(
                                "Priority",
                                width="small",
                                options=["High", "Medium", "Low"],
                                required=False
                            ),
                            "Defect ID": st.column_config.TextColumn("Defect ID", width="small"),
                            "Comments": st.column_config.TextColumn("Comments", width="medium"),
                            "Test Type": st.column_config.TextColumn("Test Type", width="small"),
                            "Test Group": st.column_config.TextColumn("Test Group", width="small")
                        }

                        # Display the dataframe as an editable table
                        edited_latest_df = st.data_editor(
                            display_df,
                            use_container_width=True,
                            num_rows="dynamic",
                            column_config=column_config,
                            hide_index=True,
                            height=500,
                            key="latest_test_cases_editor"
                        )

                        # Add a save button to update the database with edited data
                        if st.button("💾 Save Changes to Database", key="save_latest_test_cases"):
                            with st.spinner("Saving changes to database..."):
                                try:
                                    # Check if there are changes
                                    if not edited_latest_df.equals(latest_df):
                                        # Import database helper
                                        import Test_case_db_helper as db_helper

                                        # Get the current JIRA ID and test type
                                        db_jira_id = current_jira_id
                                        db_test_type = current_test_type

                                        # Update the database with the edited data
                                        success, message = db_helper.update_test_cases_in_database(
                                            db_helper.DATABASE_PATH,
                                            edited_latest_df,
                                            db_jira_id,
                                            db_test_type,
                                            current_user,
                                            is_edited=True
                                        )

                                        if success:
                                            st.success(f"✅ {message}")
                                        else:
                                            st.error(f"❌ {message}")
                                    else:
                                        st.info("No changes detected. Nothing to save.")
                                except Exception as e:
                                    st.error(f"Error saving changes to database: {str(e)}")
                                    import traceback
                                    st.code(traceback.format_exc())

                        # Create a temporary Excel file with proper formatting
                        try:
                            # Create a temporary directory if it doesn't exist
                            temp_dir = Path("temp_excel")
                            temp_dir.mkdir(exist_ok=True)

                            # Generate a unique filename
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            temp_file_path = temp_dir / f"{current_jira_id}_{current_test_type}_latest_test_cases_{timestamp}.xlsx"

                            # Process the dataframe to group test steps with their parent test cases
                            # This creates a more readable format similar to the dashboard display
                            display_df = latest_df.copy()

                            # Identify all unique test cases
                            unique_test_cases = display_df['Test Case ID'].dropna().unique()

                            # For each test case, make sure only the first row has the header information
                            for test_case in unique_test_cases:
                                # Get all rows for this test case
                                test_case_rows = display_df[display_df['Test Case ID'] == test_case].index.tolist()

                                if len(test_case_rows) > 1:
                                    # The first row should have step number 1
                                    first_row = None
                                    for idx in test_case_rows:
                                        step_no = display_df.loc[idx, 'Step No']
                                        if pd.notna(step_no) and (step_no == 1 or step_no == '1'):
                                            first_row = idx
                                            break

                                    # If we found the first row, clear header info from all other rows
                                    if first_row is not None:
                                        for idx in test_case_rows:
                                            if idx != first_row:
                                                # Clear fields that should only appear in the test case header
                                                display_df.loc[idx, 'Timestamp'] = ''
                                                display_df.loc[idx, 'Project'] = ''
                                                display_df.loc[idx, 'Feature'] = ''
                                                display_df.loc[idx, 'User Story ID'] = ''
                                                display_df.loc[idx, 'Test Case ID'] = ''
                                                display_df.loc[idx, 'Test Case Objective'] = ''
                                                display_df.loc[idx, 'Prerequisite'] = ''
                                                display_df.loc[idx, 'Priority'] = ''
                                                display_df.loc[idx, 'Test Type'] = ''
                                                display_df.loc[idx, 'Test Group'] = ''
                                                if 'User Name' in display_df.columns:
                                                    display_df.loc[idx, 'User Name'] = ''
                                                if 'Dashboard Test Type' in display_df.columns:
                                                    display_df.loc[idx, 'Dashboard Test Type'] = ''

                            # Select only the columns that should be displayed in the Excel file
                            # This matches the exact format requested
                            excel_columns = [
                                'Timestamp', 'Project', 'Feature', 'User Story ID',
                                'Test Case ID', 'Test Case Objective', 'Prerequisite',
                                'Step No', 'Test Steps', 'Expected Result', 'Actual Result',
                                'Test Status', 'Priority', 'Defect ID', 'Comments',
                                'Test Type', 'Test Group'
                            ]

                            # Filter the dataframe to only include the selected columns
                            # Only include columns that exist in the dataframe
                            excel_columns = [col for col in excel_columns if col in display_df.columns]
                            excel_df = display_df[excel_columns]

                            # Use the helper function to create a formatted Excel file with the processed dataframe
                            from helpers import create_formatted_excel_from_scenarios
                            create_formatted_excel_from_scenarios(
                                excel_df,  # Use the filtered dataframe
                                str(temp_file_path),
                                is_dataframe=True,
                                create_excel=True  # Explicitly create Excel file
                            )

                            # Create download button for the formatted Excel file
                            with open(temp_file_path, "rb") as file:
                                st.download_button(
                                    label="📥 Download Latest Test Cases",
                                    data=file,
                                    file_name=f"{current_jira_id}_{current_test_type}_latest_test_cases.xlsx",
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                    key="test_generator_download_latest_test_cases"
                                )
                        except Exception as e:
                            st.error(f"Error creating formatted Excel file: {str(e)}")
                            # Fallback to simple Excel file if formatting fails
                            temp_excel = io.BytesIO()
                            latest_df.to_excel(temp_excel, index=False)
                            temp_excel.seek(0)
                            st.download_button(
                                label="📥 Download Latest Test Cases (Simple Format)",
                                data=temp_excel,
                                file_name=f"{current_jira_id}_{current_test_type}_latest_test_cases.xlsx",
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                key="test_generator_download_latest_test_cases"
                            )
                    else:
                        st.warning(f"No test cases found for {current_jira_id} ({current_test_type})")

                        # Try a direct database query as a fallback
                        try:
                            conn = db_helper.get_thread_local_connection(db_helper.DATABASE_PATH)
                            cursor = conn.cursor()

                            # Query for any test cases with this JIRA ID
                            query = """
                            SELECT
                                tc.id,
                                tc.test_case_id as "Test Case ID",
                                tc.test_case_objective as "Test Case Objective",
                                tc.feature as "Feature",
                                tc.prerequisite as "Prerequisite",
                                tc.test_group as "Test Group",
                                tc.priority as "Priority",
                                '' as "Test Status",
                                '' as "Notes",
                                tc.dashboard_test_type,
                                tc.jira_id as "User Story ID",
                                tc.timestamp as "Timestamp",
                                tc.user_name,
                                'header' as "row_type",
                                ts.step_number as "Step No",
                                ts.test_step as "Test Steps",
                                ts.expected_result as "Expected Result",
                                '' as "Actual Result",
                                '' as "Defect ID",
                                '' as "Comments",
                                tc.is_edited,
                                tc.test_type as "Test Type",
                                tc.project as "Project"
                            FROM test_cases tc
                            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                            WHERE tc.jira_id = ?
                            """

                            # Add test type filter if not 'all'
                            params = [current_jira_id]
                            if current_test_type.lower() != 'all':
                                query += " AND tc.dashboard_test_type = ?"
                                params.append(current_test_type.lower())

                            # Add user filter
                            if current_user:
                                query += " AND tc.user_name = ?"
                                params.append(current_user)

                            # Order by timestamp to get the most recent test cases
                            query += " ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number"

                            cursor.execute(query, params)

                            # Convert the results to a DataFrame
                            columns = [description[0] for description in cursor.description]
                            rows = cursor.fetchall()

                            if rows:
                                latest_df = pd.DataFrame(rows, columns=columns)

                                # Get the total number of test cases
                                test_case_count = len(latest_df["Test Case ID"].dropna().unique())



                                # Create an expandable summary section
                                with st.expander("📊 Test Case Info", expanded=True):
                                    # Create a more compact summary with columns
                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.markdown(f"👤 **User:** {current_user}")
                                        st.markdown(f"🔑 **JIRA ID:** {current_jira_id}")
                                    with col2:
                                        st.markdown(f"🏷️ **Test Type:** {current_test_type.upper()}")
                                        st.markdown(f"📝 **Total Test Cases:** {test_case_count}")
                                    with col3:
                                        # Show which description was used for generation
                                        using_enhanced = st.session_state.get("use_enhanced_description", False) and st.session_state.get("enhanced_description") is not None
                                        if using_enhanced:
                                            enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")
                                            st.success("✨ Using Enhanced Description")
                                            st.caption(f"Enhanced on: {enhanced_timestamp}")
                                        else:
                                            st.info("Using Original Description")

                                # Process the dataframe to format it like the Generated Test Cases tab
                                # First, ensure we have all the necessary columns
                                required_columns = [
                                    "Timestamp", "Project", "Feature", "User Story ID", "Test Case ID",
                                    "Test Case Objective", "Prerequisite", "Step No", "Test Steps",
                                    "Expected Result", "Test Status", "Actual Result", "Priority",
                                    "Defect ID", "Comments", "Test Type", "Test Group"
                                ]

                                # Create a new dataframe with the required columns
                                display_df = pd.DataFrame(columns=required_columns)

                                # Get all unique test case IDs
                                unique_test_cases = latest_df['Test Case ID'].dropna().unique()

                                # For each test case, format the data properly
                                for tc_id in unique_test_cases:
                                    # Get all rows for this test case
                                    tc_rows = latest_df[latest_df["Test Case ID"] == tc_id]
                                    if tc_rows.empty:
                                        continue

                                    # Get the header row (first row with this test case ID)
                                    header_row = tc_rows.iloc[0]

                                    # Create a new row for the header
                                    header_data = {}
                                    for col in required_columns:
                                        if col in header_row:
                                            header_data[col] = header_row[col]
                                        else:
                                            header_data[col] = ""

                                    # Add the header row to the display dataframe
                                    display_df = pd.concat([display_df, pd.DataFrame([header_data])], ignore_index=True)

                                    # For each step, create a row with only step information
                                    # Skip the first step if it's the same as the header step
                                    step_rows = tc_rows.iloc[1:] if len(tc_rows) > 1 else pd.DataFrame()

                                    for _, step_row in step_rows.iterrows():
                                        if pd.isna(step_row.get('Step No')) or step_row.get('Step No') == "":
                                            continue

                                        # Create a new row for the step with empty values for header fields
                                        step_data = {col: "" for col in required_columns}

                                        # Only fill in step-specific columns
                                        step_data["Step No"] = step_row.get('Step No', "")
                                        step_data["Test Steps"] = step_row.get('Test Steps', "")
                                        step_data["Expected Result"] = step_row.get('Expected Result', "")

                                        # Add the step row to the display dataframe
                                        display_df = pd.concat([display_df, pd.DataFrame([step_data])], ignore_index=True)

                                # Apply styling to match Excel format
                                styled_df = display_df.style.apply(lambda x: [
                                    'background-color: #E3F2FD' if x.name == 0 or
                                                                 (pd.notna(x['Test Case ID']) and x['Test Case ID'] != '') else
                                    '' for _ in x
                                ], axis=1)                                # Apply additional styling
                                styled_df = styled_df.set_properties(**{
                                    'text-align': 'center',
                                    'font-family': 'Calibri',
                                    'font-size': '11pt',
                                    'border': '1px solid #BDBDBD'
                                })

                                # Create column configuration
                                column_config = {
                                    "Timestamp": st.column_config.TextColumn("Timestamp", width="small"),
                                    "Project": st.column_config.TextColumn("Project", width="small"),
                                    "Feature": st.column_config.TextColumn("Feature", width="medium"),
                                    "User Story ID": st.column_config.TextColumn("User Story ID", width="small"),
                                    "Test Case ID": st.column_config.TextColumn("Test Case ID", width="small"),
                                    "Test Case Objective": st.column_config.TextColumn("Test Case Objective", width="medium"),
                                    "Prerequisite": st.column_config.TextColumn("Prerequisite", width="medium"),
                                    "Step No": st.column_config.NumberColumn("Step No", width="small"),
                                    "Test Steps": st.column_config.TextColumn("Test Steps", width="medium"),
                                    "Expected Result": st.column_config.TextColumn("Expected Result", width="medium"),
                                    "Test Status": st.column_config.SelectboxColumn(
                                        "Test Status",
                                        width="small",
                                        options=["PASS", "FAIL", "BLOCKED", "NOT RUN"],
                                        required=False
                                    ),
                                    "Actual Result": st.column_config.TextColumn("Actual Result", width="medium"),
                                    "Priority": st.column_config.SelectboxColumn(
                                        "Priority",
                                        width="small",
                                        options=["High", "Medium", "Low"],
                                        required=False
                                    ),
                                    "Defect ID": st.column_config.TextColumn("Defect ID", width="small"),
                                    "Comments": st.column_config.TextColumn("Comments", width="medium"),
                                    "Test Type": st.column_config.TextColumn("Test Type", width="small"),
                                    "Test Group": st.column_config.TextColumn("Test Group", width="small")
                                }

                                # Display the dataframe as an editable table
                                edited_latest_df = st.data_editor(
                                    display_df,
                                    use_container_width=True,
                                    num_rows="dynamic",
                                    column_config=column_config,
                                    hide_index=True,
                                    height=500,
                                    key="latest_test_cases_editor"
                                )

                                # Add a save button to update the database with edited data
                                if st.button("💾 Save Changes to Database", key="save_latest_test_cases"):
                                    with st.spinner("Saving changes to database..."):
                                        try:
                                            # Check if there are changes
                                            if not edited_latest_df.equals(latest_df):
                                                # Import database helper
                                                import Test_case_db_helper as db_helper

                                                # Get the current JIRA ID and test type
                                                db_jira_id = current_jira_id
                                                db_test_type = current_test_type

                                                # Add debug information
                                                st.info(f"Preparing to save changes for JIRA ID: {db_jira_id}, Test Type: {db_test_type}, User: {current_user}")

                                                # Validate the dataframe before saving
                                                if edited_latest_df.empty:
                                                    st.error("Cannot save empty dataframe to database.")
                                                    return

                                                # Check for required columns
                                                required_columns = ["Test Case ID", "Test Case Objective"]
                                                missing_columns = [col for col in required_columns if col not in edited_latest_df.columns]
                                                if missing_columns:
                                                    st.error(f"Missing required columns in dataframe: {', '.join(missing_columns)}")
                                                    return

                                                # Log the shape of the dataframe
                                                st.info(f"Dataframe shape: {edited_latest_df.shape[0]} rows, {edited_latest_df.shape[1]} columns")

                                                # Update the database with the edited data
                                                try:
                                                    success, message = db_helper.update_test_cases_in_database(
                                                        db_helper.DATABASE_PATH,
                                                        edited_latest_df,
                                                        db_jira_id,
                                                        db_test_type,
                                                        current_user,
                                                        is_edited=True
                                                    )

                                                    if success:
                                                        st.success(f"✅ {message}")
                                                    else:
                                                        st.error(f"❌ {message}")
                                                        # Add to generation issues
                                                        if "generation_issues" not in st.session_state:
                                                            st.session_state.generation_issues = []
                                                        st.session_state.generation_issues.append(f"Failed to save test cases to database: {message}")
                                                except Exception as db_error:
                                                    st.error(f"Database error: {str(db_error)}")
                                                    import traceback
                                                    error_details = traceback.format_exc()
                                                    st.code(error_details)

                                                    # Add to generation issues
                                                    if "generation_issues" not in st.session_state:
                                                        st.session_state.generation_issues = []
                                                    st.session_state.generation_issues.append(f"Database error: {str(db_error)}")
                                            else:
                                                st.info("No changes detected. Nothing to save.")
                                        except Exception as e:
                                            st.error(f"Error preparing to save changes: {str(e)}")
                                            import traceback
                                            error_details = traceback.format_exc()
                                            st.code(error_details)

                                            # Add to generation issues
                                            if "generation_issues" not in st.session_state:
                                                st.session_state.generation_issues = []
                                            st.session_state.generation_issues.append(f"Error saving test cases: {str(e)}")

                                # Create a temporary Excel file with proper formatting
                                try:
                                    # Create a temporary directory if it doesn't exist
                                    temp_dir = Path("temp_excel")
                                    temp_dir.mkdir(exist_ok=True)

                                    # Generate a unique filename
                                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                    temp_file_path = temp_dir / f"{current_jira_id}_{current_test_type}_latest_test_cases_{timestamp}.xlsx"

                                    # Process the dataframe to group test steps with their parent test cases
                                    # This creates a more readable format similar to the dashboard display
                                    display_df = latest_df.copy()

                                    # Identify all unique test cases
                                    unique_test_cases = display_df['Test Case ID'].dropna().unique()

                                    # For each test case, make sure only the first row has the header information
                                    for test_case in unique_test_cases:
                                        # Get all rows for this test case
                                        test_case_rows = display_df[display_df['Test Case ID'] == test_case].index.tolist()

                                        if len(test_case_rows) > 1:
                                            # The first row should have step number 1
                                            first_row = None
                                            for idx in test_case_rows:
                                                step_no = display_df.loc[idx, 'Step No']
                                                if pd.notna(step_no) and (step_no == 1 or step_no == '1'):
                                                    first_row = idx
                                                    break

                                            # If we found the first row, clear header info from all other rows
                                            if first_row is not None:
                                                for idx in test_case_rows:
                                                    if idx != first_row:
                                                        # Clear fields that should only appear in the test case header
                                                        display_df.loc[idx, 'Timestamp'] = ''
                                                        display_df.loc[idx, 'Project'] = ''
                                                        display_df.loc[idx, 'Feature'] = ''
                                                        display_df.loc[idx, 'User Story ID'] = ''
                                                        display_df.loc[idx, 'Test Case ID'] = ''
                                                        display_df.loc[idx, 'Test Case Objective'] = ''
                                                        display_df.loc[idx, 'Prerequisite'] = ''
                                                        display_df.loc[idx, 'Priority'] = ''
                                                        display_df.loc[idx, 'Test Type'] = ''
                                                        display_df.loc[idx, 'Test Group'] = ''
                                                        if 'User Name' in display_df.columns:
                                                            display_df.loc[idx, 'User Name'] = ''
                                                        if 'Dashboard Test Type' in display_df.columns:
                                                            display_df.loc[idx, 'Dashboard Test Type'] = ''

                                    # Select only the columns that should be displayed in the Excel file
                                    # This matches the exact format requested
                                    excel_columns = [
                                        'Timestamp', 'Project', 'Feature', 'User Story ID',
                                        'Test Case ID', 'Test Case Objective', 'Prerequisite',
                                        'Step No', 'Test Steps', 'Expected Result', 'Actual Result',
                                        'Test Status', 'Priority', 'Defect ID', 'Comments',
                                        'Test Type', 'Test Group'
                                    ]

                                    # Filter the dataframe to only include the selected columns
                                    # Only include columns that exist in the dataframe
                                    excel_columns = [col for col in excel_columns if col in display_df.columns]
                                    excel_df = display_df[excel_columns]

                                    # Use the helper function to create a formatted Excel file with the processed dataframe
                                    from helpers import create_formatted_excel_from_scenarios
                                    create_formatted_excel_from_scenarios(
                                        excel_df,  # Use the filtered dataframe
                                        str(temp_file_path),
                                        is_dataframe=True,
                                        create_excel=True  # Explicitly create Excel file
                                    )

                                    # Create download button for the formatted Excel file
                                    with open(temp_file_path, "rb") as file:
                                        st.download_button(
                                            label="📥 Download Latest Test Cases",
                                            data=file,
                                            file_name=f"{current_jira_id}_{current_test_type}_latest_test_cases.xlsx",
                                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                            key="test_generator_download_latest_test_cases"
                                        )
                                except Exception as e:
                                    st.error(f"Error creating formatted Excel file: {str(e)}")
                                    # Fallback to simple Excel file if formatting fails
                                    temp_excel = io.BytesIO()
                                    latest_df.to_excel(temp_excel, index=False)
                                    temp_excel.seek(0)
                                    st.download_button(
                                        label="📥 Download Latest Test Cases (Simple Format)",
                                        data=temp_excel,
                                        file_name=f"{current_jira_id}_{current_test_type}_latest_test_cases.xlsx",
                                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                        key="test_generator_download_latest_test_cases"
                                    )
                            else:
                                st.error(f"No test cases found in the database for {current_jira_id} ({current_test_type})")
                        except Exception as e:
                            st.error(f"Error querying database directly: {e}")
                else:
                    st.info("Enter a JIRA ID to view the latest test cases.")
            except Exception as e:
                st.error(f"Error displaying latest test cases: {str(e)}")
                import traceback
                st.code(traceback.format_exc())    # Handle case where AI ran but didn't produce a parsable/savable output
    elif st.session_state.get("current_page", "generator") == "generator" and st.session_state.scenario_data and not st.session_state.scenario_data.get("output_file"):
        st.warning("AI generation was attempted, but no valid scenario Excel file was produced. Check 'Raw AI Output' if available.")

        # Create single tab for Raw AI Output since JIRA details are already shown in the expandable section
        tab1 = st.tabs(["💡 Raw AI Output"])[0]

        with tab1:
            st.markdown('<h2 class="sub-header">AI Response</h2>', unsafe_allow_html=True)
            if st.session_state.scenario_data.get("response"):
                st.text_area("Raw Output", st.session_state.scenario_data["response"], height=500)
            else:
                st.info("No AI response available. Generate test cases to see the raw output.")
