#!/usr/bin/env python3
"""
Google AI Studio Connection Validation Script for GretahAI CaseForge

This standalone script validates Google AI Studio connectivity and configuration
without disturbing existing application code. It provides comprehensive diagnostic
information to help troubleshoot integration issues.

Usage: python test_google_ai_connection.py
"""

import os
import json
import sys
import traceback
import requests
from datetime import datetime
from pathlib import Path

# Try to import Google AI library
try:
    from google import genai
    GENAI_AVAILABLE = True
except ImportError as e:
    GENAI_AVAILABLE = False
    GENAI_IMPORT_ERROR = str(e)

# Visual indicators
SUCCESS = "✅"
WARNING = "⚠️"
ERROR = "❌"
INFO = "ℹ️"

class GoogleAIValidator:
    """Comprehensive Google AI Studio connection validator"""
    
    def __init__(self):
        self.results = {
            'configuration': {},
            'authentication': {},
            'network': {},
            'service': {}
        }
        self.config_files = [
            'config.json',
            'admin_config.json',
            'GretahAI_ScriptWeaver/config.json',
            'GretahAI_ScriptWeaver/scriptweaver_config.json'
        ]
        self.api_key = None
        self.working_config_file = None
        
    def print_header(self, title):
        """Print a formatted section header"""
        print(f"\n{'='*60}")
        print(f" {title}")
        print(f"{'='*60}")
        
    def print_result(self, test_name, status, message, details=None):
        """Print a formatted test result"""
        icon = SUCCESS if status == 'pass' else WARNING if status == 'warning' else ERROR
        print(f"{icon} {test_name}: {message}")
        if details:
            for detail in details:
                print(f"   {detail}")
                
    def validate_configuration(self):
        """Validate configuration files and settings"""
        self.print_header("CONFIGURATION VALIDATION")
        
        # Check if we're in the correct directory
        current_dir = os.getcwd()
        print(f"{INFO} Current working directory: {current_dir}")
        
        # Check for configuration files
        found_configs = []
        config_data = {}
        
        for config_file in self.config_files:
            file_path = Path(config_file)
            if file_path.exists():
                found_configs.append(config_file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        config_data[config_file] = data
                        
                    self.print_result(
                        f"Config file: {config_file}",
                        'pass',
                        f"Found and parsed successfully",
                        [f"Size: {file_path.stat().st_size} bytes"]
                    )
                    
                    # Check for Google API key
                    if 'google_api_key' in data:
                        api_key = data['google_api_key']
                        if api_key and api_key.strip():
                            if not self.api_key:  # Use first valid API key found
                                self.api_key = api_key.strip()
                                self.working_config_file = config_file
                            self.print_result(
                                f"API key in {config_file}",
                                'pass',
                                f"Found (length: {len(api_key)} chars)",
                                [f"Starts with: {api_key[:10]}..." if len(api_key) > 10 else f"Value: {api_key}"]
                            )
                        else:
                            self.print_result(
                                f"API key in {config_file}",
                                'warning',
                                "Found but empty or whitespace"
                            )
                    
                except json.JSONDecodeError as e:
                    self.print_result(
                        f"Config file: {config_file}",
                        'error',
                        f"JSON parsing failed",
                        [f"Error: {str(e)}", f"Line: {e.lineno if hasattr(e, 'lineno') else 'unknown'}"]
                    )
                except Exception as e:
                    self.print_result(
                        f"Config file: {config_file}",
                        'error',
                        f"Read error: {str(e)}"
                    )
            else:
                self.print_result(
                    f"Config file: {config_file}",
                    'warning',
                    "Not found"
                )
        
        # Summary
        if not found_configs:
            self.print_result(
                "Configuration Summary",
                'error',
                "No configuration files found",
                ["Expected at least one of: " + ", ".join(self.config_files)]
            )
        else:
            self.print_result(
                "Configuration Summary",
                'pass' if self.api_key else 'warning',
                f"Found {len(found_configs)} config file(s)",
                [f"Working API key: {'Yes' if self.api_key else 'No'}",
                 f"Source: {self.working_config_file}" if self.working_config_file else "No valid API key found"]
            )
            
        self.results['configuration'] = {
            'found_configs': found_configs,
            'config_data': config_data,
            'api_key_found': bool(self.api_key),
            'working_config': self.working_config_file
        }
        
    def validate_authentication(self):
        """Validate API key format and authentication"""
        self.print_header("AUTHENTICATION VALIDATION")
        
        if not self.api_key:
            self.print_result(
                "API Key Validation",
                'error',
                "No API key available for testing",
                ["Please ensure google_api_key is set in one of the config files"]
            )
            return
            
        # Validate API key format
        if self.api_key.startswith('AIza'):
            self.print_result(
                "API Key Format",
                'pass',
                "Correct Google AI API key format",
                [f"Length: {len(self.api_key)} characters"]
            )
        else:
            self.print_result(
                "API Key Format",
                'warning',
                "Unexpected API key format",
                [f"Expected to start with 'AIza', got: {self.api_key[:10]}..."]
            )
            
        # Test API key functionality
        if not GENAI_AVAILABLE:
            self.print_result(
                "Google AI Library",
                'error',
                "google-generativeai library not available",
                [f"Import error: {GENAI_IMPORT_ERROR}",
                 "Install with: pip install google-generativeai"]
            )
            return

        try:
            # Initialize client
            client = genai.Client(api_key=self.api_key)
            self.print_result(
                "Client Initialization",
                'pass',
                "Google AI client created successfully"
            )

            # Test with a minimal API call
            test_prompt = "Hello, this is a test. Please respond with 'Connection successful.'"
            response = client.models.generate_content(
                model="gemini-2.0-flash",
                contents=test_prompt
            )

            if response and hasattr(response, 'text'):
                response_text = response.text.strip()
                token_count = None
                if hasattr(response, 'usage_metadata') and response.usage_metadata:
                    token_count = response.usage_metadata.total_token_count

                self.print_result(
                    "API Functionality Test",
                    'pass',
                    "Successfully generated content",
                    [f"Response length: {len(response_text)} characters",
                     f"Token count: {token_count}" if token_count else "Token count: Not available",
                     f"Response preview: {response_text[:100]}..." if len(response_text) > 100 else f"Response: {response_text}"]
                )
            else:
                self.print_result(
                    "API Functionality Test",
                    'warning',
                    "Received response but no text content",
                    [f"Response type: {type(response)}"]
                )

        except Exception as e:
            error_msg = str(e)
            error_type = type(e).__name__

            # Categorize common errors
            if "API_KEY_INVALID" in error_msg or "invalid API key" in error_msg.lower():
                self.print_result(
                    "API Functionality Test",
                    'error',
                    "Invalid API key",
                    [f"Error: {error_msg}",
                     "Solution: Check your Google AI Studio API key"]
                )
            elif "quota" in error_msg.lower() or "rate limit" in error_msg.lower():
                self.print_result(
                    "API Functionality Test",
                    'error',
                    "Quota or rate limit exceeded",
                    [f"Error: {error_msg}",
                     "Solution: Check your Google AI Studio quota and billing"]
                )
            elif "permission" in error_msg.lower() or "forbidden" in error_msg.lower():
                self.print_result(
                    "API Functionality Test",
                    'error',
                    "Permission denied",
                    [f"Error: {error_msg}",
                     "Solution: Verify API key permissions and model access"]
                )
            else:
                self.print_result(
                    "API Functionality Test",
                    'error',
                    f"API call failed ({error_type})",
                    [f"Error: {error_msg}",
                     "Check network connectivity and service status"]
                )

        self.results['authentication'] = {
            'api_key_format_valid': self.api_key.startswith('AIza') if self.api_key else False,
            'genai_available': GENAI_AVAILABLE,
            'api_key_length': len(self.api_key) if self.api_key else 0
        }

    def validate_network(self):
        """Validate network connectivity to Google AI services"""
        self.print_header("NETWORK CONNECTIVITY VALIDATION")

        # Test basic internet connectivity
        try:
            response = requests.get("https://www.google.com", timeout=10)
            if response.status_code == 200:
                self.print_result(
                    "Internet Connectivity",
                    'pass',
                    "Basic internet access working",
                    [f"Response time: {response.elapsed.total_seconds():.2f}s"]
                )
            else:
                self.print_result(
                    "Internet Connectivity",
                    'warning',
                    f"Unexpected response code: {response.status_code}"
                )
        except requests.exceptions.RequestException as e:
            self.print_result(
                "Internet Connectivity",
                'error',
                "No internet access",
                [f"Error: {str(e)}"]
            )

        # Test Google AI API endpoints
        google_ai_endpoints = [
            "https://generativelanguage.googleapis.com",
            "https://ai.google.dev"
        ]

        for endpoint in google_ai_endpoints:
            try:
                response = requests.get(endpoint, timeout=10)
                self.print_result(
                    f"Endpoint: {endpoint}",
                    'pass',
                    f"Reachable (Status: {response.status_code})",
                    [f"Response time: {response.elapsed.total_seconds():.2f}s"]
                )
            except requests.exceptions.RequestException as e:
                self.print_result(
                    f"Endpoint: {endpoint}",
                    'warning',
                    "Connection failed",
                    [f"Error: {str(e)}"]
                )

        self.results['network'] = {
            'internet_access': True,  # Will be updated based on actual tests
            'google_ai_endpoints_reachable': True  # Will be updated based on actual tests
        }

    def validate_service(self):
        """Validate Google AI service integration"""
        self.print_header("SERVICE INTEGRATION VALIDATION")

        if not GENAI_AVAILABLE or not self.api_key:
            self.print_result(
                "Service Validation",
                'error',
                "Cannot test service - missing prerequisites",
                ["Google AI library not available" if not GENAI_AVAILABLE else "",
                 "No API key available" if not self.api_key else ""]
            )
            return

        try:
            # Test model availability
            client = genai.Client(api_key=self.api_key)

            # Test the specific model used by the application
            model_name = "gemini-2.0-flash"
            test_prompt = "Test model availability"

            response = client.models.generate_content(
                model=model_name,
                contents=test_prompt
            )

            self.print_result(
                f"Model Access: {model_name}",
                'pass',
                "Model is accessible and responding",
                [f"Response received: {bool(response)}",
                 f"Has text: {hasattr(response, 'text') and bool(response.text)}"]
            )

            # Test multimodal capabilities (if supported)
            try:
                # This is a basic test - in real usage, you'd pass actual image data
                simple_response = client.models.generate_content(
                    model=model_name,
                    contents="Describe what you can do"
                )

                if simple_response and hasattr(simple_response, 'text'):
                    capabilities = simple_response.text
                    self.print_result(
                        "Model Capabilities",
                        'pass',
                        "Successfully queried model capabilities",
                        [f"Response length: {len(capabilities)} chars",
                         f"Preview: {capabilities[:150]}..." if len(capabilities) > 150 else capabilities]
                    )

            except Exception as e:
                self.print_result(
                    "Model Capabilities",
                    'warning',
                    f"Could not query capabilities: {str(e)}"
                )

        except Exception as e:
            self.print_result(
                "Service Integration",
                'error',
                f"Service integration failed: {str(e)}",
                [f"Error type: {type(e).__name__}"]
            )

        self.results['service'] = {
            'model_accessible': True,  # Will be updated based on actual tests
            'capabilities_tested': True  # Will be updated based on actual tests
        }

    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        self.print_header("DIAGNOSTIC SUMMARY REPORT")

        # Overall status assessment
        config_ok = self.results['configuration'].get('api_key_found', False)
        auth_ok = self.results['authentication'].get('genai_available', False) and \
                  self.results['authentication'].get('api_key_format_valid', False)

        if config_ok and auth_ok:
            overall_status = 'pass'
            status_msg = "Google AI Studio integration appears to be working"
        elif config_ok:
            overall_status = 'warning'
            status_msg = "Configuration found but authentication issues detected"
        else:
            overall_status = 'error'
            status_msg = "Critical configuration or authentication problems detected"

        self.print_result(
            "Overall Status",
            overall_status,
            status_msg
        )

        # Configuration summary
        config_summary = []
        if self.results['configuration'].get('found_configs'):
            config_summary.append(f"Found {len(self.results['configuration']['found_configs'])} config file(s)")
        if self.results['configuration'].get('working_config'):
            config_summary.append(f"Using API key from: {self.results['configuration']['working_config']}")

        if config_summary:
            self.print_result(
                "Configuration Status",
                'pass' if config_ok else 'error',
                "Configuration files processed",
                config_summary
            )

        # Authentication summary
        if GENAI_AVAILABLE:
            auth_summary = [
                f"Google AI library: Available",
                f"API key format: {'Valid' if self.results['authentication'].get('api_key_format_valid') else 'Invalid'}",
                f"API key length: {self.results['authentication'].get('api_key_length', 0)} chars"
            ]
            self.print_result(
                "Authentication Status",
                'pass' if auth_ok else 'warning',
                "Authentication components checked",
                auth_summary
            )
        else:
            self.print_result(
                "Authentication Status",
                'error',
                "Google AI library not available",
                ["Install with: pip install google-generativeai"]
            )

        # Troubleshooting recommendations
        if overall_status != 'pass':
            self.print_header("TROUBLESHOOTING RECOMMENDATIONS")

            if not config_ok:
                print(f"{ERROR} Configuration Issues:")
                print("   1. Ensure config.json exists in the project root")
                print("   2. Add 'google_api_key' field to config.json")
                print("   3. Get API key from: https://aistudio.google.com/")
                print()

            if not GENAI_AVAILABLE:
                print(f"{ERROR} Library Issues:")
                print("   1. Install Google AI library: pip install google-generativeai")
                print("   2. Restart your application after installation")
                print()

            if config_ok and not auth_ok:
                print(f"{ERROR} Authentication Issues:")
                print("   1. Verify API key is correct and active")
                print("   2. Check Google AI Studio billing and quotas")
                print("   3. Ensure API key has proper permissions")
                print()

        # Integration context
        self.print_header("INTEGRATION CONTEXT")
        print(f"{INFO} This validation script tests the same Google AI integration used by:")
        print("   • GretahAI CaseForge (helpers.py - run_google_ai_studio function)")
        print("   • GretahAI ScriptWeaver (core/ai.py - generate_llm_response function)")
        print("   • GretahAI TestInsight (helper.py - run_google_ai_studio function)")
        print()
        print(f"{INFO} Default model: gemini-2.0-flash")
        print(f"{INFO} Expected config files:")
        for config_file in self.config_files:
            status = "✓" if config_file in self.results['configuration'].get('found_configs', []) else "✗"
            print(f"   {status} {config_file}")

    def run_all_validations(self):
        """Run all validation tests"""
        print("Google AI Studio Connection Validation Script")
        print("=" * 60)
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Working Directory: {os.getcwd()}")
        print(f"Python Version: {sys.version}")

        try:
            self.validate_configuration()
            self.validate_authentication()
            self.validate_network()
            self.validate_service()
            self.generate_summary_report()

        except KeyboardInterrupt:
            print(f"\n{WARNING} Validation interrupted by user")
        except Exception as e:
            print(f"\n{ERROR} Unexpected error during validation:")
            print(f"   Error: {str(e)}")
            print(f"   Type: {type(e).__name__}")
            traceback.print_exc()

        print(f"\n{INFO} Validation completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def main():
    """Main execution function"""
    validator = GoogleAIValidator()
    validator.run_all_validations()


if __name__ == "__main__":
    main()
