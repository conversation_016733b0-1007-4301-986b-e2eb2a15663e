# GretahAI ScriptWeaver Visual Diagrams

**Comprehensive Visual Documentation with Mermaid Diagrams**

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized use, copying, or distribution is strictly prohibited.

---

## 📊 Overview

This document provides a comprehensive index of all visual diagrams included in the GretahAI ScriptWeaver documentation. All diagrams are created using Mermaid syntax and can be rendered in any Markdown viewer that supports Mermaid diagrams.

## 🗂️ Diagram Index

### **User Workflow Diagrams**

#### 1. Complete User Workflow (USER_GUIDE.md)
**Location**: `docs/USER_GUIDE.md` - Line 54
**Purpose**: Visual representation of the complete 10-stage user workflow
**Type**: Flowchart
**Description**: Shows the entire user journey from Excel upload through template-based script generation

#### 2. Stage 10 Detailed Workflow (USER_GUIDE.md)
**Location**: `docs/USER_GUIDE.md` - Line 444
**Purpose**: Detailed workflow for Stage 10 template-based script generation
**Type**: Flowchart
**Description**: Step-by-step process for template selection, gap analysis, and script generation

### **System Architecture Diagrams**

#### 3. High-Level System Architecture (DEVELOPER_GUIDE.md)
**Location**: `docs/DEVELOPER_GUIDE.md` - Line 28
**Purpose**: Complete system architecture with all layers and components
**Type**: Layered Architecture Diagram
**Description**: Shows presentation, business logic, AI integration, data, and external service layers

#### 4. Component Architecture Hierarchy (DEVELOPER_GUIDE.md)
**Location**: `docs/DEVELOPER_GUIDE.md` - Line 79
**Purpose**: Component relationships and dependencies
**Type**: Hierarchical Diagram
**Description**: Application core, stage layer, service layer, and data layer components

#### 5. AI Integration Architecture (DEVELOPER_GUIDE.md)
**Location**: `docs/DEVELOPER_GUIDE.md` - Line 272
**Purpose**: AI request processing and response handling flow
**Type**: Process Flow Diagram
**Description**: AI request layer, processing layer, response processing, and logging/monitoring

### **State Management Diagrams**

#### 6. State Transition Flow (DEVELOPER_GUIDE.md)
**Location**: `docs/DEVELOPER_GUIDE.md` - Line 162
**Purpose**: Application state transitions between stages
**Type**: State Diagram
**Description**: Complete state machine showing all stage transitions and conditions

### **Testing Architecture Diagrams**

#### 7. Testing Framework Architecture (DEVELOPER_GUIDE.md)
**Location**: `docs/DEVELOPER_GUIDE.md` - Line 653
**Purpose**: Testing layers and infrastructure organization
**Type**: Layered Architecture Diagram
**Description**: Test layers, categories, and infrastructure components

## 🎨 Diagram Rendering

### **GitHub/GitLab Rendering**
All diagrams will render automatically when viewing the documentation files on GitHub, GitLab, or any platform that supports Mermaid diagrams.

### **Local Rendering**
To render diagrams locally, you can use:

1. **Mermaid Live Editor**: https://mermaid.live/
2. **VS Code Extensions**: Mermaid Preview, Markdown Preview Enhanced
3. **Browser Extensions**: Mermaid diagrams for Chrome/Firefox
4. **Command Line Tools**: mermaid-cli for generating SVG/PNG files

### **Generating Static Images**

If you need static image files (SVG/PNG), you can use the Mermaid CLI:

```bash
# Install Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# Generate SVG from diagram
mmdc -i diagram.mmd -o diagram.svg

# Generate PNG from diagram
mmdc -i diagram.mmd -o diagram.png
```

## 📋 Diagram Details

### **1. Complete User Workflow**
```
Location: docs/USER_GUIDE.md (Line 54)
Stages Covered: All 10 stages plus decision points
Key Features: Hybrid editing, template generation, script optimization
Visual Elements: Color-coded AI operations, decision diamonds, process flows
```

### **2. Stage 10 Detailed Workflow**
```
Location: docs/USER_GUIDE.md (Line 444)
Focus: Template-based script generation process
Key Features: Gap analysis, template selection, script execution
Visual Elements: Color-coded process types, validation loops, decision points
```

### **3. High-Level System Architecture**
```
Location: docs/DEVELOPER_GUIDE.md (Line 28)
Layers: 5 distinct architectural layers
Components: 20+ system components with relationships
Visual Elements: Color-coded layers, component groupings, data flow arrows
```

### **4. Component Architecture Hierarchy**
```
Location: docs/DEVELOPER_GUIDE.md (Line 79)
Structure: Hierarchical component organization
Relationships: Component dependencies and interactions
Visual Elements: Grouped components, clear hierarchy, connection flows
```

### **5. AI Integration Architecture**
```
Location: docs/DEVELOPER_GUIDE.md (Line 272)
Process Flow: AI request lifecycle from initiation to completion
Components: Request routing, processing, response handling, monitoring
Visual Elements: Process flow, component groupings, monitoring integration
```

### **6. State Transition Flow**
```
Location: docs/DEVELOPER_GUIDE.md (Line 162)
State Machine: Complete application state transitions
Transitions: All valid stage transitions and conditions
Visual Elements: State nodes, transition arrows, condition labels
```

### **7. Testing Framework Architecture**
```
Location: docs/DEVELOPER_GUIDE.md (Line 653)
Testing Layers: Unit, integration, E2E, performance testing
Infrastructure: Test fixtures, mock services, test data, utilities
Visual Elements: Layered structure, component relationships, test flow
```

## 🔧 Customization Guidelines

### **Modifying Diagrams**
When updating diagrams, follow these guidelines:

1. **Maintain Consistency**: Use consistent styling and color schemes
2. **Clear Labels**: Ensure all nodes and connections are clearly labeled
3. **Logical Flow**: Maintain logical flow direction (top-to-bottom, left-to-right)
4. **Color Coding**: Use colors to group related components or highlight important elements
5. **Documentation**: Update this index when adding or modifying diagrams

### **Color Scheme Standards**
```
Primary Process: #e1f5fe (Light Blue)
AI Operations: #fff3e0 (Light Orange)
Data Storage: #e8f5e8 (Light Green)
External Services: #ffebee (Light Red)
UI Components: #e3f2fd (Light Blue)
State Management: #f3e5f5 (Light Purple)
Success/Completion: #c8e6c9 (Light Green)
```

### **Adding New Diagrams**
When adding new diagrams:

1. **Choose Appropriate Location**: Place in the most relevant documentation file
2. **Update This Index**: Add entry to the diagram index above
3. **Follow Naming Conventions**: Use descriptive titles and consistent formatting
4. **Test Rendering**: Verify diagram renders correctly in target environments
5. **Document Purpose**: Clearly explain the diagram's purpose and scope

## 📚 Related Documentation

### **Documentation Files with Diagrams**
- [User Guide](USER_GUIDE.md) - User workflow and process diagrams
- [Developer Guide](DEVELOPER_GUIDE.md) - System architecture and technical diagrams
- [API Documentation](API.md) - API structure and integration diagrams (if any)

### **External Resources**
- [Mermaid Documentation](https://mermaid-js.github.io/mermaid/)
- [Mermaid Live Editor](https://mermaid.live/)
- [Mermaid Syntax Guide](https://mermaid-js.github.io/mermaid/#/flowchart)

---

## 📞 Support & Maintenance

### **Diagram Updates**
For enterprise customers requiring custom diagrams or modifications:

**Primary Contact**: <EMAIL>

**Available Services**:
- Custom diagram development for specific business processes
- Architecture diagram updates for enterprise deployments
- Training materials with visual workflow documentation
- Professional diagram design and optimization

### **Technical Support**
For issues with diagram rendering or technical questions:

- **Enterprise Support**: Contact your dedicated support representative
- **General Inquiries**: Email <EMAIL> with diagram-related questions
- **Documentation Updates**: Include diagram improvements in support requests

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized copying, distribution, modification, or use of this software is strictly prohibited.

**Commercial Licensing**: Contact <EMAIL> for licensing inquiries and enterprise diagram development services.

**Note**: This software requires a valid commercial license for use. Custom diagram development services are available for qualified enterprise customers.
