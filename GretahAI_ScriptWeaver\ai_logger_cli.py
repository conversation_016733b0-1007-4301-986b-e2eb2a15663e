#!/usr/bin/env python
"""
AI Logger CLI - Command-line interface for the AI logger.

This script provides a command-line interface for generating AI usage reports
and managing AI logs.
"""

import os
import sys
import argparse
from datetime import datetime, timedelta
import json
from pathlib import Path

# Add the parent directory to the path so we can import the core module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.ai import (
    AI_LOGS_DIR,
    AI_LOGS_REQUESTS_DIR,
    AI_LOGS_ERRORS_DIR,
    AI_LOGS_METRICS_DIR
)
from core.ai_helpers import (
    get_daily_usage_summary,
    generate_usage_report
)

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="AI Logger CLI")

    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # Report command
    report_parser = subparsers.add_parser("report", help="Generate AI usage report")
    report_parser.add_argument(
        "--start-date",
        help="Start date in YYYYMMDD format (default: 7 days ago)",
        default=(datetime.now() - timedelta(days=7)).strftime("%Y%m%d")
    )
    report_parser.add_argument(
        "--end-date",
        help="End date in YYYYMMDD format (default: today)",
        default=datetime.now().strftime("%Y%m%d")
    )
    report_parser.add_argument(
        "--format",
        choices=["text", "json", "html"],
        default="text",
        help="Output format (default: text)"
    )
    report_parser.add_argument(
        "--output",
        help="Output file (default: stdout)"
    )

    # Summary command
    summary_parser = subparsers.add_parser("summary", help="Generate daily usage summary")
    summary_parser.add_argument(
        "--date",
        help="Date in YYYYMMDD format (default: today)",
        default=datetime.now().strftime("%Y%m%d")
    )
    summary_parser.add_argument(
        "--format",
        choices=["text", "json"],
        default="text",
        help="Output format (default: text)"
    )
    summary_parser.add_argument(
        "--output",
        help="Output file (default: stdout)"
    )

    # List command
    list_parser = subparsers.add_parser("list", help="List AI logs")
    list_parser.add_argument(
        "--type",
        choices=["requests", "errors", "metrics", "all"],
        default="all",
        help="Type of logs to list (default: all)"
    )
    list_parser.add_argument(
        "--limit",
        type=int,
        default=10,
        help="Maximum number of logs to list (default: 10)"
    )
    list_parser.add_argument(
        "--category",
        help="Filter logs by category"
    )
    list_parser.add_argument(
        "--function",
        help="Filter logs by function name"
    )

    # Clean command
    clean_parser = subparsers.add_parser("clean", help="Clean old AI logs")
    clean_parser.add_argument(
        "--days",
        type=int,
        default=30,
        help="Remove logs older than this many days (default: 30)"
    )
    clean_parser.add_argument(
        "--type",
        choices=["requests", "errors", "metrics", "all"],
        default="all",
        help="Type of logs to clean (default: all)"
    )
    clean_parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Don't actually delete files, just show what would be deleted"
    )

    return parser.parse_args()

def format_summary(summary, output_format="text"):
    """Format a daily usage summary."""
    if output_format == "json":
        return json.dumps(summary, indent=2)

    # Text format
    text = []
    text.append("=" * 80)
    text.append(f"AI USAGE SUMMARY: {summary['date']}")
    text.append("=" * 80)

    text.append(f"\nTotal Requests:      {summary['total_requests']:,}")
    text.append(f"Total Input Tokens:  {summary['total_input_tokens']:,}")
    text.append(f"Total Output Tokens: {summary['total_output_tokens']:,}")
    text.append(f"Total Tokens:        {summary['total_tokens']:,}")
    text.append(f"Error Count:         {summary['error_count']:,}")
    text.append(f"Avg Latency:         {summary['avg_latency_ms']:.2f} ms")
    text.append(f"Estimated Cost:      ${summary.get('total_estimated_cost', 0):.4f}")

    # Requests by category
    if summary.get('requests_by_category'):
        text.append("\nRequests by Category:")
        for category, count in sorted(summary['requests_by_category'].items(), key=lambda x: x[1], reverse=True):
            text.append(f"  {category:<30} {count:,}")

    # Requests by model
    if summary.get('requests_by_model'):
        text.append("\nRequests by Model:")
        for model, count in sorted(summary['requests_by_model'].items(), key=lambda x: x[1], reverse=True):
            text.append(f"  {model:<30} {count:,}")

    # Requests by function
    if summary.get('requests_by_function'):
        text.append("\nRequests by Function:")
        for function, count in sorted(summary['requests_by_function'].items(), key=lambda x: x[1], reverse=True):
            text.append(f"  {function:<30} {count:,}")

    return "\n".join(text)

def list_logs(log_type="all", limit=10, category=None, function=None):
    """List AI logs."""
    logs = []

    # Determine which directories to search
    dirs_to_search = []
    if log_type == "all" or log_type == "requests":
        dirs_to_search.append(AI_LOGS_REQUESTS_DIR)
    if log_type == "all" or log_type == "errors":
        dirs_to_search.append(AI_LOGS_ERRORS_DIR)
    if log_type == "all" or log_type == "metrics":
        dirs_to_search.append(AI_LOGS_METRICS_DIR)

    # Collect log files
    for directory in dirs_to_search:
        if not os.path.exists(directory):
            continue

        for filename in os.listdir(directory):
            if not filename.endswith(".txt") and not filename.endswith(".csv"):
                continue

            # Apply filters
            if category and category not in filename:
                continue
            if function and function not in filename:
                continue

            filepath = os.path.join(directory, filename)
            logs.append({
                "path": filepath,
                "filename": filename,
                "type": "metrics" if filename.endswith(".csv") else ("errors" if "errors" in filepath else "requests"),
                "size": os.path.getsize(filepath),
                "modified": datetime.fromtimestamp(os.path.getmtime(filepath))
            })

    # Sort by modification time (newest first)
    logs.sort(key=lambda x: x["modified"], reverse=True)

    # Limit the number of logs
    logs = logs[:limit]

    return logs

def clean_logs(days=30, log_type="all", dry_run=False):
    """Clean old AI logs."""
    # Calculate the cutoff date
    cutoff_date = datetime.now() - timedelta(days=days)

    # Determine which directories to clean
    dirs_to_clean = []
    if log_type == "all" or log_type == "requests":
        dirs_to_clean.append(AI_LOGS_REQUESTS_DIR)
    if log_type == "all" or log_type == "errors":
        dirs_to_clean.append(AI_LOGS_ERRORS_DIR)
    if log_type == "all" or log_type == "metrics":
        dirs_to_clean.append(AI_LOGS_METRICS_DIR)

    # Collect files to delete
    files_to_delete = []

    for directory in dirs_to_clean:
        if not os.path.exists(directory):
            continue

        for filename in os.listdir(directory):
            filepath = os.path.join(directory, filename)

            # Skip directories
            if os.path.isdir(filepath):
                continue

            # Check if the file is older than the cutoff date
            modified_time = datetime.fromtimestamp(os.path.getmtime(filepath))
            if modified_time < cutoff_date:
                files_to_delete.append({
                    "path": filepath,
                    "filename": filename,
                    "size": os.path.getsize(filepath),
                    "modified": modified_time
                })

    # Sort by modification time (oldest first)
    files_to_delete.sort(key=lambda x: x["modified"])

    # Delete the files
    deleted_count = 0
    deleted_size = 0

    for file_info in files_to_delete:
        if dry_run:
            print(f"Would delete: {file_info['path']} ({file_info['modified'].strftime('%Y-%m-%d %H:%M:%S')})")
        else:
            try:
                os.remove(file_info["path"])
                deleted_count += 1
                deleted_size += file_info["size"]
            except Exception as e:
                print(f"Error deleting {file_info['path']}: {e}")

    return {
        "deleted_count": deleted_count,
        "deleted_size": deleted_size,
        "would_delete_count": len(files_to_delete) if dry_run else 0,
        "would_delete_size": sum(f["size"] for f in files_to_delete) if dry_run else 0
    }

def main():
    """Main entry point."""
    args = parse_args()

    if args.command == "report":
        # Generate a usage report
        report = generate_usage_report(
            start_date=args.start_date,
            end_date=args.end_date,
            output_format=args.format
        )

        if args.output:
            with open(args.output, "w", encoding="utf-8") as f:
                f.write(report)
            print(f"Report written to {args.output}")
        else:
            print(report)

    elif args.command == "summary":
        # Generate a daily usage summary
        summary = get_daily_usage_summary(args.date)
        formatted_summary = format_summary(summary, args.format)

        if args.output:
            with open(args.output, "w", encoding="utf-8") as f:
                f.write(formatted_summary)
            print(f"Summary written to {args.output}")
        else:
            print(formatted_summary)

    elif args.command == "list":
        # List AI logs
        logs = list_logs(args.type, args.limit, args.category, args.function)

        if not logs:
            print("No logs found.")
            return

        print(f"Found {len(logs)} logs:")
        for log in logs:
            print(f"{log['modified'].strftime('%Y-%m-%d %H:%M:%S')} - {log['type']} - {log['filename']} ({log['size']:,} bytes)")

    elif args.command == "clean":
        # Clean old AI logs
        result = clean_logs(args.days, args.type, args.dry_run)

        if args.dry_run:
            print(f"Would delete {result['would_delete_count']} files ({result['would_delete_size']:,} bytes)")
        else:
            print(f"Deleted {result['deleted_count']} files ({result['deleted_size']:,} bytes)")

    else:
        print("No command specified. Use --help for usage information.")

if __name__ == "__main__":
    main()
