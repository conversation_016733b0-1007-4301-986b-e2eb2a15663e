'''
Pytest configuration file for automated test framework.
This module provides fixtures, hooks, and utilities for UI test automation with Selenium.
It handles browser instantiation, test logging, screenshot capture, and reporting.
Constants:
    SCREENSHOT_DIR: Directory where test screenshots are saved
    LOG_DIR: Directory for all log files
    TEST_LOGS_DIR: Directory for test-specific logs
    PAGE_SOURCE_DIR: Directory for HTML page sources
Fixtures:
    setup_session: Session-level fixture for test session setup and teardown
    browser: Creates and manages a WebDriver instance with automatic cleanup
    take_timed_screenshots: Allows taking screenshots during test execution
    log_message: Enables adding custom log messages from tests
    performance_monitor: Tracks and reports test execution time
Hooks:
    pytest_runtest_makereport: Captures test artifacts (screenshots, page source)
    pytest_terminal_summary: Generates test execution summary report
Utility Functions:
    setup_logging: Configures global logging
    add_test_log_handler: Creates test-specific log handlers
    take_screenshot: Captures and saves browser screenshots
The framework automatically:
- Creates unique log files for each test
- Takes screenshots on test failures
- Saves page source for debugging
- Generates execution summary reports
- Tracks browser console logs
'''

import logging
import os
import time
import pytest
import json
import psutil
import xml.etree.ElementTree as ET
import sys
import platform
import traceback
from datetime import datetime
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import WebDriverException

# Constants
SCREENSHOT_DIR = Path("screenshots")
SCREENSHOT_DIR.mkdir(exist_ok=True)
LOG_DIR = Path("logs")
LOG_DIR.mkdir(exist_ok=True)
TEST_LOGS_DIR = LOG_DIR / "test_logs"
TEST_LOGS_DIR.mkdir(exist_ok=True)
PAGE_SOURCE_DIR = LOG_DIR / "page_sources"
PAGE_SOURCE_DIR.mkdir(exist_ok=True)

# Configure logging
def setup_logging():
    """Configure logging for test execution"""
    log_file = LOG_DIR / f"test_execution_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

    # Ensure UTF-8 encoding for log files
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger("TestLogger")

# Function to create test-specific log handler
def add_test_log_handler(item):
    """Add a test-specific log handler and return its path."""
    test_name = item.function.__name__
    class_name = item.cls.__name__ if item.cls else "nogroup" # Use 'nogroup' if not in a class

    log_filename = f"{class_name}_{test_name}.log"
    test_log_file = TEST_LOGS_DIR / log_filename

    # Check if handler for this file already exists
    for h in logger.handlers:
        if isinstance(h, logging.FileHandler) and Path(h.baseFilename) == test_log_file:
            # Return existing handler and its path
            return h, str(test_log_file.resolve())

    # Use UTF-8 encoding for the log file
    handler = logging.FileHandler(test_log_file, mode='w', encoding='utf-8')
    handler.setLevel(logging.INFO)
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)
    logger.info(f"Test-specific log handler added: {test_log_file}")
    # Return the new handler and its path
    return handler, str(test_log_file.resolve())

# Helper function to use appropriate status symbols based on platform
def get_status_symbol(status):
    """Return an appropriate status symbol that works across platforms."""
    # Check if we're on Windows and in a terminal that doesn't support Unicode well
    is_windows = platform.system() == 'Windows'

    if is_windows:
        # Use ASCII alternatives on Windows
        if status == 'PASSED':
            return '[PASS]'
        elif status == 'FAILED':
            return '[FAIL]'
        elif status == 'SKIPPED':
            return '[SKIP]'
    else:
        # Use emoji on other platforms
        if status == 'PASSED':
            return '✅'
        elif status == 'FAILED':
            return '❌'
        elif status == 'SKIPPED':
            return '⚠️'

    # Default fallback
    return f'[{status}]'

# Global logger instance
logger = setup_logging()

# Helper functions for performance monitoring
def get_memory_usage():
    """Get current memory usage in MB"""
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        # Convert to MB for readability
        return memory_info.rss / (1024 * 1024)
    except (ImportError, AttributeError, NameError) as e:
        logger.warning(f"Could not get memory usage: {e}. Make sure psutil is installed (pip install psutil)")
        return 0

def get_cpu_usage():
    """Get current CPU usage percentage"""
    try:
        return psutil.cpu_percent(interval=0.1)
    except (ImportError, AttributeError, NameError) as e:
        logger.warning(f"Could not get CPU usage: {e}. Make sure psutil is installed (pip install psutil)")
        return 0

def get_browser_performance_metrics(driver):
    """Get browser performance metrics using the PerformanceMetrics class"""
    try:
        # Try to get metrics directly using JavaScript
        metrics = {}
        try:
            # Get performance timing metrics using JavaScript
            timing = driver.execute_script("return window.performance.timing")

            if timing:
                # Calculate key metrics
                navigation_start = timing.get('navigationStart', 0)

                # Only calculate if navigationStart is valid
                if navigation_start > 0:
                    # Page load time (from navigation start to load event end)
                    if timing.get('loadEventEnd', 0) > 0:
                        metrics['total_page_load'] = timing.get('loadEventEnd', 0) - navigation_start

            logger.info(f"Collected browser performance metrics directly: {metrics}")
            return metrics
        except Exception as direct_e:
            logger.warning(f"Failed to collect browser metrics directly: {direct_e}")
            return {}
    except Exception as e:
        logger.error(f"Failed to get browser performance metrics: {e}")
        return {}

def add_performance_metrics_to_xml(xml_file, test_metrics):
    """
    Directly add performance metrics to the XML file for each test case.
    This is a backup method to ensure metrics are added to the XML.
    """
    try:
        # Parse the XML file
        tree = ET.parse(xml_file)
        root = tree.getroot()

        # Add overall performance metrics as a property to the root
        root_properties = root.find('properties')
        if root_properties is None:
            root_properties = ET.SubElement(root, 'properties')

        # Calculate overall performance metrics
        overall_metrics = {
            "avg_execution_time": 0,
            "avg_memory_usage": 0,
            "avg_cpu_usage": 0,
            "avg_network_requests": 0,
            "avg_network_bytes": 0,
            "avg_page_load_time": 0, # Added page load time
            "max_execution_time": 0,
            "max_memory_usage": 0,
            "max_cpu_usage": 0,
            "max_network_requests": 0,
            "max_network_bytes": 0,
            "max_page_load_time": 0, # Added page load time
            "total_network_requests": 0,
            "total_network_bytes": 0
        }

        if test_metrics:
            for metric in test_metrics:
                overall_metrics["avg_execution_time"] += metric.get('execution_time', 0)
                overall_metrics["avg_memory_usage"] += metric.get('memory_usage', 0)
                overall_metrics["avg_cpu_usage"] += metric.get('cpu_usage', 0)
                # Add network metrics to the averages
                overall_metrics["avg_network_requests"] += metric.get('network_requests', 0)
                overall_metrics["avg_network_bytes"] += metric.get('network_bytes', 0)
                overall_metrics["avg_page_load_time"] += metric.get('page_load_time', 0) # Add to average page load time
                overall_metrics["max_execution_time"] = max(overall_metrics["max_execution_time"], metric.get('execution_time', 0))
                overall_metrics["max_memory_usage"] = max(overall_metrics["max_memory_usage"], metric.get('memory_usage', 0))
                overall_metrics["max_cpu_usage"] = max(overall_metrics["max_cpu_usage"], metric.get('cpu_usage', 0))
                # Track max network metrics
                overall_metrics["max_network_requests"] = max(overall_metrics["max_network_requests"], metric.get('network_requests', 0))
                overall_metrics["max_network_bytes"] = max(overall_metrics["max_network_bytes"], metric.get('network_bytes', 0))
                overall_metrics["max_page_load_time"] = max(overall_metrics["max_page_load_time"], metric.get('page_load_time', 0)) # Track max page load time
                # Add to totals
                overall_metrics["total_network_requests"] += metric.get('network_requests', 0)
                overall_metrics["total_network_bytes"] += metric.get('network_bytes', 0)

            # Calculate averages
            metric_count = len(test_metrics)
            if metric_count > 0:
                overall_metrics["avg_execution_time"] /= metric_count
                overall_metrics["avg_memory_usage"] /= metric_count
                overall_metrics["avg_cpu_usage"] /= metric_count
                overall_metrics["avg_network_requests"] /= metric_count
                overall_metrics["avg_network_bytes"] /= metric_count
                overall_metrics["avg_page_load_time"] /= metric_count # Calculate average page load time

        # Add or update overall metrics property
        found_overall = False
        for prop in root_properties.findall('property'):
            if prop.get('name') == 'performance_metrics':
                prop.set('value', json.dumps(overall_metrics))
                found_overall = True
                break

        if not found_overall:
            prop = ET.SubElement(root_properties, 'property')
            prop.set('name', 'performance_metrics')
            prop.set('value', json.dumps(overall_metrics))

        # Process each test case in the XML
        for testsuite in root.findall('.//testsuite'):
            for testcase in testsuite.findall('.//testcase'):
                classname = testcase.get('classname')
                name = testcase.get('name')

                # Find matching test metrics
                matching_metrics = None
                for metrics in test_metrics:
                    test_id = metrics.get('test_id', '')
                    if '::' in test_id:
                        test_class, test_name = test_id.split('::')
                        # Clean up class name for comparison
                        if '/' in test_class:
                            test_class = test_class.split('/')[-1]
                        if '\\' in test_class:
                            test_class = test_class.split('\\')[-1]
                        if '.' in test_class:
                            test_class = test_class.split('.')[-1]

                        if (test_class == classname or classname.endswith(test_class)) and test_name == name:
                            matching_metrics = metrics
                            break

                if matching_metrics:
                    # Check if this test case already has properties
                    properties = testcase.find('properties')
                    if properties is None:
                        properties = ET.SubElement(testcase, 'properties')

                    # Remove any existing performance_metrics properties to avoid duplicates
                    for prop in properties.findall('property'):
                        if prop.get('name') == 'performance_metrics' or prop.get('name') == 'performance_metrics_json':
                            properties.remove(prop)

                    # Add simplified performance metrics
                    simplified_metrics = {
                        'execution_time': matching_metrics.get('execution_time', 0),
                        'memory_usage': matching_metrics.get('memory_usage', 0),
                        'cpu_usage': matching_metrics.get('cpu_usage', 0),
                        'network_requests': matching_metrics.get('network_requests', 0),
                        'network_bytes': matching_metrics.get('network_bytes', 0)
                    }

                    # Add page load time if available
                    if matching_metrics.get('page_load_time', 0) > 0:
                        simplified_metrics['page_load_time'] = matching_metrics.get('page_load_time', 0)

                    metrics_json = json.dumps(simplified_metrics)
                    prop = ET.SubElement(properties, 'property')
                    prop.set('name', 'performance_metrics')
                    prop.set('value', metrics_json)
                    logger.info(f"Added simplified performance metrics to test case {classname}::{name} in XML")

        # Save the XML file
        tree.write(xml_file)
        logger.info(f"Updated XML file with performance metrics: {xml_file}")
        return True
    except Exception as e:
        logger.error(f"Failed to add performance metrics to XML file: {e}")
        logger.error(traceback.format_exc())
        return False

def inspect_performance_logs(browser):
    """Utility function to inspect and debug performance logs"""
    try:
        logs = browser.get_log('performance')
        log_summary = {}
        
        # Analyze what kinds of entries we have
        for log in logs:
            try:
                message = json.loads(log.get('message', '{}'))
                method = message.get('message', {}).get('method', 'unknown')
                
                if method not in log_summary:
                    log_summary[method] = 0
                log_summary[method] += 1
                
                # Special case: explore a sample of network events
                if method.startswith('Network.') and log_summary[method] <= 2:
                    logger.debug(f"Sample {method} entry: {json.dumps(message.get('message', {}).get('params', {}), indent=2)[:500]}")
            except:
                pass
                
        # Log summary of what we found
        logger.debug(f"Performance log summary: {json.dumps(log_summary, indent=2)}")
        
        # If we have network entries, summarize them
        network_methods = [method for method in log_summary if method.startswith('Network.')]
        if network_methods:
            logger.info(f"Found {sum(log_summary[m] for m in network_methods)} network-related log entries "
                        f"of types: {', '.join(network_methods)}")
        else:
            logger.warning("No Network-related entries found in performance logs")
    except Exception as e:
        logger.error(f"Error inspecting performance logs: {e}")
        logger.error(traceback.format_exc())
        
    return log_summary

def parse_network_metrics_from_logs(performance_logs):
    """
    Parses raw performance log entries to extract network requests and bytes.
    Returns a dictionary with 'network_requests' and 'network_bytes' (in KB).
    """
    network_requests = 0
    network_bytes = 0
    urls_loaded = set() # Track unique request IDs

    if not performance_logs:
        return {"network_requests": 0, "network_bytes": 0, "urls_accessed": 0}

    for log in performance_logs:
        log_message = log.get('message', '{}')
        try:
            log_data = json.loads(log_message)
            method = log_data.get('message', {}).get('method', '')
            params = log_data.get('message', {}).get('params', {})

            # Count network requests based on requestWillBeSent or responseReceived
            if any(net_event in method for net_event in [
                'Network.requestWillBeSent',
                'Network.responseReceived',
                'Network.loadingFinished' # Also count finished requests
            ]):
                request_id = params.get('requestId')
                if request_id:
                    # Ensure we only count each unique request once
                    if request_id not in urls_loaded:
                        urls_loaded.add(request_id)
                        network_requests += 1

            # Get data size from dataReceived or loadingFinished
            if method == 'Network.dataReceived':
                data_length = params.get('dataLength', 0)
                network_bytes += data_length

            if method == 'Network.loadingFinished':
                 # Use encodedDataLength if available, as it's often the actual bytes transferred
                encoded_data_length = params.get('encodedDataLength', 0)
                if encoded_data_length > 0:
                     # Add to total bytes, avoid double counting if dataReceived was also processed for this request
                     # A more sophisticated approach might track requests by ID, but this is a reasonable estimate
                     network_bytes += encoded_data_length


        except json.JSONDecodeError:
            # Ignore logs that aren't valid JSON
            continue
        except Exception as parse_err:
            logger.debug(f"Error parsing performance log entry: {parse_err}")

    # Fallback/estimation if no logs were captured but a browser was used
    # This part might be less necessary now that logs are captured before quit,
    # but keep as a safeguard.
    # Note: This fallback logic was previously in performance_monitor, moved here.
    if network_requests == 0 and len(urls_loaded) > 0: # If we tracked URLs but didn't count requests via methods
         network_requests = len(urls_loaded) # Assume one request per tracked URL

    if network_bytes == 0 and network_requests > 0:
         # Estimate minimum data if requests were counted but no bytes were recorded
         network_bytes = network_requests * 10240 # Estimate 10KB per request

    return {
        "network_requests": network_requests,
        "network_bytes": network_bytes / 1024, # Convert to KB
        "urls_accessed": len(urls_loaded) # Keep track of unique URLs/requests seen
    }


@pytest.fixture(scope="session", autouse=True)
def setup_session():
    """Session-level fixture that runs automatically"""
    logger.info("Starting test session")
    yield
    logger.info("Test session finished")

@pytest.fixture(scope="function")
def browser(request):
    """Browser fixture with automatic logging and artifact path tracking."""
    # Ensure user_properties exists on the node early
    if not hasattr(request.node, 'user_properties'):
        request.node.user_properties = []

    # Create test-specific log file and get its path
    test_log_handler, log_file_path = add_test_log_handler(request.node)
    # Store log path in the node for later access in makereport
    request.node.user_properties.append(("artifact_log", log_file_path))

    test_name = request.node.function.__name__
    logger.info(f"Starting browser for test: {test_name}")

    # Configure Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    
    # Enable detailed performance logging for network metrics
    chrome_options.set_capability("goog:loggingPrefs", {
        'browser': 'ALL',
        'performance': 'ALL',
        'network': 'ALL'
    })
    
    # Enable CDP performance logging
    chrome_options.add_experimental_option('perfLoggingPrefs', {
        'enableNetwork': True,
        'enablePage': True,
        'traceCategories': 'browser,devtools.timeline,devtools'
    })

    driver = None # Initialize driver to None
    try:
        # Initialize driver
        driver = webdriver.Chrome(options=chrome_options)
        driver.implicitly_wait(5)
        logger.info("Browser started successfully")
        driver.test_name = request.node.name # Keep test name if needed elsewhere

        yield driver # Yield the driver for the test function to use

        # --- Teardown starts here ---
        logger.info(f"Finishing test: {test_name}")

        # Log browser console before closing
        try:
            browser_logs = driver.get_log('browser')
            if browser_logs:
                logger.info("Browser console logs:")
                for log in browser_logs:
                    logger.info(f"  {log}")
        except Exception as e: # Catch specific exception if logs not available
            logger.warning(f"Could not retrieve browser logs: {e}")

        # --- Retrieve Performance Logs BEFORE quitting ---
        performance_logs = []
        browser_timing_metrics = {} # Initialize timing metrics dictionary
        try:
            logger.info("DEBUG [conftest]: Attempting to retrieve performance logs BEFORE quit...")
            performance_logs = driver.get_log('performance')
            logger.info(f"DEBUG [conftest]: Retrieved {len(performance_logs)} performance log entries BEFORE quit.")
            if performance_logs:
                 logger.info("DEBUG [conftest]: First 5 performance log entries BEFORE quit:")
                 for i, log_entry in enumerate(performance_logs[:5]):
                     logger.info(f"DEBUG [conftest]: Entry {i}: {log_entry}")

            # Store raw performance logs in user_properties for performance_monitor
            request.node.user_properties.append(("raw_performance_logs", performance_logs))
            logger.info(f"DEBUG [conftest]: Stored {len(performance_logs)} raw performance logs in user_properties.")

            # --- Retrieve Browser Timing Metrics BEFORE quitting ---
            logger.info("DEBUG [conftest]: Attempting to retrieve browser timing metrics BEFORE quit...")
            browser_timing_metrics = get_browser_performance_metrics(driver)
            request.node.user_properties.append(("browser_timing_metrics", browser_timing_metrics))
            logger.info(f"DEBUG [conftest]: Stored browser timing metrics in user_properties: {browser_timing_metrics}")


        except Exception as e:
            logger.warning(f"Could not retrieve performance logs or timing metrics before quitting browser: {e}")
            # Ensure properties are added even if retrieval failed
            if not any(name == "raw_performance_logs" for name, _ in request.node.user_properties):
                 request.node.user_properties.append(("raw_performance_logs", []))
            if not any(name == "browser_timing_metrics" for name, _ in request.node.user_properties):
                 request.node.user_properties.append(("browser_timing_metrics", {}))


    except Exception as e:
        logger.error(f"Failed to initialize browser: {e}")
        # Ensure properties are added even if browser init fails
        if not any(name == "raw_performance_logs" for name, _ in request.node.user_properties):
             request.node.user_properties.append(("raw_performance_logs", []))
        if not any(name == "browser_timing_metrics" for name, _ in request.node.user_properties):
             request.node.user_properties.append(("browser_timing_metrics", {}))
        raise # Re-raise the exception to mark the test as failed
    finally:
        try:
            if driver: # Check if driver exists before quitting
                driver.quit()
                logger.info("Browser closed successfully")
        except Exception as e:
            logger.error(f"Failed to close browser: {e}")

        # Remove test-specific log handler
        if test_log_handler in logger.handlers:
            logger.removeHandler(test_log_handler)
            test_log_handler.close()

def take_screenshot(driver, base_filename):
    """Take a screenshot and return its absolute path."""
    if not driver:
        logger.error("Cannot take screenshot - driver is None")
        return None

    filename = SCREENSHOT_DIR / f"{base_filename}.png"

    try:
        SCREENSHOT_DIR.mkdir(parents=True, exist_ok=True)
        driver.save_screenshot(str(filename))
        logger.info(f"Screenshot saved: {filename}")
        return str(filename.resolve()) # Return absolute path
    except Exception as e:
        logger.error(f"Failed to take screenshot '{filename}': {e}")
        return None

@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    # --- Artifact Capture (Moved Before Yield) ---
    if call.when == "call": # Check if this is the execution phase
        driver = None
        if "browser" in item.fixturenames:
            try:
                # Access the driver instance that was yielded
                driver = item.funcargs.get("browser")
            except Exception as fixture_exc:
                logger.error(f"Could not retrieve 'browser' fixture for {item.nodeid} before yield: {fixture_exc}")
                driver = None

        if driver:
            failed = call.excinfo is not None
            result_status = "FAIL" if failed else "PASS"
            logger.info(f"Test {result_status.lower()} ({item.nodeid}): Capturing artifacts post-execution, pre-report.")

            if not hasattr(item, 'user_properties'):
                item.user_properties = []

            test_name = item.function.__name__
            class_name = item.cls.__name__ if item.cls else "nogroup"
            base_name_pattern = f"{class_name}_{test_name}"
            if class_name == "nogroup":
                base_name_pattern = test_name

            # --- Take Screenshot ---
            logger.info(f"DEBUG [conftest]: Attempting screenshot for item {item.nodeid}")
            screenshot_base_name = f"{result_status}_{base_name_pattern}" if failed else base_name_pattern
            screenshot_path = take_screenshot(driver, screenshot_base_name)
            if screenshot_path:
                prop = ("artifact_screenshot", screenshot_path)
                if prop not in item.user_properties:
                    item.user_properties.append(prop)
                    logger.info(f"DEBUG [conftest]: Added screenshot property to ITEM: {screenshot_path}")
            else:
                logger.warning(f"DEBUG [conftest]: Screenshot path was None for item {item.nodeid}")

            # --- Save Page Source ---
            logger.info(f"DEBUG [conftest]: Attempting page source for item {item.nodeid}")
            try:
                page_source = driver.page_source
                source_filename = f"page_source_{base_name_pattern}.html"
                source_file = PAGE_SOURCE_DIR / source_filename
                PAGE_SOURCE_DIR.mkdir(parents=True, exist_ok=True)
                with open(source_file, 'w', encoding='utf-8') as f:
                    f.write(page_source)
                source_path = str(source_file.resolve())
                prop = ("artifact_page_source", source_path)
                if prop not in item.user_properties:
                    item.user_properties.append(prop)
                    logger.info(f"DEBUG [conftest]: Added page_source property to ITEM: {source_path}")
            except Exception as e:
                logger.error(f"Could not save page source for item {item.nodeid}: {e}")
                logger.warning(f"DEBUG [conftest]: Page source path generation failed for item {item.nodeid}")

            logger.info(f"DEBUG [conftest]: Final ITEM user_properties before yield for {item.nodeid}: {item.user_properties}")

        else:
            logger.warning(f"DEBUG [conftest]: Driver was None for item {item.nodeid} during 'call' phase (pre-yield). Skipping artifact capture.")

    outcome = yield
    report = outcome.get_result()

    # Ensure report.user_properties is a list and contains properties from item
    if not hasattr(report, 'user_properties'):
        report.user_properties = list(getattr(item, 'user_properties', []))
    elif not isinstance(report.user_properties, list):
        report.user_properties = list(report.user_properties)
    else:
        item_props = getattr(item, 'user_properties', [])
        existing_report_props = {prop[0]: prop[1] for prop in report.user_properties}
        for name, value in item_props:
            if name not in existing_report_props:
                report.user_properties.append((name, value))

    # --- Process and Serialize Performance Metrics ---
    # Iterate through properties, find the performance metrics dictionary,
    # serialize it to JSON, and build a new list of properties.
    updated_user_properties = []
    performance_metrics_dict = None

    for name, value in report.user_properties:
        if name == 'performance_metrics' and isinstance(value, dict):
            # Found the dictionary, store it for serialization and skip adding it now
            performance_metrics_dict = value
            logger.info(f"DEBUG [conftest]: Found performance_metrics dict for {item.nodeid}. Will serialize.")
        elif name == 'performance_metrics' and isinstance(value, str):
             # If it's already a string (e.g., from a previous run or different source), keep it
             updated_user_properties.append((name, value))
             logger.info(f"DEBUG [conftest]: Found existing performance_metrics string for {item.nodeid}. Keeping it.")
        else:
            # Keep all other properties
            updated_user_properties.append((name, value))

    # If the performance metrics dictionary was found, serialize it and add the JSON string
    if performance_metrics_dict is not None:
        try:
            performance_metrics_json = json.dumps(performance_metrics_dict)
            # Add the serialized JSON version under the original name 'performance_metrics'
            updated_user_properties.append(("performance_metrics", performance_metrics_json))
            logger.info(f"DEBUG [conftest]: Serialized performance_metrics dict for {item.nodeid} and added JSON string.")
        except Exception as e:
            logger.error(f"Failed to serialize performance metrics dict for report {item.nodeid}: {e}")
            # If serialization fails, add the original dictionary back so it's not lost
            updated_user_properties.append(("performance_metrics", performance_metrics_dict))


    # Replace the report's user_properties with the updated list
    report.user_properties = updated_user_properties
    logger.info(f"DEBUG [conftest]: Replaced report.user_properties with updated list for {item.nodeid}.")


    # --- Remove raw_performance_logs from report properties ---
    # This step can remain as is, operating on the potentially updated list
    report.user_properties = [(name, value) for name, value in report.user_properties if name != "raw_performance_logs"]
    logger.info(f"DEBUG [conftest]: Removed 'raw_performance_logs' from report properties for {item.nodeid}.")

    test_name = item.function.__name__
    class_name = item.cls.__name__ if item.cls else "nogroup"

    test_handler = next((h for h in logger.handlers if isinstance(h, logging.FileHandler) and Path(h.baseFilename).name == f"{class_name}_{test_name}.log"), None)
    test_logger = logger
    if test_handler:
        test_logger.setLevel(logging.INFO)

    if report.when == "call":
        if report.failed:
            test_logger.error(f"{get_status_symbol('FAILED')} Test Result: FAILED - {item.nodeid}")
        elif report.passed:
            test_logger.info(f"{get_status_symbol('PASSED')} Test Result: PASSED - {item.nodeid}")
        elif report.skipped:
            test_logger.warning(f"{get_status_symbol('SKIPPED')} Test Result: SKIPPED - {item.nodeid}")

        # The performance metrics should now be a JSON string in user_properties
        # We don't need to find the dictionary and serialize it here anymore.
        # The terminal summary hook will handle parsing the JSON string.

    logger.info(f"DEBUG [conftest]: Final REPORT user_properties for {item.nodeid} ({report.when}): {report.user_properties}")

@pytest.fixture
def take_timed_screenshots(browser):
    """
    Fixture to take screenshots at intervals during a test
    Usage: Call the returned function with a name parameter
    """
    def _take_screenshot(name):
        return take_screenshot(browser, name)

    return _take_screenshot

@pytest.fixture
def log_message():
    """Fixture to add custom log messages from tests"""
    def _log_message(message, level="INFO"):
        if (level.upper() == "INFO"):
            logger.info(message)
        elif (level.upper() == "WARNING"):
            logger.warning(message)
        elif (level.upper() == "ERROR"):
            logger.error(message)
        elif (level.upper() == "DEBUG"):
            logger.debug(message)

    return _log_message

@pytest.fixture(autouse=True)
def performance_monitor(request):
    """
    Fixture to monitor test performance including:
    - Test execution time
    - Memory usage
    - CPU usage
    - Network metrics (requests and data transferred)
    - Page load time
    
    This fixture is automatically applied to all tests (autouse=True)
    You don't need to explicitly include this fixture in your test functions.
    """
    # Log that performance monitoring is active
    logger.info(f"Performance monitoring ACTIVATED for test: {request.node.nodeid}")
    
    # Start measuring system metrics
    start_time = time.time()
    start_memory = get_memory_usage()
    
    # Initialize CPU monitoring - reset to get consistent readings
    psutil.cpu_percent(interval=None)  # Initialize CPU monitoring
    
    # Initialize metrics collection
    metrics = {}
    
    # Check if browser fixture is active for this test
    has_browser = "browser" in request.fixturenames
    
    # Create and yield the monitor object
    yield metrics # Test function executes here

    # --- Teardown starts here (after test function completes) ---

    # Calculate basic execution time for the whole test
    duration = time.time() - start_time
    end_memory = get_memory_usage()
    
    # Get CPU usage - this will be the average since we initialized it above
    cpu_used = psutil.cpu_percent(interval=0.1)  # Get CPU usage since initialization

    # Calculate memory difference
    memory_used = end_memory - start_memory

    # Get browser performance metrics if available
    browser_metrics = {} # This will now be populated from user_properties
    network_metrics = {"network_requests": 0, "network_bytes": 0}
    
    if has_browser:
        # Retrieve raw performance logs stored by the browser fixture
        raw_performance_logs = next((value for name, value in request.node.user_properties if name == "raw_performance_logs"), [])
        logger.info(f"DEBUG [conftest]: Performance monitor retrieved {len(raw_performance_logs)} raw performance logs from user_properties.")

        # Parse network metrics from the retrieved logs
        network_metrics = parse_network_metrics_from_logs(raw_performance_logs)
        logger.info(f"DEBUG [conftest]: Performance monitor parsed network metrics: {network_metrics}")

        # Retrieve browser timing metrics stored by the browser fixture
        browser_metrics = next((value for name, value in request.node.user_properties if name == "browser_timing_metrics"), {})
        logger.info(f"DEBUG [conftest]: Performance monitor retrieved browser timing metrics from user_properties: {browser_metrics}")

    # Combine system and network metrics
    metrics.update({
        "execution_time": duration,
        "memory_usage": memory_used,
        "cpu_usage": cpu_used,
        "network_requests": network_metrics.get("network_requests", 0),
        "network_bytes": network_metrics.get("network_bytes", 0)
    })
    
    # Add page load time if available from the retrieved browser_metrics
    if browser_metrics.get('total_page_load'):
        metrics["page_load_time"] = browser_metrics.get('total_page_load')

    # Log all performance metrics
    logger.info(f"Test execution time: {duration:.2f} seconds")
    logger.info(f"Memory usage: {memory_used:.2f} MB")
    logger.info(f"CPU usage: {cpu_used:.2f}%")
    logger.info(f"Network requests: {metrics['network_requests']}")
    logger.info(f"Network data: {metrics['network_bytes']:.2f} KB")
    if metrics.get('page_load_time'): # Log page load time if available
        logger.info(f"Page load time: {metrics['page_load_time']:.2f} ms")
    
    # Store metrics in node properties for later collection (e.g., by makereport)
    request.node.user_properties = [(name, value) for name, value in request.node.user_properties if name != "performance_metrics"]
    request.node.user_properties.append(("performance_metrics", metrics)) # Store as dictionary here

    logger.info(f"Performance metrics stored in node properties for {request.node.nodeid}")

    return metrics

@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_terminal_summary(terminalreporter, exitstatus=None, config=None):
    """
    Create a summary report at the end of the test session with enhanced performance metrics.
    """
    # Acknowledge parameters to avoid linter warnings
    _ = exitstatus, config

    yield

    # Collect basic test statistics
    stats = {
        'total': len(terminalreporter.stats.get('passed', [])) +
                len(terminalreporter.stats.get('failed', [])) +
                len(terminalreporter.stats.get('skipped', [])),
        'passed': len(terminalreporter.stats.get('passed', [])),
        'failed': len(terminalreporter.stats.get('failed', [])),
        'skipped': len(terminalreporter.stats.get('skipped', [])),
        'duration': time.time() - terminalreporter._sessionstarttime
    }

    # Add timestamp to the stats
    stats['timestamp'] = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Simplified performance metrics with network data
    performance_metrics = {
        'avg_execution_time': 0,
        'avg_memory_usage': 0,
        'avg_cpu_usage': 0,
        'avg_network_requests': 0,
        'avg_network_bytes': 0,
        'avg_page_load_time': 0, # Added page load time
        'max_execution_time': 0,
        'max_memory_usage': 0,
        'max_cpu_usage': 0,
        'max_network_requests': 0,
        'max_network_bytes': 0,
        'max_page_load_time': 0, # Added page load time
        'total_network_requests': 0,
        'total_network_bytes': 0
    }

    # Detailed metrics per test
    test_metrics = []

    # Process all reports to extract performance data
    metric_count = 0
    for test_reports in terminalreporter.stats.values():
        for report in test_reports:
            if hasattr(report, 'user_properties'):
                for name, value in report.user_properties:
                    # Look for the JSON string version of performance metrics
                    if name == 'performance_metrics' and isinstance(value, str):
                        try:
                            performance_metrics_dict = json.loads(value)
                            if isinstance(performance_metrics_dict, dict):
                                metric_count += 1

                                # Extract metrics from the parsed dictionary
                                execution_time = performance_metrics_dict.get('execution_time', 0)
                                memory_usage = performance_metrics_dict.get('memory_usage', 0)
                                cpu_usage = performance_metrics_dict.get('cpu_usage', 0)
                                network_requests = performance_metrics_dict.get('network_requests', 0)
                                network_bytes = performance_metrics_dict.get('network_bytes', 0)
                                page_load_time = performance_metrics_dict.get('page_load_time', 0) # Get page load time

                                # Update averages
                                performance_metrics['avg_execution_time'] += execution_time
                                performance_metrics['avg_memory_usage'] += memory_usage
                                performance_metrics['avg_cpu_usage'] += cpu_usage
                                performance_metrics['avg_network_requests'] += network_requests
                                performance_metrics['avg_network_bytes'] += network_bytes
                                performance_metrics['avg_page_load_time'] += page_load_time # Add to average page load time

                                # Update maximums
                                performance_metrics['max_execution_time'] = max(performance_metrics['max_execution_time'], execution_time)
                                performance_metrics['max_memory_usage'] = max(performance_metrics['max_memory_usage'], memory_usage)
                                performance_metrics['max_cpu_usage'] = max(performance_metrics['max_cpu_usage'], cpu_usage)
                                performance_metrics['max_network_requests'] = max(performance_metrics['max_network_requests'], network_requests)
                                performance_metrics['max_network_bytes'] = max(performance_metrics['max_network_bytes'], network_bytes)
                                performance_metrics['max_page_load_time'] = max(performance_metrics['max_page_load_time'], page_load_time) # Track max page load time

                                # Update totals
                                performance_metrics['total_network_requests'] += network_requests
                                performance_metrics['total_network_bytes'] += network_bytes

                                # Store individual test metrics
                                test_metrics.append({
                                    'test_id': report.nodeid,
                                    'execution_time': execution_time,
                                    'memory_usage': memory_usage,
                                    'cpu_usage': cpu_usage,
                                    'network_requests': network_requests,
                                    'network_bytes': network_bytes,
                                    'page_load_time': page_load_time # Store page load time
                                })
                            else:
                                logger.warning(f"Performance metrics value for {report.nodeid} is not a dictionary after JSON parse: {value}")
                        except json.JSONDecodeError:
                            logger.error(f"Failed to parse performance metrics JSON for {report.nodeid}: {value}")
                        except Exception as e:
                            logger.error(f"Error processing performance metrics for {report.nodeid}: {e}")


    # Calculate averages
    if metric_count > 0:
        for key in ['avg_execution_time', 'avg_memory_usage', 'avg_cpu_usage',
                    'avg_network_requests', 'avg_network_bytes', 'avg_page_load_time']: # Include page load time
            performance_metrics[key] /= metric_count

    # Add performance metrics to stats
    stats['performance'] = performance_metrics
    stats['test_metrics'] = test_metrics

    # Log summary information
    logger.info("===== Test Session Summary =====")
    logger.info(f"Total tests: {stats['total']}")
    logger.info(f"Passed: {stats['passed']}")
    logger.info(f"Failed: {stats['failed']}")
    logger.info(f"Skipped: {stats['skipped']}")
    logger.info(f"Duration: {stats['duration']:.2f} seconds")

    # Log performance metrics
    if metric_count > 0:
        logger.info("===== Performance Metrics =====")
        logger.info(f"Average execution time: {performance_metrics['avg_execution_time']:.2f} seconds")
        logger.info(f"Average memory usage: {performance_metrics['avg_memory_usage']:.2f} MB")
        logger.info(f"Average CPU usage: {performance_metrics['avg_cpu_usage']:.2f}%")
        logger.info(f"Maximum execution time: {performance_metrics['max_execution_time']:.2f} seconds")
        logger.info(f"Maximum memory usage: {performance_metrics['max_memory_usage']:.2f} MB")
        logger.info(f"Maximum CPU usage: {performance_metrics['max_cpu_usage']:.2f}%")
        logger.info(f"Total network requests: {performance_metrics['total_network_requests']}")
        logger.info(f"Total network data: {performance_metrics['total_network_bytes']:.2f} KB")
        logger.info(f"Average network requests per test: {performance_metrics['avg_network_requests']:.2f}")
        logger.info(f"Average network data per test: {performance_metrics['avg_network_bytes']:.2f} KB")
        if performance_metrics.get('avg_page_load_time', 0) > 0: # Log average page load time if available
             logger.info(f"Average page load time: {performance_metrics['avg_page_load_time']:.2f} ms")
        if performance_metrics.get('max_page_load_time', 0) > 0: # Log max page load time if available
             logger.info(f"Maximum page load time: {performance_metrics['max_page_load_time']:.2f} ms")

    # Add performance metrics to the XML file
    if metric_count > 0:
        try:
            # Find the XML file
            xml_files = list(Path(os.getcwd()).glob("results_*.xml"))
            if xml_files:
                latest_xml = max(xml_files, key=os.path.getmtime)
                add_performance_metrics_to_xml(latest_xml, test_metrics)
        except Exception as e:
            logger.error(f"Failed to add performance metrics to XML file: {e}")
            logger.error(traceback.format_exc())

    # Save detailed summary to JSON file
    summary_file = LOG_DIR / f"test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(summary_file, 'w') as f:
        json.dump(stats, f, indent=2)
    logger.info(f"Test summary saved to {summary_file}")
