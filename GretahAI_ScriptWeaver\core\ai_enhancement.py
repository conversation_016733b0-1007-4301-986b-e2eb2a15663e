"""
AI-powered comment and feedback enhancement functionality.

This module handles the enhancement of user comments and feedback using AI,
transforming raw user input into detailed, technical, actionable instructions.
"""

import json
import logging
import time
import uuid
from typing import Optional, Dict, Any

# Get logger
logger = logging.getLogger("ScriptWeaver.core.ai_enhancement")


def _build_generation_prompts(
    user_comment: str,
    test_case: dict | None,
    step_table_entry: dict | None,
) -> tuple[str, str]:
    system_prompt = """
You are a senior automation architect (Selenium+PyTest, 10 yrs).
Goal: turn raw user feedback + context into a concise to-do list
for regenerating a test script.

Output contract:
• Format: numbered bullets, ≤ 12, no markdown.
• Each bullet ≤ 25 words, starts with an imperative verb (e.g., Refactor, Add).
• If a bullet relates to the current step, prefix with [STEP-{step_no}].
• Cover locator strategy, waits, data-driven parametrize, robustness,
  naming / folder structure, and PyTest fixtures.
• End with "#DONE".
• Omit apologies, self-references, or a recap of the prompt.
"""
    context_blob = {
        "test_case": test_case or {},
        "step": step_table_entry or {},
        "raw_feedback": user_comment,
    }

    user_prompt = f"""CONTEXT:
{json.dumps(context_blob, indent=2)}

TASK:
Enhance raw_feedback into actionable bullets per the contract above.
Return only the bullets.
"""

    return system_prompt.strip(), user_prompt.strip()


def enhance_user_comment_with_ai(
    user_comment: str,
    validation_results: Dict[str, Any],
    *,
    model_name: str = "gemini-2.0-flash",  # Using Gemini 2.0 Flash model
    api_key: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> str:
    """
    Use an LLM to enhance user's raw comment/feedback into detailed, technical, actionable instructions.

    This function takes a user's raw comment about script optimization issues and enhances it
    with AI to create more detailed, technical, and actionable instructions for script regeneration.

    Args:
        user_comment (str): The user's raw comment/feedback
        validation_results (Dict[str, Any]): Results from script validation
        model_name (str): The AI model to use
        api_key (Optional[str]): API key for Google AI
        context (Optional[Dict[str, Any]]): Additional context information

    Returns:
        str: Enhanced, detailed, and actionable instructions for script improvement

    Raises:
        Exception: If the AI enhancement fails
    """
    # Import here to avoid circular imports
    from .ai import generate_llm_response, log_ai_interaction

    # Generate unique request ID for tracking
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Prepare context for logging
    enhancement_context = {
        'user_comment_length': len(user_comment),
        'has_validation_results': bool(validation_results),
        'validation_status': validation_results.get('validation_status', 'unknown'),
        'model_name': model_name,
        'request_id': request_id
    }

    if context:
        enhancement_context.update(context)

    logger.info(f"Starting AI comment enhancement [Request ID: {request_id}]")
    logger.info(f"User comment length: {len(user_comment)} characters")
    logger.info(f"Validation results available: {bool(validation_results)}")

    try:
        # Prepare validation context for the prompt
        validation_context = ""
        if validation_results:
            validation_status = validation_results.get('validation_status', 'unknown')
            validation_context = f"\n\nValidation Results:\n- Status: {validation_status}"

            if validation_results.get('issues'):
                validation_context += f"\n- Issues Found: {len(validation_results['issues'])}"
                for i, issue in enumerate(validation_results['issues'][:3], 1):  # Show first 3 issues
                    validation_context += f"\n  {i}. {issue}"

            if validation_results.get('validation_error'):
                validation_context += f"\n- Validation Error: {validation_results['validation_error']}"

        # Create the system prompt for comment enhancement
        system_prompt = """You are a senior QA-automation engineer (10+ yrs, PyTest SME).
Goal: turn raw user feedback + validation data into concise, developer-ready
tasks for regenerating a PyTest script.

Output contract:
• Format: numbered bullet list, max 12 points. No markdown, no chatty text.
• Each point ≤ 25 words, starts with an imperative verb (Fix, Add, Refactor…).
• If an item maps to a validation issue, prefix with [ISSUE-{id}].
• Cover: functional fixes, structure, best-practices, and examples if helpful.
• End with "#DONE".
• Double-check that every point is actionable and PyTest-specific."""

        # Create the user prompt with the comment and validation context
        user_prompt = f"""RAW_FEEDBACK:

\"\"\"{user_comment}\"\"\"
VALIDATION_DATA:
{json.dumps(validation_results, indent=2)}

Transform the feedback into actionable tasks following the contract above.
Return only the bullet list."""

        # Log the prompt generation
        log_ai_interaction(
            function_name="enhance_user_comment_with_ai",
            prompt=user_prompt,
            response="<prompt generation>",
            model_name=model_name,
            request_id=request_id,
            context=enhancement_context,
            category="comment_enhancement_prompt",
            is_prompt_generation=True
        )

        # Call the LLM to enhance the comment
        response = generate_llm_response(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model_name=model_name,
            api_key=api_key,
            function_name="enhance_user_comment_with_ai",
            parent_request_id=request_id,
            context=enhancement_context,
            category="comment_enhancement"
        )

        # Clean the response to remove any markdown formatting
        from .ai_helpers import clean_llm_response
        enhanced_comment = clean_llm_response(response, "text")

        # Calculate total time
        end_time = time.time()
        total_time_ms = (end_time - start_time) * 1000

        # Log the successful enhancement
        log_ai_interaction(
            function_name="enhance_user_comment_with_ai",
            prompt=f"User comment length: {len(user_comment)} chars",
            response=f"Enhanced comment length: {len(enhanced_comment)} chars",
            model_name=model_name,
            request_id=request_id,
            context={
                **enhancement_context,
                'original_comment_length': len(user_comment),
                'enhanced_comment_length': len(enhanced_comment),
                'total_processing_time_ms': total_time_ms
            },
            latency_ms=total_time_ms,
            category="comment_enhancement_success"
        )

        logger.info(f"AI comment enhancement completed successfully [Request ID: {request_id}]")
        logger.info(f"Enhanced comment length: {len(enhanced_comment)} characters")
        logger.info(f"Processing time: {total_time_ms:.2f}ms")

        return enhanced_comment

    except Exception as e:
        logger.error(f"Error in enhance_user_comment_with_ai: {e}")
        logger.error(f"Error details: {str(e)}")

        # Log the error
        log_ai_interaction(
            function_name="enhance_user_comment_with_ai",
            prompt=f"User comment length: {len(user_comment)} chars",
            response="",
            model_name=model_name,
            request_id=request_id,
            context=enhancement_context,
            error=str(e),
            category="comment_enhancement_error"
        )

        # Return the original comment as fallback
        logger.warning("Returning original comment due to enhancement error")
        return user_comment


def enhance_generation_comment_with_ai(
    user_comment: str,
    test_case: Dict[str, Any],
    step_table_entry: Optional[Dict[str, Any]] = None,
    *,
    model_name: str = "gemini-2.0-flash",  # Using Gemini 2.0 Flash model
    api_key: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> str:
    """
    Use an LLM to enhance user's raw comment/feedback into detailed, technical, actionable instructions for script generation.

    This function takes a user's raw comment about script generation issues and enhances it
    with AI to create more detailed, technical, and actionable instructions for script regeneration.

    Args:
        user_comment (str): The user's raw comment/feedback about script generation
        test_case (Dict[str, Any]): The test case information for context
        step_table_entry (Optional[Dict[str, Any]]): The step table entry for additional context
        model_name (str): The AI model to use for enhancement (default: "gemini-2.0-flash")
        api_key (Optional[str]): API key for Google AI. If None, use initialized client
        context (Optional[Dict[str, Any]]): Additional context for logging

    Returns:
        str: Enhanced, detailed, technical instructions for script generation

    Raises:
        Exception: If the AI enhancement fails
    """
    # Import here to avoid circular imports
    from .ai import generate_llm_response, log_ai_interaction

    # Generate unique request ID for tracking
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Prepare context for logging
    enhancement_context = {
        'user_comment_length': len(user_comment),
        'has_test_case': bool(test_case),
        'has_step_table_entry': bool(step_table_entry),
        'test_case_id': test_case.get('Test Case ID', 'unknown') if test_case else 'unknown',
        'step_no': step_table_entry.get('step_no', 'unknown') if step_table_entry else 'unknown',
        'model_name': model_name,
        'request_id': request_id
    }

    if context:
        enhancement_context.update(context)

    try:
        logger.info(f"Starting generation comment enhancement [Request ID: {request_id}]")
        logger.info(f"User comment length: {len(user_comment)} characters")

        # Create system prompt for generation comment enhancement
        system_prompt, user_prompt = _build_generation_prompts(user_comment, test_case, step_table_entry)

        # Log the prompt generation
        log_ai_interaction(
            function_name="enhance_generation_comment_with_ai",
            prompt=user_prompt,
            response="<prompt generation>",
            model_name=model_name,
            request_id=request_id,
            context=enhancement_context,
            category="generation_comment_enhancement_prompt",
            is_prompt_generation=True
        )

        # Call the LLM to enhance the comment
        response = generate_llm_response(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model_name=model_name,
            api_key=api_key,
            function_name="enhance_generation_comment_with_ai",
            parent_request_id=request_id,
            context=enhancement_context,
            category="generation_comment_enhancement"
        )

        # Clean the response to remove any markdown formatting
        from .ai_helpers import clean_llm_response
        enhanced_comment = clean_llm_response(response, "text")

        # Calculate total time
        end_time = time.time()
        total_time_ms = (end_time - start_time) * 1000

        # Log the successful enhancement
        log_ai_interaction(
            function_name="enhance_generation_comment_with_ai",
            prompt=f"User comment length: {len(user_comment)} chars",
            response=f"Enhanced comment length: {len(enhanced_comment)} chars",
            model_name=model_name,
            request_id=request_id,
            context={
                **enhancement_context,
                'original_comment_length': len(user_comment),
                'enhanced_comment_length': len(enhanced_comment),
                'total_processing_time_ms': total_time_ms
            },
            latency_ms=total_time_ms,
            category="generation_comment_enhancement_success"
        )

        logger.info(f"Generation comment enhancement completed successfully [Request ID: {request_id}]")
        return enhanced_comment

    except Exception as e:
        logger.error(f"Error in enhance_generation_comment_with_ai: {e}")
        logger.error(f"Error details: {str(e)}")

        # Log the error
        log_ai_interaction(
            function_name="enhance_generation_comment_with_ai",
            prompt=f"User comment length: {len(user_comment)} chars",
            response="",
            model_name=model_name,
            request_id=request_id,
            context=enhancement_context,
            error=str(e),
            category="generation_comment_enhancement_error"
        )

        # Return the original comment as fallback
        logger.warning("Returning original comment due to enhancement error")
        return user_comment
