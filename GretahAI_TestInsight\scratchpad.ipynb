{"cells": [{"cell_type": "code", "execution_count": 1, "id": "52e479f3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to read statistics from: c:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\Autotest\\test_results.db\n", "\n", "--- Database Statistics ---\n", "Total Test Runs Recorded: 3\n", "Total Test Cases Recorded: 15\n", "Unique Test Case Names:    5\n", "\n", "Test Cases by Result:\n", "  - FAILED: 4\n", "  - PASSED: 11\n", "-------------------------\n"]}], "source": ["import sqlite3\n", "import os\n", "from sql_lite_db_helpers import get_thread_local_connection, close_thread_local_connection\n", "\n", "# --- Configuration ---\n", "# Assuming DATABASE_PATH is defined elsewhere or you set it directly\n", "# Example: Get it from your environment setup if available\n", "try:\n", "    import init_env\n", "    DATABASE_PATH = init_env.DATABASE_PATH\n", "except ImportError:\n", "    # Fallback: Define the path directly if init_env is not in the same context\n", "    # Adjust this path as needed\n", "    DATABASE_PATH = os.path.join(os.path.dirname(__file__), '..', 'database', 'test_results.db')\n", "    print(f\"Warning: init_env not found, using default DB path: {DATABASE_PATH}\")\n", "\n", "# --- Main Logic ---\n", "def get_database_statistics(db_path):\n", "    \"\"\"Connects to the database and retrieves overall statistics.\"\"\"\n", "    stats = {\n", "        \"total_runs\": 0,\n", "        \"total_cases\": 0,\n", "        \"cases_by_result\": {},\n", "        \"unique_test_names\": 0,\n", "        \"error\": None\n", "    }\n", "    conn = None\n", "    try:\n", "        if not os.path.exists(db_path):\n", "            stats[\"error\"] = f\"Database file not found at: {db_path}\"\n", "            return stats\n", "\n", "        conn = get_thread_local_connection(db_path)\n", "        cursor = conn.cursor()\n", "\n", "        # 1. Total number of test runs\n", "        cursor.execute(\"SELECT COUNT(*) FROM test_runs\")\n", "        stats[\"total_runs\"] = cursor.fetchone()[0]\n", "\n", "        # 2. Total number of test cases recorded\n", "        cursor.execute(\"SELECT COUNT(*) FROM test_cases\")\n", "        stats[\"total_cases\"] = cursor.fetchone()[0]\n", "\n", "        # 3. Test cases count by result\n", "        cursor.execute(\"SELECT result, COUNT(*) FROM test_cases GROUP BY result\")\n", "        results = cursor.fetchall()\n", "        stats[\"cases_by_result\"] = {row[0]: row[1] for row in results}\n", "\n", "        # 4. Number of unique test cases (based on classname and name)\n", "        cursor.execute(\"SELECT COUNT(DISTINCT classname || '::' || name) FROM test_cases\")\n", "        stats[\"unique_test_names\"] = cursor.fetchone()[0]\n", "\n", "    except sqlite3.Error as e:\n", "        stats[\"error\"] = f\"Database error: {e}\"\n", "    except Exception as e:\n", "        stats[\"error\"] = f\"An unexpected error occurred: {e}\"\n", "    finally:\n", "        if conn:\n", "            close_thread_local_connection() # Use the helper to close the thread-local connection\n", "\n", "    return stats\n", "\n", "# --- Execution ---\n", "if __name__ == \"__main__\":\n", "    print(f\"Attempting to read statistics from: {DATABASE_PATH}\")\n", "    db_stats = get_database_statistics(DATABASE_PATH)\n", "\n", "    if db_stats[\"error\"]:\n", "        print(f\"\\nError retrieving statistics: {db_stats['error']}\")\n", "    else:\n", "        print(\"\\n--- Database Statistics ---\")\n", "        print(f\"Total Test Runs Recorded: {db_stats['total_runs']}\")\n", "        print(f\"Total Test Cases Recorded: {db_stats['total_cases']}\")\n", "        print(f\"Unique Test Case Names:    {db_stats['unique_test_names']}\")\n", "        print(\"\\nTest Cases by Result:\")\n", "        for result, count in db_stats['cases_by_result'].items():\n", "            print(f\"  - {result or 'UNKNOWN'}: {count}\")\n", "        print(\"-------------------------\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "14c2e40b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'sqlite_sequence': [(0, 'name', '', 0, None, 0), (1, 'seq', '', 0, None, 0)], 'test_runs': [(0, 'id', 'INTEGER', 0, None, 1), (1, 'timestamp', 'TEXT', 1, None, 0), (2, 'total_tests', 'INTEGER', 0, None, 0), (3, 'passed_tests', 'INTEGER', 0, None, 0), (4, 'failed_tests', 'INTEGER', 0, None, 0), (5, 'skipped_tests', 'INTEGER', 0, None, 0), (6, 'duration', 'REAL', 0, None, 0)], 'test_suites': [(0, 'id', 'INTEGER', 0, None, 1), (1, 'run_id', 'INTEGER', 1, None, 0), (2, 'name', 'TEXT', 1, None, 0), (3, 'total_tests', 'INTEGER', 0, None, 0), (4, 'failed_tests', 'INTEGER', 0, None, 0), (5, 'skipped_tests', 'INTEGER', 0, None, 0), (6, 'duration', 'REAL', 0, None, 0)], 'test_cases': [(0, 'id', 'INTEGER', 0, None, 1), (1, 'suite_id', 'INTEGER', 1, None, 0), (2, 'name', 'TEXT', 1, None, 0), (3, 'classname', 'TEXT', 1, None, 0), (4, 'duration', 'REAL', 0, None, 0), (5, 'result', 'TEXT', 1, None, 0), (6, 'failure_message', 'TEXT', 0, None, 0)], 'artifacts': [(0, 'id', 'INTEGER', 0, None, 1), (1, 'case_id', 'INTEGER', 1, None, 0), (2, 'type', 'TEXT', 1, None, 0), (3, 'path', 'TEXT', 1, None, 0), (4, 'timestamp', 'TEXT', 0, 'CURRENT_TIMESTAMP', 0)], 'ai_summaries': [(0, 'id', 'INTEGER', 0, None, 1), (1, 'case_id', 'INTEGER', 0, None, 0), (2, 'run_id', 'INTEGER', 0, None, 0), (3, 'model_name', 'TEXT', 0, None, 0), (4, 'summary_type', 'TEXT', 1, None, 0), (5, 'summary_content', 'TEXT', 0, None, 0), (6, 'timestamp', 'TEXT', 0, 'CURRENT_TIMESTAMP', 0)], 'app_config': [(0, 'key', 'TEXT', 0, None, 1), (1, 'value', 'TEXT', 0, None, 0), (2, 'updated_at', 'TEXT', 0, 'CURRENT_TIMESTAMP', 0)]}\n"]}], "source": ["def get_database_schema(db_path):\n", "    \"\"\"Retrieves the complete database schema.\"\"\"\n", "    schema = {}\n", "    conn = None\n", "    try:\n", "        if not os.path.exists(db_path):\n", "            return f\"Database file not found at: {db_path}\"\n", "\n", "        conn = sqlite3.connect(db_path)\n", "        cursor = conn.cursor()\n", "\n", "        # Get all table names\n", "        cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table';\")\n", "        tables = cursor.fetchall()\n", "\n", "        for table in tables:\n", "            table_name = table[0]\n", "            cursor.execute(f\"PRAGMA table_info({table_name});\")\n", "            schema[table_name] = cursor.fetchall()\n", "\n", "    except sqlite3.Error as e:\n", "        return f\"Database error: {e}\"\n", "    except Exception as e:\n", "        return f\"An unexpected error occurred: {e}\"\n", "    finally:\n", "        if conn:\n", "            conn.close()\n", "\n", "    return schema\n", "\n", "# Retrieve and print the schema\n", "schema = get_database_schema(DATABASE_PATH)\n", "print(schema)"]}, {"cell_type": "code", "execution_count": 3, "id": "d5663e36", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DATABASE_PATH: c:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\Autotest\\test_results.db\n", "count: 11\n", "\n", "db_stats:\n", "{'cases_by_result': {'FAILED': 4, 'PASSED': 11},\n", " 'error': None,\n", " 'total_cases': 15,\n", " 'total_runs': 3,\n", " 'unique_test_names': 5}\n", "\n", "result: PASSED\n", "\n", "schema:\n", "{'ai_summaries': [(0, 'id', 'INTEGER', 0, None, 1),\n", "                  (1, 'case_id', 'INTEGER', 0, None, 0),\n", "                  (2, 'run_id', 'INTEGER', 0, None, 0),\n", "                  (3, 'model_name', 'TEXT', 0, None, 0),\n", "                  (4, 'summary_type', 'TEXT', 1, None, 0),\n", "                  (5, 'summary_content', 'TEXT', 0, None, 0),\n", "                  (6, 'timestamp', 'TEXT', 0, 'CURRENT_TIMESTAMP', 0)],\n", " 'app_config': [(0, 'key', 'TEXT', 0, None, 1),\n", "                (1, 'value', 'TEXT', 0, None, 0),\n", "                (2, 'updated_at', 'TEXT', 0, 'CURRENT_TIMESTAMP', 0)],\n", " 'artifacts': [(0, 'id', 'INTEGER', 0, None, 1),\n", "               (1, 'case_id', 'INTEGER', 1, None, 0),\n", "               (2, 'type', 'TEXT', 1, None, 0),\n", "               (3, 'path', 'TEXT', 1, None, 0),\n", "               (4, 'timestamp', 'TEXT', 0, 'CURRENT_TIMESTAMP', 0)],\n", " 'sqlite_sequence': [(0, 'name', '', 0, None, 0), (1, 'seq', '', 0, None, 0)],\n", " 'test_cases': [(0, 'id', 'INTEGER', 0, None, 1),\n", "                (1, 'suite_id', 'INTEGER', 1, None, 0),\n", "                (2, 'name', 'TEXT', 1, None, 0),\n", "                (3, 'classname', 'TEXT', 1, None, 0),\n", "                (4, 'duration', 'REAL', 0, None, 0),\n", "                (5, 'result', 'TEXT', 1, None, 0),\n", "                (6, 'failure_message', 'TEXT', 0, None, 0)],\n", " 'test_runs': [(0, 'id', 'INTEGER', 0, None, 1),\n", "               (1, 'timestamp', 'TEXT', 1, None, 0),\n", "               (2, 'total_tests', 'INTEGER', 0, None, 0),\n", "               (3, 'passed_tests', 'INTEGER', 0, None, 0),\n", "               (4, 'failed_tests', 'INTEGER', 0, None, 0),\n", "               (5, 'skipped_tests', 'INTEGER', 0, None, 0),\n", "               (6, 'duration', 'REAL', 0, None, 0)],\n", " 'test_suites': [(0, 'id', 'INTEGER', 0, None, 1),\n", "                 (1, 'run_id', 'INTEGER', 1, None, 0),\n", "                 (2, 'name', 'TEXT', 1, None, 0),\n", "                 (3, 'total_tests', 'INTEGER', 0, None, 0),\n", "                 (4, 'failed_tests', 'INTEGER', 0, None, 0),\n", "                 (5, 'skipped_tests', 'INTEGER', 0, None, 0),\n", "                 (6, 'duration', 'REAL', 0, None, 0)]}\n"]}], "source": ["from pprint import pprint\n", "\n", "# Print DATABASE_PATH\n", "print(f\"DATABASE_PATH: {DATABASE_PATH}\")\n", "\n", "# Print count\n", "print(f\"count: {count}\")\n", "\n", "# Print db_stats\n", "print(\"\\ndb_stats:\")\n", "pprint(db_stats)\n", "\n", "# Print result\n", "print(f\"\\nresult: {result}\")\n", "\n", "# Print schema\n", "print(\"\\nschema:\")\n", "pprint(schema)"]}, {"cell_type": "code", "execution_count": 7, "id": "6dc629bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using Database: c:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\Autotest\\test_results.db\n", "\n", "--- AI Summary Length Statistics (visual_analysis) ---\n", "{'75th_percentile_length': 1224,\n", " '90th_percentile_length': 1367,\n", " '95th_percentile_length': 1380,\n", " 'average_length': 647.8,\n", " 'error': None,\n", " 'max_length': 1394,\n", " 'median_length (50th)': 559,\n", " 'min_length': 54,\n", " 'summaries_analyzed': 10,\n", " 'total_summaries_found': 10}\n", "----------------------------------------------------\n"]}], "source": ["# ... existing cells ...\n", "# Add this as a new cell:\n", "import sqlite3\n", "import os\n", "import json\n", "import numpy as np # Using numpy for easier percentile calculation\n", "from pprint import pprint\n", "\n", "# --- Configuration ---\n", "# Use the DATABASE_PATH defined in previous cells\n", "print(f\"Using Database: {DATABASE_PATH}\\n\")\n", "\n", "# --- Function to get AI Summary Length Stats ---\n", "def get_ai_summary_length_stats(db_path):\n", "    \"\"\"Fetches visual_analysis summaries and calculates length statistics.\"\"\"\n", "    lengths = []\n", "    stats = {\n", "        \"total_summaries_found\": 0,\n", "        \"summaries_analyzed\": 0,\n", "        \"min_length\": None,\n", "        \"max_length\": None,\n", "        \"average_length\": None,\n", "        \"median_length (50th)\": None,\n", "        \"75th_percentile_length\": None,\n", "        \"90th_percentile_length\": None,\n", "        \"95th_percentile_length\": None,\n", "        \"error\": None\n", "    }\n", "    conn = None\n", "    try:\n", "        if not os.path.exists(db_path):\n", "            stats[\"error\"] = f\"Database file not found at: {db_path}\"\n", "            return stats\n", "\n", "        # Using a direct connection for simplicity in this analysis script\n", "        conn = sqlite3.connect(db_path)\n", "        cursor = conn.cursor()\n", "\n", "        # Query to get the content of visual_analysis summaries\n", "        # Adjust table/column names if your schema differs (e.g., artifacts table)\n", "        cursor.execute(\"\"\"\n", "            SELECT summary_content\n", "            FROM ai_summaries\n", "            WHERE summary_type = 'visual_analysis' AND summary_content IS NOT NULL AND summary_content != ''\n", "        \"\"\")\n", "        # --- Alternative query if summaries are in artifacts table ---\n", "        # cursor.execute(\"\"\"\n", "        #    SELECT content\n", "        #    FROM artifacts\n", "        #    WHERE artifact_type = 'ai_summary'\n", "        #      AND name = 'visual_analysis'\n", "        #      AND content IS NOT NULL AND content != ''\n", "        # \"\"\")\n", "        # --- End Alternative ---\n", "\n", "        rows = cursor.fetchall()\n", "        stats[\"total_summaries_found\"] = len(rows)\n", "\n", "        for row in rows:\n", "            content = row[0]\n", "            # Calculate length of the raw content string stored in the DB\n", "            lengths.append(len(content))\n", "\n", "        if lengths:\n", "            stats[\"summaries_analyzed\"] = len(lengths)\n", "            stats[\"min_length\"] = int(np.min(lengths))\n", "            stats[\"max_length\"] = int(np.max(lengths))\n", "            stats[\"average_length\"] = round(np.mean(lengths), 2)\n", "            stats[\"median_length (50th)\"] = int(np.percentile(lengths, 50))\n", "            stats[\"75th_percentile_length\"] = int(np.percentile(lengths, 75))\n", "            stats[\"90th_percentile_length\"] = int(np.percentile(lengths, 90))\n", "            stats[\"95th_percentile_length\"] = int(np.percentile(lengths, 95))\n", "        else:\n", "            stats[\"error\"] = \"No valid 'visual_analysis' summaries found to analyze.\"\n", "\n", "\n", "    except sqlite3.Error as e:\n", "        stats[\"error\"] = f\"Database error: {e}\"\n", "    except ImportError:\n", "        stats[\"error\"] = \"Numpy is required for percentile calculations. Please install it (`pip install numpy`).\"\n", "        # Fallback without percentiles if n<PERSON><PERSON> is not installed\n", "        if lengths:\n", "             stats[\"summaries_analyzed\"] = len(lengths)\n", "             stats[\"min_length\"] = min(lengths)\n", "             stats[\"max_length\"] = max(lengths)\n", "             stats[\"average_length\"] = round(sum(lengths) / len(lengths), 2)\n", "\n", "    except Exception as e:\n", "        stats[\"error\"] = f\"An unexpected error occurred: {e}\"\n", "    finally:\n", "        if conn:\n", "            conn.close()\n", "\n", "    return stats\n", "\n", "# --- Execution ---\n", "ai_summary_stats = get_ai_summary_length_stats(DATABASE_PATH)\n", "\n", "print(\"--- AI Summary Length Statistics (visual_analysis) ---\")\n", "if ai_summary_stats[\"error\"] and not ai_summary_stats[\"summaries_analyzed\"] > 0 :\n", "     print(f\"Error: {ai_summary_stats['error']}\")\n", "else:\n", "    # Use pprint for cleaner dictionary output\n", "    pprint(ai_summary_stats)\n", "    if ai_summary_stats[\"error\"]: # Print non-fatal errors (like numpy missing)\n", "        print(f\"\\nNote: {ai_summary_stats['error']}\")\n", "print(\"----------------------------------------------------\")"]}, {"cell_type": "code", "execution_count": 8, "id": "3b2b16a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using Database: c:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\Autotest\\test_results.db\n", "\n", "--- Failure Message Length Statistics (Failed Tests) ---\n", "{'75th_percentile_length': 1966,\n", " '90th_percentile_length': 1966,\n", " '95th_percentile_length': 1966,\n", " 'average_length': 1677.0,\n", " 'error': None,\n", " 'max_length': 1966,\n", " 'median_length (50th)': 1681,\n", " 'messages_analyzed': 4,\n", " 'min_length': 1379,\n", " 'total_failed_tests_with_msg': 4}\n", "--------------------------------------------------------\n"]}], "source": ["import sqlite3\n", "import os\n", "import numpy as np  # Using numpy for easier percentile calculation\n", "from pprint import pprint\n", "\n", "# --- Configuration ---\n", "# Use the DATABASE_PATH defined in previous cells\n", "print(f\"Using Database: {DATABASE_PATH}\\n\")\n", "\n", "# --- Function to get Failure Message Length Stats ---\n", "def get_failure_message_length_stats(db_path):\n", "    \"\"\"Fetches failure messages for failed tests and calculates length statistics.\"\"\"\n", "    lengths = []\n", "    stats = {\n", "        \"total_failed_tests_with_msg\": 0,\n", "        \"messages_analyzed\": 0,\n", "        \"min_length\": None,\n", "        \"max_length\": None,\n", "        \"average_length\": None,\n", "        \"median_length (50th)\": None,\n", "        \"75th_percentile_length\": None,\n", "        \"90th_percentile_length\": None,\n", "        \"95th_percentile_length\": None,\n", "        \"error\": None\n", "    }\n", "    conn = None\n", "    try:\n", "        if not os.path.exists(db_path):\n", "            stats[\"error\"] = f\"Database file not found at: {db_path}\"\n", "            return stats\n", "\n", "        # Using a direct connection for simplicity in this analysis script\n", "        conn = sqlite3.connect(db_path)\n", "        cursor = conn.cursor()\n", "\n", "        # Query to get the failure_message for failed tests\n", "        cursor.execute(\"\"\"\n", "            SELECT failure_message\n", "            FROM test_cases\n", "            WHERE result = 'FAILED' AND failure_message IS NOT NULL AND failure_message != ''\n", "        \"\"\")\n", "\n", "        rows = cursor.fetchall()\n", "        stats[\"total_failed_tests_with_msg\"] = len(rows)\n", "\n", "        for row in rows:\n", "            content = row[0]\n", "            # Calculate length of the raw failure message string\n", "            lengths.append(len(content))\n", "\n", "        if lengths:\n", "            stats[\"messages_analyzed\"] = len(lengths)\n", "            stats[\"min_length\"] = int(np.min(lengths))\n", "            stats[\"max_length\"] = int(np.max(lengths))\n", "            stats[\"average_length\"] = round(np.mean(lengths), 2)\n", "            stats[\"median_length (50th)\"] = int(np.percentile(lengths, 50))\n", "            stats[\"75th_percentile_length\"] = int(np.percentile(lengths, 75))\n", "            stats[\"90th_percentile_length\"] = int(np.percentile(lengths, 90))\n", "            stats[\"95th_percentile_length\"] = int(np.percentile(lengths, 95))\n", "        else:\n", "            stats[\"error\"] = \"No failed tests with failure messages found to analyze.\"\n", "\n", "    except sqlite3.Error as e:\n", "        stats[\"error\"] = f\"Database error: {e}\"\n", "    except ImportError:\n", "        stats[\"error\"] = \"Numpy is required for percentile calculations. Please install it (`pip install numpy`).\"\n", "        # Fallback without percentiles if n<PERSON><PERSON> is not installed\n", "        if lengths:\n", "            stats[\"messages_analyzed\"] = len(lengths)\n", "            stats[\"min_length\"] = min(lengths)\n", "            stats[\"max_length\"] = max(lengths)\n", "            stats[\"average_length\"] = round(sum(lengths) / len(lengths), 2)\n", "\n", "    except Exception as e:\n", "        stats[\"error\"] = f\"An unexpected error occurred: {e}\"\n", "    finally:\n", "        if conn:\n", "            conn.close()\n", "\n", "    return stats\n", "\n", "# --- Execution ---\n", "failure_msg_stats = get_failure_message_length_stats(DATABASE_PATH)\n", "\n", "print(\"--- Failure Message Length Statistics (Failed Tests) ---\")\n", "if failure_msg_stats[\"error\"] and not failure_msg_stats[\"messages_analyzed\"] > 0:\n", "    print(f\"Error: {failure_msg_stats['error']}\")\n", "else:\n", "    # Use pprint for cleaner dictionary output\n", "    pprint(failure_msg_stats)\n", "    if failure_msg_stats[\"error\"]:  # Print non-fatal errors (like numpy missing)\n", "        print(f\"\\nNote: {failure_msg_stats['error']}\")\n", "print(\"--------------------------------------------------------\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}