/*
 * ==========================================================================
 * STYLE SHEET: style.css for GretahAI ScriptWeaver
 * ==========================================================================
 *
 * Description:
 * This CSS file defines the visual styling for the GretahAI ScriptWeaver application.
 * It includes definitions for color themes, typography, layout elements,
 * animations, and specific component styles.
 *
 * Structure:
 * 1.  `:root`        - Defines global CSS variables for colors.
 * 2.  Light Mode     - Styles applied when the 'light-mode' class is active.
 * 3.  Dark Mode      - Styles applied when the 'dark-mode' class is active.
 * 4.  Headers        - Styles for main and sub-headers, including animations.
 * 5.  Panels         - Styles for different types of informational panels.
 * 6.  Cards          - Styles for professional card layout.
 * 7.  Buttons        - Base styles and hover effects for Streamlit buttons.
 * 8.  Sidebar        - Styles and animations for the Streamlit sidebar.
 * 9.  Status         - Styles for visual status indicators.
 * 10. Action Buttons - Basic styling for action buttons.
 * 11. Logo           - Styles for the logo container and text.
 * 12. Footer         - Styles for the application footer.
 * 13. Animations     - Keyframe definitions for animations.
 *
 * ==========================================================================
 */

:root {
    /* ScriptWeaver-specific color palette */
    --primary-color: #673AB7; /* Deep Purple */
    --secondary-color: #8E24AA; /* Purple */
    --accent-color: #FFC107; /* Amber */
    --background-color-light: #F5F0FF; /* Very Light Purple */
    --background-color-dark: #1E1A2E; /* Dark Purple-Gray */
    --text-color-light: #37474F; /* Dark Blue-Gray */
    --text-color-dark: #F5F5F5; /* White Smoke */
    --success-color: #4CAF50; /* Green */
    --warning-color: #FF9800; /* Orange */
    --error-color: #F44336; /* Red */
    --info-color: #2196F3; /* Blue */
}

/* Light Mode Styles */
.light-mode {
    background-color: var(--background-color-light);
    color: var(--text-color-light);
}

.light-mode .stButton > button {
    background-color: var(--primary-color);
    color: white;
}

/* Dark Mode Styles */
.dark-mode {
    background-color: var(--background-color-dark);
    color: var(--text-color-dark);
}

.dark-mode .stButton > button {
    background-color: var(--secondary-color);
    color: var(--text-color-dark);
}

/* Header Animations - ScriptWeaver specific */
.main-header {
    font-size: 2.5rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1rem;
    animation: fadeIn 1s ease-in-out;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    background: linear-gradient(120deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sub-header {
    font-size: 1.8rem;
    color: var(--secondary-color);
    margin-top: 2rem;
    margin-bottom: 1rem;
    animation: fadeIn 1s ease-in-out;
    font-weight: 600;
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 5px;
    display: inline-block;
}

/* Professional panels - ScriptWeaver specific */
.feature-panel {
    background-color: rgba(103, 58, 183, 0.05);
    border-left: 3px solid var(--primary-color);
    padding: 15px;
    margin: 15px 0;
    font-size: 0.95rem;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.success-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(76, 175, 80, 0.1);
    border-left: 5px solid var(--success-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.info-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(33, 150, 243, 0.1);
    border-left: 5px solid var(--info-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.warning-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(255, 152, 0, 0.1);
    border-left: 5px solid var(--warning-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.error-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(244, 67, 54, 0.1);
    border-left: 5px solid var(--error-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

/* Professional card layout */
.pro-card {
    border: 1px solid rgba(103, 58, 183, 0.2);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 3px 10px rgba(103, 58, 183, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pro-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(103, 58, 183, 0.15);
}

/* Button Styling */
.stButton > button {
    width: 100%;
    border-radius: 8px;
    font-weight: 600;
    padding: 0.6rem 1.2rem;
    border: none;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stButton > button:hover {
    background-color: var(--accent-color);
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stDownloadButton > button {
    width: 100%;
    border-radius: 8px;
    background-color: var(--success-color);
    color: white;
    font-weight: 600;
    padding: 0.6rem 1.2rem;
    border: none;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stDownloadButton > button:hover {
    background-color: #43A047;
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Sidebar Animation and Styling */
.stSidebar {
    animation: slideIn 0.5s ease-in-out;
    background-color: #f0e6ff;
    border-right: 1px solid rgba(103, 58, 183, 0.2);
}

.dark-mode .stSidebar {
    background-color: #1a172b;
    border-right: 1px solid rgba(142, 36, 170, 0.2);
}

/* Status indicators */
.status-passed {
    color: var(--success-color);
    font-weight: bold;
}

.status-failed {
    color: var(--error-color);
    font-weight: bold;
}

.status-blocked {
    color: var(--warning-color);
    font-weight: bold;
}

/* Form elements */
.stTextArea textarea {
    border-radius: 8px;
    border: 1px solid rgba(103, 58, 183, 0.3);
    transition: border 0.3s ease, box-shadow 0.3s ease;
}

.stTextArea textarea:focus {
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 0 2px rgba(103, 58, 183, 0.2);
}

div[data-testid="stDataFrame"] {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
}

/* Footer style - ScriptWeaver specific */
.scriptweaver-footer {
    text-align: center;
    padding: 20px;
    margin-top: 40px;
    font-size: 0.9rem;
    background: linear-gradient(to right, rgba(103, 58, 183, 0.05), rgba(142, 36, 170, 0.05));
    border-top: 2px solid rgba(103, 58, 183, 0.2);
    border-bottom: 1px solid rgba(103, 58, 183, 0.1);
    color: var(--primary-color);
    font-weight: 500;
    line-height: 1.4;
}

.scriptweaver-footer a {
    text-decoration: none;
    font-weight: 600;
}

.scriptweaver-footer a:hover {
    text-decoration: underline;
    opacity: 0.8;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
