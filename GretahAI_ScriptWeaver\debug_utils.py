"""
Debug utilities for GretahAI ScriptWeaver.

This module provides debug utility functions that can be used across the application.
"""

import os
import logging
import streamlit as st

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.debug_utils")

# Debug mode flag - can be set via environment variable
DEBUG_MODE = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")

def debug(message):
    """
    Print a debug message to the console and optionally display it in the Streamlit UI.
    
    This function replaces st.debug() calls with a more flexible approach that works
    with all Streamlit versions.
    
    Args:
        message (str): The debug message to display
    """
    # Always log to console
    logger.debug(message)
    
    # Also print to console for immediate visibility during development
    if DEBUG_MODE:
        print(f"DEBUG: {message}")
        
        # Optionally show in UI when in development mode
        try:
            # Check if we're in a Streamlit context with an active session state
            if st._is_running:
                # Create a debug section in the sidebar if it doesn't exist
                if 'show_debug' not in st.session_state:
                    st.session_state.show_debug = False
                    st.session_state.debug_messages = []
                
                # Store the message
                st.session_state.debug_messages.append(message)
        except:
            # If any error occurs, just continue without UI display
            pass
