#!/usr/bin/env python3

import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import io
import Test_case_db_helper as db  # Import database helper
from helpers import create_formatted_excel_from_scenarios

def render_reporting(current_user):
    """
    Render the reporting tab for test analysis.

    Args:
        current_user (str): The current user's username
    """
    st.subheader("Test Case Reporting")
    st.info("Generate and download reports for your test cases.")

    # Create tabs for different report types
    report_tabs = st.tabs(["JIRA Reports", "Test Run Reports", "Custom Reports"])

    # Tab 1: JIRA Reports
    with report_tabs[0]:
        st.markdown("### JIRA Test Case Reports")

        # Get unique JIRA IDs from the database
        jira_ids = db.get_unique_jira_ids(db.DATABASE_PATH, user_name=current_user)

        if jira_ids:
            # Create a selectbox for JIRA IDs
            selected_jira_id = st.selectbox(
                "Select JIRA ID",
                options=jira_ids,
                key="report_jira_id_select"
            )

            # Add a test type filter
            test_type_options = ["All Types", "positive", "negative", "security", "performance"]
            selected_test_type = st.selectbox(
                "Select Test Type",
                options=test_type_options,
                key="report_test_type_select"
            )

            # Generate report button
            if st.button("Generate Report", key="report_jira_generate", use_container_width=True):
                with st.spinner("Generating report..."):
                    # Get test cases for the selected JIRA ID
                    if selected_test_type == "All Types":
                        # Get all test types for this JIRA ID
                        test_cases_data = db.get_test_cases_by_filters(
                            db.DATABASE_PATH,
                            jira_id=selected_jira_id,
                            user_name=current_user
                        )
                    else:
                        # Get specific test type for this JIRA ID
                        test_cases_data = db.get_test_cases_by_filters(
                            db.DATABASE_PATH,
                            jira_id=selected_jira_id,
                            test_type=selected_test_type,
                            user_name=current_user
                        )

                    if not test_cases_data.empty:
                        # Create Excel file
                        excel_buffer = io.BytesIO()
                        create_formatted_excel_from_scenarios(test_cases_data, excel_buffer)
                        excel_buffer.seek(0)

                        # Create download button
                        st.download_button(
                            label="Download Excel Report",
                            data=excel_buffer,
                            file_name=f"{selected_jira_id}_{selected_test_type}_report.xlsx",
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            key="report_jira_download"
                        )

                        # Display preview
                        st.markdown("### Report Preview")
                        st.dataframe(test_cases_data, use_container_width=True)
                    else:
                        st.error(f"No test cases found for JIRA ID: {selected_jira_id}")
        else:
            st.warning("No JIRA IDs found in the database. Generate some test cases first.")

    # Tab 2: Test Run Reports
    with report_tabs[1]:
        st.markdown("### Test Run Reports")

        # Get all test runs for the current user
        try:
            test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)
            if test_runs is None:
                test_runs = []
        except Exception as e:
            st.error(f"Error retrieving test runs: {str(e)}")
            test_runs = []

        if test_runs:
            # Create a dataframe for better display
            test_runs_df = pd.DataFrame(test_runs)

            # Add a timestamp column if it exists
            if 'timestamp' in test_runs_df.columns:
                test_runs_df['timestamp'] = pd.to_datetime(test_runs_df['timestamp'])
                test_runs_df = test_runs_df.sort_values('timestamp', ascending=False)

            # Format the dataframe for display
            display_runs = test_runs_df[['id', 'jira_id', 'test_type', 'timestamp', 'status', 'notes']].copy()
            display_runs.columns = ['Run ID', 'JIRA ID', 'Test Type', 'Timestamp', 'Status', 'Notes']

            # Display the test runs
            st.dataframe(display_runs, use_container_width=True)

            # Create a selectbox for test runs
            run_options = [f"Run {run['id']}: {run['jira_id']} ({run['test_type']})" for run in test_runs]
            selected_run_option = st.selectbox(
                "Select Test Run",
                options=run_options,
                key="report_test_run_select"
            )

            # Extract the run ID from the selected option
            if selected_run_option:
                selected_run_id = int(selected_run_option.split(":")[0].replace("Run ", "").strip())

            # Generate report button
            if st.button("Generate Report", key="report_run_generate", use_container_width=True):
                with st.spinner("Generating report..."):
                    # Get test cases for the selected test run
                    test_cases_data = db.get_test_cases_by_test_run(db.DATABASE_PATH, selected_run_id)

                    if isinstance(test_cases_data, list) and len(test_cases_data) > 0:
                        test_cases_df = pd.DataFrame(test_cases_data)

                        # Create Excel file
                        excel_buffer = io.BytesIO()
                        create_formatted_excel_from_scenarios(test_cases_df, excel_buffer)
                        excel_buffer.seek(0)

                        # Create download button
                        st.download_button(
                            label="Download Excel Report",
                            data=excel_buffer,
                            file_name=f"TestRun_{selected_run_id}_report.xlsx",
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            key="report_run_download"
                        )

                        # Display preview
                        st.markdown("### Report Preview")
                        st.dataframe(test_cases_df, use_container_width=True)
                    elif hasattr(test_cases_data, 'empty') and not test_cases_data.empty:
                        # Create Excel file
                        excel_buffer = io.BytesIO()
                        create_formatted_excel_from_scenarios(test_cases_data, excel_buffer)
                        excel_buffer.seek(0)

                        # Create download button
                        st.download_button(
                            label="Download Excel Report",
                            data=excel_buffer,
                            file_name=f"TestRun_{selected_run_id}_report.xlsx",
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            key="report_run_download"
                        )

                        # Display preview
                        st.markdown("### Report Preview")
                        st.dataframe(test_cases_data, use_container_width=True)
                    else:
                        st.error(f"No test cases found for Test Run ID: {selected_run_id}")
        else:
            st.warning("No test runs found in the database. Generate some test cases first.")

    # Tab 3: Custom Reports
    with report_tabs[2]:
        st.markdown("### Custom Test Case Reports")

        # Add date range filter
        default_start_date = datetime.now() - timedelta(days=90)
        default_end_date = datetime.now()
        date_range = st.date_input(
            "Date Range",
            value=(default_start_date, default_end_date),
            key="report_custom_date_range"
        )

        # Add JIRA ID filter (optional)
        jira_id_filter = st.text_input("JIRA ID (optional)", key="report_custom_jira_id")

        # Add test type filter
        test_type_options = ["All Types", "positive", "negative", "security", "performance"]
        selected_test_type = st.selectbox(
            "Select Test Type",
            options=test_type_options,
            key="report_custom_test_type_select"
        )

        # Generate report button
        if st.button("Generate Custom Report", key="report_custom_generate", use_container_width=True):
            with st.spinner("Generating custom report..."):
                # Convert date range to string format
                start_date = date_range[0].strftime("%Y-%m-%d") if isinstance(date_range, tuple) and len(date_range) > 0 else None
                end_date = date_range[1].strftime("%Y-%m-%d") if isinstance(date_range, tuple) and len(date_range) > 1 else None

                # Prepare filters
                filters = {
                    "start_date": start_date,
                    "end_date": end_date,
                    "user_name": current_user
                }

                if jira_id_filter:
                    filters["jira_id"] = jira_id_filter

                if selected_test_type != "All Types":
                    filters["test_type"] = selected_test_type

                # Get test cases with the specified filters
                test_cases_data = db.get_test_cases_by_filters(db.DATABASE_PATH, **filters)

                if not test_cases_data.empty:
                    # Create Excel file
                    excel_buffer = io.BytesIO()
                    create_formatted_excel_from_scenarios(test_cases_data, excel_buffer)
                    excel_buffer.seek(0)

                    # Create download button
                    report_name = f"Custom_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                    st.download_button(
                        label="Download Excel Report",
                        data=excel_buffer,
                        file_name=report_name,
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        key="report_custom_download"
                    )

                    # Display preview
                    st.markdown("### Report Preview")
                    st.dataframe(test_cases_data, use_container_width=True)
                else:
                    st.error("No test cases found for the selected filters.")
