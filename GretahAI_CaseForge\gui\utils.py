"""
Utility functions for the GUI module.
"""

import streamlit as st
import pandas as pd
import json
import os
from datetime import datetime
from pathlib import Path
from jira import JIRA

# Import helper functions from helpers.py
from GretahAI_CaseForge.helpers import (
    run_ollama_with_chat,
    run_google_ai_studio,
    is_ollama_running,
    generate_gemini_test_gen_prompt,
    parse_test_scenarios_json,
    create_formatted_excel_from_scenarios,
    get_latest_test_case_file,
    extract_test_info_from_issue
)

# Import database helper
import GretahAI_CaseForge.Test_case_db_helper as db  # Import the database helper

@st.cache_resource
def load_config(config_path=None):
    """
    Load configuration from a JSON file

    Args:
        config_path (str, optional): Path to the config file. If None, uses default path.

    Returns:
        dict: Configuration as a dictionary
    """
    if config_path is None:
        # Try multiple possible locations for config.json
        possible_paths = [
            # First try: parent directory of GretahAI_CaseForge (project root)
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config.json"),
            # Second try: GretahAI_CaseForge directory
            os.path.join(os.path.dirname(os.path.dirname(__file__)), "config.json"),
            # Third try: current working directory
            os.path.join(os.getcwd(), "config.json"),
            # Fourth try: relative to gui directory
            os.path.join(os.path.dirname(__file__), "..", "..", "config.json"),
        ]

        config_path = None
        for path in possible_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path):
                config_path = abs_path
                break

        if config_path is None:
            # If still not found, use the first path as default for error message
            config_path = os.path.abspath(possible_paths[0])

    try:
        with open(config_path, 'r') as file:
            config = json.load(file)
        return config
    except FileNotFoundError:
        raise FileNotFoundError(f"Configuration file not found at: {config_path}")
    except json.JSONDecodeError:
        raise ValueError(f"Invalid JSON in configuration file: {config_path}")

@st.cache_resource
def jira_connection(server, user, token):
    options = {"server": server}
    return JIRA(options=options, basic_auth=(user, token))

def load_admin_config():
    # Always load admin_config.json from the parent directory
    admin_config_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'admin_config.json'))
    with open(admin_config_path, "r") as f:
        config = json.load(f)
        return config

def generate_test_scenarios(case_id, test_type, num_scenarios, selected_model, jira_client, ai_provider, google_api_key, is_all_test_types=False, continue_numbering=False, test_run_id=None):
    # Database helper is already imported at the top of the file

    # Create Test_cases folder if it doesn't exist (for raw responses)
    test_cases_dir = Path(os.path.join(os.path.dirname(os.path.abspath(__file__)), "Test_cases"))
    test_cases_dir.mkdir(exist_ok=True)

    # Initialize database if it doesn't exist
    db.init_db(db.DATABASE_PATH)

    # Import the helper functions to manage the test case counter
    from GretahAI_CaseForge.helpers import reset_test_case_counter, set_test_case_counter, get_test_case_counter, _last_jira_id

    # Check if we're generating test cases for a new JIRA ID
    if _last_jira_id is not None and case_id != _last_jira_id and not continue_numbering:
        print(f"New JIRA ID detected: {case_id} (previous: {_last_jira_id}). Resetting test case counter.")
        reset_test_case_counter()

    # Check if test cases exist in the database for this JIRA ID and test type
    # If this is part of the "All Test Case Types" option, use "all"
    # Otherwise, use the test_type parameter
    dashboard_test_type = "all" if is_all_test_types else test_type
    highest_id = db.get_highest_test_case_id_number(db.DATABASE_PATH, case_id, dashboard_test_type)
    test_cases_exist = highest_id > 0

    # Track if we're creating new test cases
    creating_new_test_cases = not test_cases_exist

    # Reset the counter if no test cases exist for this JIRA ID and test type
    # But only if we're not continuing numbering from a previous test type
    if not test_cases_exist and not continue_numbering:
        # For specific test types (not 'all'), always reset the counter if no test cases exist
        # This ensures that each specific test type starts from TC_001 when generated for the first time
        if test_type != 'all' or not test_cases_exist:
            from GretahAI_CaseForge.helpers import reset_test_case_counter, set_test_case_counter
            reset_test_case_counter()
            set_test_case_counter(0)  # Explicitly set to 0 to ensure it starts from TC_001
            print(f"Test case counter reset to 0 for {case_id} ({test_type}). Creating new test cases.")

    # Store the test case status in session state for this test type
    if f"test_case_status_{case_id}_{test_type}" not in st.session_state:
        st.session_state[f"test_case_status_{case_id}_{test_type}"] = {}

    st.session_state[f"test_case_status_{case_id}_{test_type}"] = {
        "exists": test_cases_exist,
        "creating_new": creating_new_test_cases,
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S")
    }

    # Fetch issue from JIRA
    issue = jira_client.issue(case_id)

    # Update the JIRA issue in the database with the actual summary and description from the JIRA API
    summary = issue.fields.summary
    description = issue.fields.description or ""
    status = issue.fields.status.name
    db.update_jira_issue_in_database(db.DATABASE_PATH, case_id, summary, description, status)

    # Check for attachments in the JIRA issue
    attachment_path = None
    if hasattr(issue.fields, 'attachment') and issue.fields.attachment:
        # Create a directory for storing attachments if it doesn't exist
        attached_images_dir = Path(os.path.join(os.path.dirname(os.path.abspath(__file__)), "attached_images"))
        attached_images_dir.mkdir(exist_ok=True)

        # Map filename → attachment URL
        # The 'content' attribute contains the URL to download the attachment
        att_map = {}
        for att in issue.fields.attachment:
            try:
                att_map[att.filename] = att.get_data()
            except Exception as e:
                print(f"Error getting attachment data for {att.filename}: {e}")
                # Try to get the content URL directly if available
                if hasattr(att, 'content'):
                    att_map[att.filename] = att.content

        # Download each attachment to the attached_images folder
        local_map = {}
        for fname, data_or_url in att_map.items():
            # Only download image files
            if any(fname.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']):
                try:
                    unique_name = f"{case_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{fname}"
                    file_path = attached_images_dir / unique_name

                    # Check if we have binary data or a URL
                    if isinstance(data_or_url, bytes):
                        # We already have the binary data
                        with open(file_path, "wb") as f:
                            f.write(data_or_url)
                        local_map[fname] = str(file_path)
                        print(f"Saved attachment data: {fname} to {file_path}")
                    elif isinstance(data_or_url, str) and data_or_url.startswith('http'):
                        # We have a URL, need to download
                        resp = jira_client._session.get(data_or_url, stream=True)
                        if resp.status_code == 200:
                            with open(file_path, "wb") as f:
                                f.write(resp.content)
                            local_map[fname] = str(file_path)
                            print(f"Downloaded attachment: {fname} to {file_path}")
                        else:
                            print(f"⚠️ Failed to download {fname}: HTTP {resp.status_code}")
                    else:
                        print(f"⚠️ Unsupported attachment data type for {fname}: {type(data_or_url)}")
                except Exception as e:
                    print(f"⚠️ Error processing attachment {fname}: {e}")

        # Use the first image attachment for visual analysis
        attachment_path = next((path for path in local_map.values() if os.path.exists(path)), None)
        if attachment_path:
            print(f"Using attachment for visual analysis: {attachment_path}")

    # Get the prompt based on the test type
    if ai_provider == "Local":
        combined_text = extract_test_info_from_issue(issue, test_type=test_type, num_scenarios=num_scenarios)
    else:  # Google AI Studio
        combined_text = generate_gemini_test_gen_prompt(issue, test_type=test_type, num_scenarios=num_scenarios, attachment_path=attachment_path)

    response_text = ""
    token_count = 0  # Initialize token count

    # Generate timestamp for the raw response filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Generate test Cases using the selected AI provider
    start_time = datetime.now()
    if ai_provider == "Local":
        response_text = run_ollama_with_chat(combined_text, model=selected_model)
        # Note: Local Ollama token tracking is not implemented here
        token_count = None  # Indicate token count is not available for local

    elif ai_provider == "Google AI Studio":
        if not google_api_key:
            raise ValueError("Google AI Studio API Key is required.")

        # Handle the case where combined_text is a tuple (image, prompt) from generate_gemini_test_gen_prompt
        # or a regular string prompt
        result = run_google_ai_studio(combined_text, api_key=google_api_key, model=selected_model)

        # Check if the result is a tuple (text, token_count) or just an error message
        if isinstance(result, tuple) and len(result) == 2:
            response_text, token_count = result
        else:
            # If it's just a string (error message)
            response_text = str(result)  # Ensure it's a string
            token_count = None

        # If we used an attachment, log that information
        if attachment_path and os.path.exists(attachment_path):
            print(f"Used attachment in prompt: {attachment_path}")
            # Check if the response already has the visual analysis note
            if not response_text.startswith("Generated with visual analysis of attachment:"):
                # Add a note to the response text about the attachment being used
                response_text = f"Generated with visual analysis of attachment: {os.path.basename(attachment_path)}\n\n{response_text}"

        # --- Record Google AI Usage ---
        current_time = datetime.now()
        st.session_state.google_request_timestamps.append(current_time)
        if token_count is not None and token_count > 0:
            st.session_state.google_token_usage.append((current_time, token_count))
        save_usage_data()
        # -----------------------------

    else:
        raise ValueError("Invalid AI provider selected.")

    processing_time = (datetime.now() - start_time).total_seconds()

    # Save the raw response before trying to parse it
    raw_response_dir = Path(os.path.join(os.path.dirname(os.path.abspath(__file__)), "raw_responses"))
    raw_response_dir.mkdir(exist_ok=True)
    raw_response_file = raw_response_dir / f"Latest_testcases_{case_id}_{test_type}_{timestamp}_raw.txt"
    try:
        with open(raw_response_file, "w", encoding="utf-8") as f:
            f.write(response_text)
    except Exception as e:
        st.warning(f"Failed to save raw response: {e}")

    # Parse the response and save to database (only if response_text is generated)
    if response_text:
        try:
            # Set the dashboard_test_type based on what was selected in the dropdown
            # If this is part of the "All Test Case Types" option, use "all"
            # Otherwise, use the test_type parameter
            dashboard_test_type = "all" if is_all_test_types else test_type

            # Parse the response text into a DataFrame
            # Pass the dashboard_test_type to the function
            df = parse_test_scenarios_json(response_text, issue, start_id_from=None, continue_numbering=continue_numbering, dashboard_test_type=dashboard_test_type)

            # Get the current user from session state (if logged in)
            current_user = st.session_state.get("admin_username", "anonymous")

            # Create a test run if one wasn't provided
            if test_run_id is None:
                test_run_id = db.create_test_run(
                    db.DATABASE_PATH,
                    case_id,
                    dashboard_test_type,
                    user_name=current_user,
                    notes=f"Generated with {ai_provider} using {selected_model}"
                )

            # Save the DataFrame to the database with user info and test run ID
            db_success = db.save_test_cases_to_database(
                db.DATABASE_PATH,
                case_id,
                df,
                dashboard_test_type,
                user_name=current_user,
                test_run_id=test_run_id
            )

            if db_success:
                print(f"Test cases saved to database successfully for {case_id} ({test_type}).")
                # Create a virtual output file path for compatibility with existing code
                output_file = f"database://{case_id}/{dashboard_test_type}/{timestamp}"

                # Force update the session state to ensure the tabs are displayed
                st.session_state.scenario_data = {
                    "issue": issue,
                    "response": response_text,
                    "output_file": output_file,
                    "processing_time": processing_time,
                    "ai_provider": ai_provider,
                    "model_used": selected_model,
                    "tokens_used": token_count,
                    "test_type": test_type,
                    "start_id": None,  # Will be calculated later
                    "end_id": None,  # Will be calculated later,
                    "used_attachment": attachment_path is not None and os.path.exists(attachment_path),
                    "attachment_name": os.path.basename(attachment_path) if attachment_path and os.path.exists(attachment_path) else None,
                    "num_test_cases": len(df["Test Case ID"].dropna().unique()) if "Test Case ID" in df.columns else 0,
                    "has_issues": False,
                    "issues": []
                }
            else:
                st.error("Failed to save test cases to database.")
                output_file = None
        except Exception as e:
            error_message = f"Failed to parse and save test cases: {str(e)}"
            st.error(error_message)
            st.error("Raw response may not be in valid JSON format. Please try generating test cases again.")

            # If we have a test run ID, update it with the error information
            if test_run_id is not None:
                db.update_test_run(
                    db.DATABASE_PATH,
                    test_run_id,
                    num_test_cases=0,
                    status="failed",
                    notes=f"Error: {error_message}"
                )

            # Return a tuple that includes the raw response for debugging
            return issue, response_text, None, processing_time, token_count
    else:
        st.warning("AI did not return a valid response. Test cases not generated.")
        output_file = None # No output file if no response

    return issue, response_text, output_file, processing_time, token_count # Return token_count

# --- Initialize Session State ---
# Store generated data
if "scenario_data" not in st.session_state:
    st.session_state.scenario_data = None

# Load usage data from file
def load_usage_data():
    try:
        # Use path relative to the current file
        usage_data_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "usage_data.json")
        if os.path.exists(usage_data_path):
            with open(usage_data_path, "r") as f:
                data = json.load(f)
                # Convert timestamps back to datetime objects
                request_timestamps = [datetime.fromisoformat(ts) for ts in data.get("request_timestamps", [])]
                token_usage = [(datetime.fromisoformat(ts), count) for ts, count in data.get("token_usage", [])]
                return request_timestamps, token_usage
        return [], []
    except FileNotFoundError:
        return [], []
    except Exception as e:
        st.error(f"Error loading usage data: {e}")
        return [], []

# Save usage data to file
def save_usage_data():
    try:
        # Convert datetime objects to ISO format strings for JSON serialization
        request_timestamps = [ts.isoformat() for ts in st.session_state.google_request_timestamps]
        token_usage = [(ts.isoformat(), count) for ts, count in st.session_state.google_token_usage]

        data = {
            "request_timestamps": request_timestamps,
            "token_usage": token_usage
        }
        # Use path relative to the current file
        usage_data_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "usage_data.json")
        with open(usage_data_path, "w") as f:
            json.dump(data, f)
    except Exception as e:
        st.error(f"Error saving usage data: {e}")


def merge_excel_files(input_files, output_file, creating_new_main_file=False):
    """Merges multiple Excel files into a single Excel file with all Test cases in one sheet.

    Args:
        input_files: List of input Excel files to merge
        output_file: Name of the output Excel file
        creating_new_main_file: Whether we're creating a new main file (if True, don't include existing main file data)
    """
    # Create a combined DataFrame
    all_data = []

    # Ensure input_files is a list and contains valid files
    if not input_files:
        st.warning("No input files provided for merging.")
        return None

    # Filter out any files that don't exist
    valid_input_files = [f for f in input_files if os.path.exists(f)]
    if not valid_input_files:
        st.warning("None of the input files exist.")
        return None

    if len(valid_input_files) < len(input_files):
        st.warning(f"Some input files don't exist. Processing {len(valid_input_files)} out of {len(input_files)} files.")

    # Check if the main file exists and read it if it does
    test_cases_dir = Path(os.path.join(os.path.dirname(os.path.abspath(__file__)), "Test_cases"))
    test_cases_dir.mkdir(exist_ok=True)  # Ensure the directory exists
    main_file_path = test_cases_dir / output_file

    # Check if this is a merge to the main ALL file
    is_main_all_file = "_ALL.xlsx" in output_file and not any(c.isdigit() for c in output_file.split('_')[-1])

    # Extract JIRA ID and test type from the output file name
    parts = output_file.split('_')
    jira_id = None
    test_type = None

    if len(parts) >= 3:
        # Format is typically Complete_testcases_TP-1_ALL.xlsx or Complete_testcases_TP-1_positive.xlsx
        jira_id = parts[2]  # TP-1
        if len(parts) >= 4:
            test_type = parts[3].split('.')[0].lower()  # ALL or positive

    # Get the main file status from session state
    main_file_status = None
    if jira_id and test_type:
        main_file_status = st.session_state.get(f"main_file_status_{jira_id}_{test_type}", {})
        # Override creating_new_main_file if we have it in session state
        if "creating_new" in main_file_status:
            creating_new_main_file = main_file_status.get("creating_new")

    if main_file_path.exists() and not creating_new_main_file:
        try:
            # Read the existing main file
            main_df = pd.read_excel(main_file_path)

            # Only include the main file data if we're appending to the main file
            all_data.append(main_df)
            st.info(f"Found existing main file: {main_file_path}. Will append new test cases.")
        except Exception as e:
            st.warning(f"Error reading existing main file: {str(e)}. Creating a new file.")

    for file in valid_input_files:
        try:
            # Read the Excel file
            df = pd.read_excel(file)

            # Extract test type from filename to clearly identify the test type
            file_basename = os.path.basename(file)
            parts = file_basename.split('_')

            # Initialize test_type
            test_type = ""

            # First, try to get the test type from the Test Type column if it exists
            if "Test Type" in df.columns:
                # Get unique non-empty test types
                unique_test_types = df["Test Type"].dropna().unique()
                if len(unique_test_types) > 0 and unique_test_types[0]:
                    test_type = unique_test_types[0]

            # If we couldn't get the test type from the DataFrame, extract it from the filename
            if not test_type:
                # Handle different filename formats
                if len(parts) >= 4 and parts[-2].lower() in ['positive', 'negative', 'security', 'performance', 'all']:
                    test_type = parts[-2].upper()
                else:
                    # Try to extract from the last part before the extension
                    test_type = parts[-1].split('.')[0].upper()

            # Add a column for test type if it doesn't exist
            if "Test Type" not in df.columns:
                # Only set Test Type for rows that have a Test Case ID
                test_case_id_mask = df["Test Case ID"].notna() & (df["Test Case ID"] != "")
                df.loc[test_case_id_mask, "Test Type"] = test_type

            # Determine the group number based on the test type
            group_number = 1  # Default group number for POSITIVE
            if test_type == "NEGATIVE":
                group_number = 2
            elif test_type == "SECURITY":
                group_number = 3
            elif test_type == "PERFORMANCE":
                group_number = 4

            # Set the Test Group value with the fixed group number based on test type
            # Only set Test Group for rows that have a Test Case ID
            test_case_id_mask = df["Test Case ID"].notna() & (df["Test Case ID"] != "")
            df.loc[test_case_id_mask, "Test Group"] = f"Group {group_number}: {test_type}"

            # Add to our combined data
            all_data.append(df)

        except Exception as e:
            st.error(f"Error processing file {file}: {str(e)}")

    # Combine all DataFrames
    if all_data:
        # Find the highest Test Case ID number if we're appending to the main file
        start_id_from = None
        if is_main_all_file and len(all_data) > 1 and "Test Case ID" in all_data[0].columns:
            # Extract the highest test case ID from the main file
            try:
                # Get all Test Case IDs from the first DataFrame (main file)
                test_case_ids = all_data[0]["Test Case ID"].dropna().unique()

                # Extract numeric parts and find the highest
                highest_id = 0
                for tc_id in test_case_ids:
                    if isinstance(tc_id, str) and tc_id.startswith("TC_"):
                        try:
                            id_num = int(tc_id.split("_")[1])
                            highest_id = max(highest_id, id_num)
                        except (ValueError, IndexError):
                            pass

                if highest_id > 0:
                    start_id_from = highest_id
                    print(f"Continuing Test Case IDs from {highest_id}")
            except Exception as e:
                st.warning(f"Could not determine highest Test Case ID: {e}")

        combined_df = pd.concat(all_data, ignore_index=True)

        # Ensure the main file is saved in the Test_cases directory
        main_output_path = test_cases_dir / output_file

        # Fix Test Group values based on Test Type before saving
        if "Test Type" in combined_df.columns and "Test Group" in combined_df.columns:
            # Only update rows with a Test Case ID
            test_case_id_mask = combined_df["Test Case ID"].notna() & (combined_df["Test Case ID"] != "")

            # For each test type, set the correct group number
            for idx, row in combined_df[test_case_id_mask].iterrows():
                test_type = row["Test Type"]
                if pd.notna(test_type) and test_type:
                    # Determine the group number based on the test type
                    group_number = 1  # Default group number for POSITIVE
                    if test_type == "NEGATIVE":
                        group_number = 2
                    elif test_type == "SECURITY":
                        group_number = 3
                    elif test_type == "PERFORMANCE":
                        group_number = 4

                    # Set the Test Group value
                    combined_df.loc[idx, "Test Group"] = f"Group {group_number}: {test_type}"

        # Save to Excel with proper formatting
        try:
            # Use the helper function to create a formatted Excel file
            create_formatted_excel_from_scenarios(
                combined_df,  # Pass the combined dataframe directly
                str(main_output_path),  # Convert Path to string
                is_dataframe=True,  # Flag to indicate input is a DataFrame
                start_id_from=start_id_from,  # Pass the starting ID if we found one
                continue_numbering=True  # Always continue numbering when merging files
            )
            st.success(f"✅ Successfully merged test cases into {main_output_path}")
            return str(main_output_path)
        except Exception as e:
            st.error(f"Error saving merged file: {str(e)}")
            # Fallback to simple Excel save
            combined_df.to_excel(main_output_path, index=False)
            st.warning(f"Saved unformatted file to {main_output_path} due to formatting error")
            return str(main_output_path)
    else:
        st.warning("No data found to combine")
        return None