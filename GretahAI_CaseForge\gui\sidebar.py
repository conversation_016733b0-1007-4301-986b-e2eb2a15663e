"""
Sidebar functionality for the GUI application.
"""

import streamlit as st
import os
import pandas as pd
from pathlib import Path
from helpers import is_ollama_running, upload_edited_excel

def render_sidebar(jira_connected, config):
    """
    Render the sidebar for the application.

    Args:
        jira_connected (bool): Whether the JIRA connection is established
        config (dict): Configuration dictionary

    Returns:
        tuple: (ai_provider, google_api_key, selected_model)
    """
    # Initialize variables that need to be available globally
    uploaded_file = None
    ai_provider = "Local"  # Default value
    selected_model = "gemma:1b"  # Default value
    google_api_key = ""  # Default value

    with st.sidebar:
        # CaseForge-specific branding
        st.image("https://cogniron.com/wp-content/uploads/2024/10/image-69.png", width=300)
        
        # Replace hard-coded colors with classes that will adapt to the theme
        st.markdown("""
        <div style="text-align: center; margin-bottom: 20px;">
            <h2 class="sidebar-header" style="margin: 0; padding: 0; font-size: 1.5rem;">GretahAI CaseForge</h2>
            <p class="sidebar-subheader" style="margin: 0; padding: 0; font-size: 0.9rem;">Test Case Management</p>
        </div>
        """, unsafe_allow_html=True)

        # Initialize the page state if it doesn't exist
        if "current_page" not in st.session_state:
            st.session_state["current_page"] = "generator"

        # Navigation section at the top
        st.markdown("### Navigation")

        # Create radio buttons for navigation that look like the other inputs
        current_page = st.radio(
            "Select Section",
            options=["Test Generator", "Test Analysis"],
            index=0 if st.session_state["current_page"] == "generator" else 1,
            key="navigation_radio",
            horizontal=True
        )

        # Update the session state based on selection
        if current_page == "Test Generator" and st.session_state["current_page"] != "generator":
            st.session_state["current_page"] = "generator"
            st.rerun()
        elif current_page == "Test Analysis" and st.session_state["current_page"] != "analysis":
            st.session_state["current_page"] = "analysis"
            st.rerun()

        # Only show AI settings if we're on the Test Generator page
        if st.session_state["current_page"] == "generator":
            st.markdown("---")
            # Add AI Provider selection
            ai_provider = st.radio(
                "Select AI Provider",
                options=["Local", "Google AI Studio"],
                index=0, # Default to Local
                key="ai_provider_radio" # Add a key for stability
            )

            # Get Google API key from config but don't show the input field
            try:
                google_api_key = config.get("google_api_key", "")
            except:
                pass

            st.markdown("### Model Settings")
            # Adjust model options based on provider (example)
            if ai_provider == "Local":
                # Example local models - adjust based on what 'ollama list' shows
                model_options = ["gemma:1b", "gemma:4b", "mistral"] # Replace with actual available local models
                provider_tag = "📍 **LOCAL**"
                # Try to get actual local models if Ollama is running
                try:
                    ollama_running, ollama_version = is_ollama_running()
                    if not ollama_running:
                        st.warning("⚠️ Ollama not detected. Local AI provider may not work.")
                except Exception:
                    pass
            else: # Google AI Studio
                model_options = ["gemini-1.5-flash", "gemini-1.5-flash-8b"]
                provider_tag = "☁️ **GOOGLE**"

            col1, col2 = st.columns([3, 1])
            with col1:
                # Use a different key for the selectbox depending on the provider to avoid state issues
                model_key = f"model_select_{ai_provider.lower().replace(' ', '_')}"
                selected_model = st.selectbox("Select AI Model", model_options, key=model_key)
            with col2:
                st.markdown(provider_tag)

            st.markdown("---")
            st.markdown("### Context Settings")
            
            # Initialize the session state for context_prompt if it doesn't exist
            if "context_prompt" not in st.session_state:
                st.session_state["context_prompt"] = ""
                
            # Add a text area for additional context that will be used automatically
            st.text_area(
                "Additional Context",
                value=st.session_state["context_prompt"],  # Use the session state value
                help="Additional context or instructions that will be automatically used when generating test cases",
                key="context_prompt",
                placeholder="Enter additional context or instructions here..."
            )
            
            # Add a button to clear the context
            if st.button("Clear Context", key="clear_context_button"):
                st.session_state["context_prompt"] = ""
                st.rerun()

            st.markdown("---")
            st.markdown("### Upload Edited Test Cases")
            uploaded_file = st.file_uploader("Upload edited Excel file", type=["xlsx"], help="Upload an edited test case Excel file to store it in the system")
        else:
            # If we're on the Test Analysis page, show login status and options
            st.markdown("---")

            # Check if the user is logged in
            is_admin_logged_in = st.session_state.get("is_admin_logged_in", False)
            current_user = st.session_state.get("admin_username", "")

            if is_admin_logged_in and current_user:
                st.success(f"Logged in as: {current_user}")

                # Add logout button
                if st.button("Logout", key="sidebar_analysis_logout"):
                    st.session_state["admin_username"] = ""
                    st.session_state["is_admin_logged_in"] = False
                    st.session_state["admin_user"] = ""
                    st.rerun()
            else:
                st.info("Please log in through the Admin Panel to view your test runs.")

        # Only show upload options if we're on the Generator page and have an uploaded file
        if st.session_state["current_page"] == "generator" and "uploaded_file" in locals() and uploaded_file is not None:
            # Add information about the upload options
            with st.expander("About Upload Options"):
                st.markdown("### Update Database")
                st.info("This option will save the edited test cases to the database with the 'is_edited' flag set to True. When viewing test cases, edited versions will be prioritized over original versions.")

                st.markdown("### Jira Integration (Coming Soon)")
                st.info("When implemented, this option will:")
                st.markdown("""1. Convert the Excel file to CSV format
                2. Upload the CSV to your Jira project using credentials from config.json
                3. Link the test cases to the User Story IDs specified in the Excel file""")

            # Store the uploaded file in session state for later use
            st.session_state["uploaded_excel_file"] = uploaded_file
            st.success(f"Excel file '{uploaded_file.name}' uploaded successfully. Choose an action below:")

            col1, col2 = st.columns(2)

            with col1:
                if st.button("Update Database"):
                    with st.spinner("Updating database..."):
                        # Get the current logged-in user
                        current_user = st.session_state.get("admin_username", "admin")
                        success, message, file_path = upload_edited_excel(st.session_state["uploaded_excel_file"], current_user)
                        if success:
                            st.success(message)
                        else:
                            st.error(message)

            with col2:
                # Jira Integration button (disabled for now)
                if st.button("Jira Integration", disabled=False, help="Upload test cases to Jira Zephyr Scale"):
                    if "uploaded_excel_file" in st.session_state:
                        with st.spinner("Uploading test cases to Jira Zephyr Scale..."):
                            try:
                                # Read the Excel file into a DataFrame
                                df = pd.read_excel(st.session_state["uploaded_excel_file"])

                                # Extract user story IDs from the DataFrame
                                user_story_ids = df["User Story ID"].dropna().unique().tolist()

                                # Get Jira credentials from config
                                jira_url = config["jira_server"]
                                jira_username = config["jira_user"]
                                jira_api_token = config["jira_api_token"]

                                # Import the Zephyr integration module
                                from helpers import load_zephyr_integration
                                if load_zephyr_integration():
                                    # This will import the upload_test_cases_to_jira function
                                    from zephyr_integration import upload_test_cases_to_jira

                                    # Upload test cases to Jira
                                    success, message = upload_test_cases_to_jira(
                                        dataframe=df,
                                        jira_url=jira_url,
                                        jira_username=jira_username,
                                        jira_password=jira_api_token,
                                        user_story_ids=user_story_ids
                                    )

                                    if success:
                                        st.success(message)
                                    else:
                                        st.error(message)
                                else:
                                    st.error("Zephyr integration module not available")
                            except Exception as e:
                                st.error(f"Error uploading test cases to Jira: {str(e)}")
                    else:
                        st.warning("Please upload an Excel file first")
                # Keep the info message for now
                st.info("Jira integration with Zephyr Scale is now available.")

        st.markdown("---")

        # Add Google AI Studio Usage section to sidebar (always show it)
        # Calculate usage metrics
        from datetime import datetime, timedelta
        from gui.utils import save_usage_data

        now = datetime.now()
        one_minute_ago = now - timedelta(minutes=1)
        one_day_ago = now - timedelta(days=1)

        # Filter timestamps for the relevant periods
        requests_last_minute = [ts for ts in st.session_state.google_request_timestamps if ts > one_minute_ago]
        requests_last_day = [ts for ts in st.session_state.google_request_timestamps if ts > one_day_ago]

        # Filter token usage for the last minute
        tokens_last_minute_data = [tup for tup in st.session_state.google_token_usage if tup[0] > one_minute_ago]
        tokens_last_minute_sum = sum(count for _, count in tokens_last_minute_data)  # Use _ for unused variable

        # Calculate metrics
        current_rpm = len(requests_last_minute)
        current_tpm = tokens_last_minute_sum
        current_rpd = len(requests_last_day)

        # Display metrics in a more compact format for the sidebar
        with st.expander("☁️ Google AI Studio Usage", expanded=False):
            st.metric("TPM (Tokens/Minute)", f"{current_tpm:,}")
            st.metric("RPM (Requests/Minute)", f"{current_rpm}")
            st.metric("RPD (Requests/Day)", f"{current_rpd}")

            # Add a button to clear session usage stats
            if st.button("Clear Usage Stats", key="clear_google_usage_stats"):
                st.session_state.google_request_timestamps = []
                st.session_state.google_token_usage = []
                save_usage_data() # Clear the file
                st.success("Usage stats cleared")
                st.rerun()

        # Add Admin Panel
        # Check if we should automatically open the Admin Panel
        auto_open_admin = st.session_state.get("open_admin_panel", False)
        if auto_open_admin:
            # Reset the flag
            st.session_state["open_admin_panel"] = False

        # Import admin configuration and database helper
        import admin_config
        import Test_case_db_helper as db
        # Reload the module to ensure we have the latest version with our new function
        import importlib
        importlib.reload(db)

        with st.expander("🔐 Admin Panel", expanded=auto_open_admin):
            st.warning("⚠️ This panel provides administrative functions that can delete data.")

            # Create tabs for different admin functions
            admin_tabs = st.tabs(["Login", "User Management", "Settings", "Database Operations"])

            # Login Tab
            with admin_tabs[0]:
                st.subheader("Admin Login")

                # Username and password inputs
                admin_username = st.text_input("Username", key="admin_username_input")
                admin_password = st.text_input("Password", type="password", key="admin_password_input")

                # Login button with better styling
                col1, col2 = st.columns([1, 3])
                with col1:
                    login_button = st.button("🔐 Login", key="admin_login_button")

                # Handle login button
                if login_button:
                    if admin_username and admin_password:
                        with st.spinner("Logging in..."):
                            if admin_config.verify_user(admin_username, admin_password):
                                if admin_config.is_admin_user(admin_username):
                                    st.session_state["admin_logged_in"] = True
                                    st.session_state["admin_username"] = admin_username
                                    # Set is_admin_logged_in flag to share with test runs tab
                                    st.session_state["is_admin_logged_in"] = True
                                    # Store the admin user in a separate variable for the test runs tab
                                    st.session_state["admin_user"] = admin_username
                                    st.success(f"Logged in as {admin_username}")
                                    st.rerun()
                                else:
                                    st.error("You do not have admin privileges")
                            else:
                                st.error("Invalid username or password")
                    else:
                        st.error("Username and password are required")

                # Check if a user is logged in
                is_logged_in = st.session_state.get("admin_logged_in", False)
                logged_in_username = st.session_state.get("admin_username", "")

                if is_logged_in:
                    st.success(f"Logged in as {logged_in_username}")

                    # Logout button with better styling
                    col1, col2 = st.columns([1, 3])
                    with col1:
                        logout_button = st.button("🚪 Logout", key="admin_logout_button")

                    # Handle logout button
                    if logout_button:
                        with st.spinner("Logging out..."):
                            st.session_state["admin_logged_in"] = False
                            st.session_state["admin_username"] = ""
                            # Clear is_admin_logged_in flag
                            st.session_state["is_admin_logged_in"] = False
                            # Clear admin_user
                            st.session_state["admin_user"] = ""
                            st.rerun()

            # User Management Tab
            with admin_tabs[1]:
                st.subheader("User Management")

                # Check if admin is logged in
                is_logged_in = st.session_state.get("admin_logged_in", False)
                logged_in_username = st.session_state.get("admin_username", "")

                if not is_logged_in:
                    st.warning("Please login as an admin to manage users")
                else:
                    # Display existing users
                    users = admin_config.get_config("users")
                    if users:
                        st.write("Existing Users:")
                        user_data = []
                        for user in users:
                            user_data.append({
                                "Username": user.get("username"),
                                "Role": user.get("role"),
                                "Created": user.get("created_at", "").split("T")[0] if "T" in user.get("created_at", "") else user.get("created_at", "")
                            })
                        st.dataframe(user_data)

                    # Add new user section
                    st.write("---")
                    st.subheader("Add New User")
                    new_username = st.text_input("New Username", key="new_username_input")
                    new_password = st.text_input("New Password", type="password", key="new_password_input")
                    new_role = st.selectbox("Role", options=["user", "admin"], key="new_role_select")

                    # Use columns for better button layout
                    col1, col2 = st.columns([1, 3])
                    with col1:
                        add_user_button = st.button("➕ Add User", key="add_user_button")

                    # Handle add user button
                    if add_user_button:
                        if new_username and new_password:
                            with st.spinner("Adding user..."):
                                success = admin_config.add_user(
                                    new_username,
                                    new_password,
                                    new_role,
                                    logged_in_username,
                                    admin_password
                                )
                                if success:
                                    st.success(f"User '{new_username}' added successfully")
                                    st.rerun()
                                else:
                                    st.error("Failed to add user. Make sure you have admin privileges and the username is unique.")
                        else:
                            st.error("Username and password are required")

                    # Delete user section
                    st.write("---")
                    st.subheader("Delete User")

                    # Get usernames for selection
                    usernames = [user.get("username") for user in users if user.get("username") != logged_in_username]
                    if usernames:
                        delete_username = st.selectbox("Select User to Delete", options=usernames, key="delete_username_select")

                        # Use columns for better button layout
                        col1, col2 = st.columns([1, 3])
                        with col1:
                            delete_user_button = st.button("🗑️ Delete User", key="delete_user_button")

                        # Add confirmation checkbox
                        with col2:
                            if delete_user_button:
                                confirm_delete = st.checkbox("Confirm deletion", key="delete_user_confirm")

                        # Handle delete user button
                        if delete_user_button:
                            if delete_username:
                                if 'confirm_delete' in locals() and confirm_delete:
                                    with st.spinner("Deleting user..."):
                                        success = admin_config.delete_user(
                                            delete_username,
                                            logged_in_username,
                                            admin_password
                                        )
                                        if success:
                                            st.success(f"User '{delete_username}' deleted successfully")
                                            st.rerun()
                                        else:
                                            st.error("Failed to delete user. Make sure you have admin privileges.")
                                else:
                                    st.error("Please confirm deletion by checking the box")
                    else:
                        st.info("No other users to delete")

                    # Change own password section
                    st.write("---")
                    st.subheader("Change Your Password")
                    current_password = st.text_input("Current Password", type="password", key="current_password_input")
                    new_password = st.text_input("New Password", type="password", key="change_new_password_input")
                    confirm_password = st.text_input("Confirm New Password", type="password", key="confirm_new_password_input")

                    # Use columns for better button layout
                    col1, col2 = st.columns([1, 3])
                    with col1:
                        change_pwd_button = st.button("🔄 Change Password", key="change_own_password_button")

                    # Handle change password button
                    if change_pwd_button:
                        if current_password and new_password and confirm_password:
                            if new_password == confirm_password:
                                with st.spinner("Changing password..."):
                                    success = admin_config.change_user_password(
                                        logged_in_username,
                                        current_password,
                                        new_password
                                    )
                                    if success:
                                        st.success("Password changed successfully")
                                    else:
                                        st.error("Failed to change password. Current password may be incorrect.")
                            else:
                                st.error("New passwords do not match")
                        else:
                            st.error("All fields are required")

            # Settings Tab
            with admin_tabs[2]:
                st.subheader("Settings")

                # Check if admin is logged in
                is_logged_in = st.session_state.get("admin_logged_in", False)
                logged_in_username = st.session_state.get("admin_username", "")

                if not is_logged_in:
                    st.warning("Please login as an admin to change settings")
                else:
                    # Get current settings
                    allow_delete = admin_config.get_config("allow_delete_test_cases")
                    allow_clear = admin_config.get_config("allow_clear_database")

                    # Display current settings
                    st.info(f"Delete test cases without password: {'Allowed' if allow_delete else 'Requires Admin Password'}")
                    st.info(f"Clear entire database without password: {'Allowed' if allow_clear else 'Requires Admin Password'}")

                    col1, col2 = st.columns(2)

                    with col1:
                        if st.button("Allow Delete Test Cases", key="allow_delete_button", help="Allow deleting test cases without admin password"):
                            admin_config.update_config("allow_delete_test_cases", True)
                            st.success("Setting updated successfully")
                            st.rerun()

                        if st.button("Allow Clear Database", key="allow_clear_button", help="Allow clearing the entire database without admin password"):
                            admin_config.update_config("allow_clear_database", True)
                            st.success("Setting updated successfully")
                            st.rerun()

                    with col2:
                        if st.button("Require Password for Delete", key="require_password_delete_button", help="Require admin password to delete test cases"):
                            admin_config.update_config("allow_delete_test_cases", False)
                            st.success("Setting updated successfully")
                            st.rerun()

                        if st.button("Require Password for Clear", key="require_password_clear_button", help="Require admin password to clear the database"):
                            admin_config.update_config("allow_clear_database", False)
                            st.success("Setting updated successfully")
                            st.rerun()

            # Database Operations Tab
            with admin_tabs[3]:
                st.subheader("Database Operations")

                # Check if admin is logged in
                is_logged_in = st.session_state.get("admin_logged_in", False)
                logged_in_username = st.session_state.get("admin_username", "")

                if not is_logged_in:
                    st.warning("Please login as an admin to perform database operations")
                else:
                    # Create tabs for different deletion operations
                    delete_tabs = st.tabs(["Delete by JIRA ID", "Delete Edited Test Cases", "Delete by Time Range"])

                    # Tab 1: Delete by JIRA ID
                    with delete_tabs[0]:
                        # JIRA ID and test type for deletion
                        delete_jira_id = st.text_input("JIRA ID", key="delete_jira_id_input")
                        delete_test_type = st.selectbox(
                            "Test Type to Delete",
                            options=["all", "positive", "negative", "security", "performance"],
                            key="delete_test_type_select"
                        )

                        # Add some spacing
                        st.write("")

                        # Delete button
                        if st.button("🗑️ Delete Test Cases",
                                    key="delete_test_cases_button",
                                    help="Delete test cases for the specified JIRA ID and test type"):
                            if delete_jira_id:
                                with st.spinner("Deleting test cases..."):
                                    success = db.clear_database_for_jira(db.DATABASE_PATH, delete_jira_id, delete_test_type, admin_password)
                                    if success:
                                        st.success(f"Successfully deleted test cases for {delete_jira_id} ({delete_test_type})")
                                    else:
                                        st.error("Failed to delete test cases. Admin password may be required.")
                            else:
                                st.error("Please enter a JIRA ID")

                    # Tab 2: Delete Edited Test Cases
                    with delete_tabs[1]:
                        st.info("This will delete test cases that are marked as edited (is_edited = 1). These are typically test cases that have been uploaded through the 'Upload Edited Excel' feature.")

                        # JIRA ID for deletion (optional)
                        delete_edited_jira_id = st.text_input("JIRA ID (optional, leave empty to delete all edited test cases)", key="delete_edited_jira_id_input")

                        # Add some spacing
                        st.write("")

                        # Delete button - using the same simple approach as Delete by JIRA ID
                        if st.button("🗑️ Delete Edited Test Cases",
                                   key="delete_edited_test_cases_button",
                                   help="Delete test cases marked as edited (is_edited = 1)"):
                            if delete_edited_jira_id:
                                # Delete edited test cases for specific JIRA ID
                                with st.spinner("Deleting edited test cases..."):
                                    success = db.delete_edited_test_cases(db.DATABASE_PATH, delete_edited_jira_id, admin_password)
                                    if success:
                                        st.success(f"Successfully deleted edited test cases for {delete_edited_jira_id}")
                                    else:
                                        st.error(f"Failed to delete edited test cases for {delete_edited_jira_id}. Admin password may be required.")
                            else:
                                # Delete all edited test cases
                                with st.spinner("Deleting all edited test cases..."):
                                    success = db.delete_all_edited_test_cases(db.DATABASE_PATH, admin_password)
                                    if success:
                                        st.success("Successfully deleted all edited test cases")
                                    else:
                                        st.error("Failed to delete all edited test cases. Admin password may be required.")

                    # Tab 3: Delete by Time Range
                    with delete_tabs[2]:
                        st.info("Delete test cases created within a specific time range.")

                        # Date range selection
                        col1, col2 = st.columns(2)
                        with col1:
                            start_date = st.date_input("Start Date", key="delete_start_date")
                        with col2:
                            end_date = st.date_input("End Date", key="delete_end_date")

                        # Add some spacing
                        st.write("")

                        # Delete button with confirmation
                        col1, col2 = st.columns([3, 1])
                        with col1:
                            delete_by_date_button = st.button("🗑️ Delete Test Cases by Date",
                                                        key="delete_by_date_button",
                                                        help="Delete test cases created within the specified date range")

                        # Add confirmation checkbox
                        with col2:
                            if delete_by_date_button:
                                confirm_delete_by_date = st.checkbox("Confirm", key="delete_by_date_confirm")

                        # Handle delete by date button
                        if delete_by_date_button:
                            if 'confirm_delete_by_date' in locals() and confirm_delete_by_date:
                                with st.spinner("Deleting test cases by date..."):
                                    # Convert dates to strings in the format expected by the database function
                                    start_date_str = start_date.strftime("%Y-%m-%d")
                                    end_date_str = end_date.strftime("%Y-%m-%d")

                                    # Call the database function to delete test cases by date range
                                    success = db.delete_test_cases_by_date_range(db.DATABASE_PATH, start_date_str, end_date_str, admin_password)

                                    if success:
                                        st.success(f"Successfully deleted test cases created between {start_date_str} and {end_date_str}")
                                    else:
                                        st.error(f"Failed to delete test cases by date range. Admin password may be required.")
                            else:
                                st.error("Please confirm by checking the box to delete test cases by date range")

                    # Add a separator
                    st.markdown("---")

                    # Clear Entire Database section
                    st.subheader("Clear Entire Database")
                    st.warning("⚠️ This will delete ALL test cases, test runs, and JIRA issues in the database. This action cannot be undone.")

                    # Use a direct SQL script approach
                    if "clear_db_state" not in st.session_state:
                        st.session_state.clear_db_state = "initial"

                    if st.session_state.clear_db_state == "initial":
                        # Show the clear button
                        if st.button("⚠️ Clear Entire Database", key="clear_database_button"):
                            st.session_state.clear_db_state = "confirm"

                    elif st.session_state.clear_db_state == "confirm":
                        # Show confirmation message and buttons
                        st.info("Are you sure you want to clear the entire database? This action cannot be undone.")

                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button("✅ Yes, Clear Database", key="confirm_clear_db"):
                                st.session_state.clear_db_state = "processing"

                        with col2:
                            if st.button("❌ No, Cancel", key="cancel_clear_db"):
                                st.session_state.clear_db_state = "initial"

                    elif st.session_state.clear_db_state == "processing":
                        # Process the database clearing
                        with st.spinner("Clearing database..."):
                            try:
                                # Use the SQL script directly
                                import subprocess
                                import os

                                # Make sure the SQL script exists
                                sql_script_path = "clear_database.sql"
                                if not os.path.exists(sql_script_path):
                                    # Create the SQL script if it doesn't exist
                                    with open(sql_script_path, "w") as f:
                                        f.write("-- Disable foreign key constraints\n")
                                        f.write("PRAGMA foreign_keys = OFF;\n\n")
                                        f.write("-- Begin transaction\n")
                                        f.write("BEGIN TRANSACTION;\n\n")
                                        f.write("-- Delete all data from tables in the correct order\n")
                                        f.write("DELETE FROM test_steps;\n")
                                        f.write("DELETE FROM test_cases;\n")
                                        f.write("DELETE FROM test_runs;\n")
                                        f.write("DELETE FROM jira_issues;\n")
                                        f.write("DELETE FROM test_case_executions;\n\n")
                                        f.write("-- Reset the auto-increment counters\n")
                                        f.write("DELETE FROM sqlite_sequence WHERE name IN ('test_steps', 'test_cases', 'test_runs', 'jira_issues', 'test_case_executions');\n\n")
                                        f.write("-- Commit the transaction\n")
                                        f.write("COMMIT;\n\n")
                                        f.write("-- Re-enable foreign key constraints\n")
                                        f.write("PRAGMA foreign_keys = ON;")

                                # Execute the SQL script
                                result = subprocess.run(
                                    ["sqlite3", db.DATABASE_PATH, ".read " + sql_script_path],
                                    capture_output=True,
                                    text=True
                                )

                                # Check if the command was successful
                                if result.returncode == 0:
                                    st.session_state.clear_db_state = "success"
                                else:
                                    st.session_state.clear_db_state = "error"
                                    st.session_state.clear_db_error = result.stderr
                            except Exception as e:
                                st.session_state.clear_db_state = "error"
                                st.session_state.clear_db_error = str(e)

                    elif st.session_state.clear_db_state == "success":
                        # Show success message
                        st.success("Database cleared successfully!")
                        if st.button("Done", key="clear_db_done"):
                            st.session_state.clear_db_state = "initial"

                    elif st.session_state.clear_db_state == "error":
                        # Show error message
                        st.error(f"Failed to clear database: {st.session_state.get('clear_db_error', 'Unknown error')}")
                        if st.button("Try Again", key="clear_db_retry"):
                            st.session_state.clear_db_state = "initial"

    return ai_provider, google_api_key, selected_model