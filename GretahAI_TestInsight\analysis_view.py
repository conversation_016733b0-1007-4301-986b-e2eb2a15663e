# Render the Analysis Suite UI for both Failure Investigation and Root Cause Analysis in a Streamlit application.
# Displays two main tabs:
# 1. Failure Investigation
#     - Allows filtering of test runs by class name.
#     - Presents run-level metrics (total, passed, failed, duration).
#     - Provides interactive filters (status, class, search, failures only).
#     - Offers multiple visualization tabs: duration analysis, status distribution, results grid, and a detailed inspector.
#     - Supports bulk generation of missing visual/AI summaries for all or failed tests in the current filter.
#     - Renders expandable details for individual test cases via a supplied callback.
# 2. Root Cause Analysis (RCA)
#     - Lists recent test runs and enables selection for RCA.
#     - Fetches failed test details, related logs, and any existing AI artifacts.
#     - Invokes an AI-driven RCA function to analyze failure patterns and generate structured insights.
#     - Presents RCA output in categorized tabs: Overview, Issues, Root Causes, Impact, Recommendations.
#     - Handles both JSON and plain-text outputs, displays errors, and offers a download button.
#     - Saves the RCA summary back to the SQLite database linked to the selected run.
#      st_obj (module): The Streamlit module or a Streamlit-like interface to render UI elements.
#      db_path (str): Filesystem path to the SQLite database containing test run metadata and artifacts.
#      render_test_case_details_func (callable): Function to render details for a single test case when expanding its entry.


import streamlit as st
import os
import json
import sqlite3
import pandas as pd
import plotly.express as px
import traceback

# Import helper functions (assuming helper.py and sql_lite_db_helpers.py are accessible)
from helper import (
    format_timestamp_display,
    generate_and_save_summary_if_missing,
    perform_rca,
    extract_json_dict
)
from sql_lite_db_helpers import (
    load_test_run_history,
    get_test_run_details,
    build_unified_test_entries_from_db,
    safe_parse_json,
    close_thread_local_connection,
    find_artifacts,
    find_log_files,
    find_run_id_by_timestamp,
    save_summary,
    get_classnames_for_run,
    get_all_unique_classnames
)

# Note: render_test_case_details is passed as an argument (render_test_case_details_func)

def render_log_summarization(st_obj, db_path, render_test_case_details_func):
    """
    Renders the Analysis Suite section (Failure Investigation and RCA).

    Args:
        st_obj: The Streamlit object (st).
        db_path (str): Path to the SQLite database.
        render_test_case_details_func (callable): Function to render individual test case details.
    """
    st = st_obj # Use the passed Streamlit object
    DATABASE_PATH = db_path
    render_test_case_details = render_test_case_details_func # Assign the passed function

    st.markdown('<h1 class="main-header">Gretah AI TestInsight Pro - Analysis Suite</h1>', unsafe_allow_html=True)

    st.markdown("""
    <div class="feature-panel">
    <strong>AI-Powered Analysis:</strong> Leverage AI for advanced defect detection, root cause analysis (RCA), and failure investigation using test artifacts.
    </div>
    """, unsafe_allow_html=True)

    hide_menu_style = """
    <style>
    #MainMenu, header, footer, .viewerBadge_container__1QSob {
        visibility: hidden;
        height: 0;
    }
    </style>
    """
    st.markdown(hide_menu_style, unsafe_allow_html=True)

    tab1, tab2, tab3 = st.tabs(["Performance Metrics", "Failure Investigation", "Root Cause Analysis"])

    with tab1:
        st.markdown("## Performance Metrics Dashboard")

        # Load test run history to get performance metrics
        run_history = load_test_run_history(DATABASE_PATH, limit=20)
        close_thread_local_connection()

        if not run_history:
            st.warning("📂 No test run data available. Execute tests first.")
        else:
            # Filter runs with performance metrics
            runs_with_metrics = [run for run in run_history if run.get('performance') and run['performance']]

            if not runs_with_metrics:
                st.warning("No performance metrics data available. Run tests with the performance_monitor fixture to collect metrics.")
            else:
                # Create dataframes for different metric types
                execution_df = []
                resource_df = []
                network_df = []

                for run in runs_with_metrics:
                    timestamp = run['timestamp']
                    display_time = format_timestamp_display(timestamp)
                    perf = run.get('performance', {})

                    # Execution time metrics
                    if any(k in perf for k in ['avg_execution_time', 'max_execution_time', 'avg_page_load_time', 'max_page_load_time']):
                        execution_df.append({
                            "Timestamp": timestamp,
                            "Run Time": display_time,
                            "Avg Execution Time (s)": perf.get("avg_execution_time", 0),
                            "Max Execution Time (s)": perf.get("max_execution_time", 0),
                            "Avg Page Load Time (ms)": perf.get("avg_page_load_time", 0),
                            "Max Page Load Time (ms)": perf.get("max_page_load_time", 0)
                        })

                    # Resource usage metrics
                    if any(k in perf for k in ['avg_memory_usage', 'max_memory_usage', 'avg_cpu_usage', 'max_cpu_usage']):
                        resource_df.append({
                            "Timestamp": timestamp,
                            "Run Time": display_time,
                            "Avg Memory Usage (MB)": perf.get("avg_memory_usage", 0),
                            "Max Memory Usage (MB)": perf.get("max_memory_usage", 0),
                            "Avg CPU Usage (%)": perf.get("avg_cpu_usage", 0),
                            "Max CPU Usage (%)": perf.get("max_cpu_usage", 0)
                        })

                    # Network metrics
                    if any(k in perf for k in ['total_network_requests', 'total_network_bytes']):
                        network_df.append({
                            "Timestamp": timestamp,
                            "Run Time": display_time,
                            "Total Network Requests": perf.get("total_network_requests", 0),
                            "Total Network Data (KB)": perf.get("total_network_bytes", 0)
                        })

                # Convert to pandas dataframes
                execution_df = pd.DataFrame(execution_df) if execution_df else None
                resource_df = pd.DataFrame(resource_df) if resource_df else None
                network_df = pd.DataFrame(network_df) if network_df else None

                # Sort by timestamp
                if execution_df is not None and not execution_df.empty:
                    execution_df = execution_df.sort_values("Timestamp")
                if resource_df is not None and not resource_df.empty:
                    resource_df = resource_df.sort_values("Timestamp")
                if network_df is not None and not network_df.empty:
                    network_df = network_df.sort_values("Timestamp")

                # Create metric tabs
                metric_tabs = st.tabs(["Execution Time", "Resource Usage", "Network Metrics"])

                with metric_tabs[0]:  # Execution Time
                    st.markdown("### Execution Time Metrics")
                    if execution_df is not None and not execution_df.empty:
                        # Create line chart for execution time metrics
                        fig_exec = px.line(
                            execution_df,
                            x="Run Time",
                            y=["Avg Execution Time (s)", "Max Execution Time (s)"],
                            title="Test Execution Time Trends",
                            labels={"value": "Time (seconds)", "variable": "Metric", "Run Time": "Test Run Time"},
                            markers=True,
                            color_discrete_map={
                                "Avg Execution Time (s)": "#1f77b4",
                                "Max Execution Time (s)": "#ff7f0e"
                            }
                        )
                        st.plotly_chart(fig_exec, use_container_width=True)

                        # Create line chart for page load time metrics
                        fig_page = px.line(
                            execution_df,
                            x="Run Time",
                            y=["Avg Page Load Time (ms)", "Max Page Load Time (ms)"],
                            title="Page Load Time Trends",
                            labels={"value": "Time (milliseconds)", "variable": "Metric", "Run Time": "Test Run Time"},
                            markers=True,
                            color_discrete_map={
                                "Avg Page Load Time (ms)": "#2ca02c",
                                "Max Page Load Time (ms)": "#d62728"
                            }
                        )
                        st.plotly_chart(fig_page, use_container_width=True)

                        # Show data table
                        st.dataframe(execution_df.drop("Timestamp", axis=1), use_container_width=True)
                    else:
                        st.info("No execution time metrics data available.")

                with metric_tabs[1]:  # Resource Usage
                    st.markdown("### Resource Usage Metrics")
                    if resource_df is not None and not resource_df.empty:
                        # Create line chart for memory usage metrics
                        fig_mem = px.line(
                            resource_df,
                            x="Run Time",
                            y=["Avg Memory Usage (MB)", "Max Memory Usage (MB)"],
                            title="Memory Usage Trends",
                            labels={"value": "Memory (MB)", "variable": "Metric", "Run Time": "Test Run Time"},
                            markers=True,
                            color_discrete_map={
                                "Avg Memory Usage (MB)": "#9467bd",
                                "Max Memory Usage (MB)": "#8c564b"
                            }
                        )
                        st.plotly_chart(fig_mem, use_container_width=True)

                        # Create line chart for CPU usage metrics
                        fig_cpu = px.line(
                            resource_df,
                            x="Run Time",
                            y=["Avg CPU Usage (%)", "Max CPU Usage (%)"],
                            title="CPU Usage Trends",
                            labels={"value": "CPU (%)", "variable": "Metric", "Run Time": "Test Run Time"},
                            markers=True,
                            color_discrete_map={
                                "Avg CPU Usage (%)": "#e377c2",
                                "Max CPU Usage (%)": "#7f7f7f"
                            }
                        )
                        st.plotly_chart(fig_cpu, use_container_width=True)

                        # Show data table
                        st.dataframe(resource_df.drop("Timestamp", axis=1), use_container_width=True)
                    else:
                        st.info("No resource usage metrics data available.")

                with metric_tabs[2]:  # Network Metrics
                    st.markdown("### Network Metrics")
                    if network_df is not None and not network_df.empty:
                        # Create line chart for network requests
                        fig_req = px.line(
                            network_df,
                            x="Run Time",
                            y="Total Network Requests",
                            title="Network Requests Trend",
                            labels={"Total Network Requests": "Number of Requests", "Run Time": "Test Run Time"},
                            markers=True,
                            color_discrete_sequence=["#17becf"]
                        )
                        st.plotly_chart(fig_req, use_container_width=True)

                        # Create line chart for network data
                        fig_data = px.line(
                            network_df,
                            x="Run Time",
                            y="Total Network Data (KB)",
                            title="Network Data Transfer Trend",
                            labels={"Total Network Data (KB)": "Data (KB)", "Run Time": "Test Run Time"},
                            markers=True,
                            color_discrete_sequence=["#bcbd22"]
                        )
                        st.plotly_chart(fig_data, use_container_width=True)

                        # Show data table
                        st.dataframe(network_df.drop("Timestamp", axis=1), use_container_width=True)
                    else:
                        st.info("No network metrics data available.")

    with tab3:
        st.markdown("## Root Cause Analysis (RCA)")

        rca_run_history = load_test_run_history(DATABASE_PATH, limit=50)
        close_thread_local_connection()

        selected_rca_run_ts = None

        if not rca_run_history:
            st.warning("No test runs found in the database. Execute tests via the 'Execution' module first.")
        else:
            st.markdown("### Select Test Run for RCA")
            rca_run_options = {
                f"{format_timestamp_display(run['timestamp'])} (Total: {run.get('total_tests', 0)}, Failed: {run.get('failed_tests', 0)})": run['timestamp']
                for run in rca_run_history
            }
            selected_rca_run_display = st.selectbox(
                "Choose a test run to analyze for root causes:",
                options=list(rca_run_options.keys()),
                key="selected_rca_run_display_key"
            )

            if selected_rca_run_display:
                selected_rca_run_ts = rca_run_options[selected_rca_run_display]
                st.markdown(f"**Analyzing Run:** `{format_timestamp_display(selected_rca_run_ts)}`")
            else:
                st.info("Please select a test run from the dropdown.")

        if selected_rca_run_ts:
            perform_rca_enabled = True
            if st.session_state['model_type'] == "Online" and not st.session_state.api_key:
                st.error("API Key required for Cloud RCA. Please configure it in the sidebar.")
                perform_rca_enabled = False

            if st.button("Perform RCA on Failed Tests", key="rca_button", disabled=not perform_rca_enabled):
                failed_test_details = []
                all_test_cases = get_test_run_details(DATABASE_PATH, selected_rca_run_ts)
                close_thread_local_connection()

                if not all_test_cases:
                    st.error(f"Could not retrieve test details for run {selected_rca_run_ts} from database.")
                else:
                    try:
                        for case in all_test_cases:
                            if case["result"] == "FAILED":
                                details = {
                                    "db_case_id": case["case_id"],
                                    "test_name": case["name"],
                                    "class_name": case["classname"],
                                    "failure_message": case.get("failure_message", ""),
                                    "log_snippet": None,
                                    "ai_analysis": None
                                }

                                log_artifact = find_log_files(DATABASE_PATH, case["case_id"])
                                close_thread_local_connection()
                                log_path = log_artifact['path'] if log_artifact else None

                                if log_path and os.path.exists(log_path):
                                    try:
                                        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                                            details["log_snippet"] = f.read(2000)
                                    except Exception as log_read_err:
                                        details["log_snippet"] = f"Error reading log: {log_read_err}"
                                else:
                                    details["log_snippet"] = "Log file not found or not linked."

                                analysis_artifacts = find_artifacts(DATABASE_PATH, case["case_id"], artifact_type='visual_analysis')
                                close_thread_local_connection()

                                latest_analysis_artifact = analysis_artifacts[0] if analysis_artifacts else None

                                if latest_analysis_artifact:
                                    summary_content = latest_analysis_artifact.get('summary_content')
                                    if summary_content:
                                        try:
                                            summary_data = safe_parse_json(summary_content)
                                            details['ai_analysis'] = summary_data if summary_data else {"raw_text": summary_content, "parse_error": True}
                                        except Exception as parse_err:
                                            details['ai_analysis'] = {"raw_text": summary_content, "parse_error": str(parse_err)}
                                    else:
                                        details['ai_analysis'] = {"error": "AI analysis artifact found but content is missing."}
                                else:
                                    details['ai_analysis'] = None

                                failed_test_details.append(details)

                    except sqlite3.Error as db_err:
                        st.error(f"Database error fetching failed test details: {db_err}")
                        close_thread_local_connection()
                        failed_test_details = []
                    except Exception as general_err:
                        st.error(f"An unexpected error occurred while preparing RCA data: {general_err}")
                        import traceback
                        st.expander("Show Error Details").code(traceback.format_exc())
                        close_thread_local_connection()
                        failed_test_details = []

                    if not failed_test_details:
                        st.warning(f"No failed tests found or details could not be retrieved for run {selected_rca_run_ts}.")
                    else:
                        st.info(f"Performing RCA based on {len(failed_test_details)} failed test(s) from run {selected_rca_run_ts}.")
                        with st.spinner("Performing RCA... This may take a while."):
                            google_timestamps = st.session_state.google_request_timestamps if st.session_state['model_type'] == "Online" else None
                            google_tokens = st.session_state.google_token_usage if st.session_state['model_type'] == "Online" else None

                            rca_result = perform_rca(
                                failed_test_details,
                                model_name=st.session_state.model,
                                api_key=st.session_state.api_key,
                                timestamps_list=google_timestamps,
                                token_usage_list=google_tokens
                            )

                        st.success("RCA Completed!")
                        st.markdown("### 📊 **Root Cause Analysis Results**")

                        if isinstance(rca_result, dict) and "error" in rca_result:
                            st.error(f"RCA Error: {rca_result['error']}")
                            if "raw_response" in rca_result: st.code(rca_result['raw_response'], language='text')

                        if isinstance(rca_result, str):
                            try:
                                # if it's valid JSON, turn it into a dict
                                rca_result = json.loads(rca_result)
                            except json.JSONDecodeError:
                                # leave it as a string if parsing fails
                                rca_result = extract_json_dict(rca_result)

                        # st.write("Raw result type:", type(rca_result))

                        if isinstance(rca_result, dict):
                            # Create tabs for different aspects of the RCA
                            rca_tabs = st.tabs(["Overview", "Issues", "Root Causes", "Impact", "Recommendations"])

                            with rca_tabs[0]:  # Overview tab
                                st.markdown("#### 📋 Summary Overview")

                                # Display key metrics
                                col1, col2, col3 = st.columns([1, 1, 1])
                                with col1:
                                    st.metric("Issues Identified",
                                             len(rca_result.get("identified_issues", [])),
                                             help="Number of distinct issues found in test failures")
                                with col2:
                                    st.metric("Root Causes",
                                             len(rca_result.get("root_causes", [])),
                                             help="Number of underlying causes identified")
                                with col3:
                                    st.metric("Recommended Actions",
                                             len(rca_result.get("recommended_actions", [])),
                                             help="Number of suggested steps to resolve issues")

                                # Show condensed version of all sections
                                st.markdown("### Key Findings")
                                if rca_result.get("identified_issues"):
                                    with st.container():
                                        st.markdown("##### ⚠️ Top Issues")
                                        for issue in rca_result['identified_issues'][:3]:
                                            st.markdown(f"• {issue}")
                                        if len(rca_result['identified_issues']) > 3:
                                            with st.expander("Show all issues"):
                                                for issue in rca_result['identified_issues'][3:]:
                                                    st.markdown(f"• {issue}")

                                if rca_result.get("root_causes"):
                                    with st.container():
                                        st.markdown("##### 🔍 Primary Causes")
                                        for cause in rca_result['root_causes'][:3]:
                                            st.markdown(f"• {cause}")
                                        if len(rca_result['root_causes']) > 3:
                                            with st.expander("Show all causes"):
                                                for cause in rca_result['root_causes'][3:]:
                                                    st.markdown(f"• {cause}")

                                if rca_result.get("recommended_actions"):
                                    with st.container():
                                        st.markdown("##### 🛠️ Critical Actions")
                                        for action in rca_result['recommended_actions'][:3]:
                                            st.markdown(f"• {action}")
                                        if len(rca_result['recommended_actions']) > 3:
                                            with st.expander("Show all actions"):
                                                for action in rca_result['recommended_actions'][3:]:
                                                    st.markdown(f"• {action}")
                            with rca_tabs[1]:  # Issues tab
                                st.markdown("#### ⚠️ Identified Issues")
                                issues = rca_result.get("identified_issues", [])
                                if issues:
                                    for i, issue in enumerate(issues):
                                        issue_parts = issue.split(" in ", 1)
                                        if len(issue_parts) > 1:
                                            exception_name, test_name = issue_parts
                                            st.markdown(f"""
                                            <div style="margin-bottom: 10px; padding: 10px; border-left: 4px solid #f63366; background-color: rgba(246, 51, 102, 0.1);">
                                                <span style="font-weight: bold; color: #f63366;">{exception_name}</span>
                                                <br/>in test: <code>{test_name}</code>
                                            </div>
                                            """, unsafe_allow_html=True)
                                        else:
                                            st.markdown(f"- **Issue {i+1}**: {issue}")
                                else:
                                    st.info("No specific issues were identified.")

                            with rca_tabs[2]:  # Root Causes tab
                                st.markdown("#### 🔍 Root Causes")
                                causes = rca_result.get("root_causes", [])
                                if causes:
                                    for i, cause in enumerate(causes):
                                        with st.container():
                                            st.markdown(f"""
                                            <div style="margin-bottom: 15px; padding: 15px; border-radius: 5px; background-color: rgba(38, 39, 48, 0.1);">
                                                <span style="font-weight: bold; font-size: 1.1em;">Cause {i+1}:</span> {cause}
                                            </div>
                                            """, unsafe_allow_html=True)
                                else:
                                    st.info("No root causes were identified.")

                            with rca_tabs[3]:  # Impact tab
                                st.markdown("#### 📉 Potential Impact")
                                impacts = rca_result.get("potential_impact", [])
                                if impacts:
                                    impact_icons = ["🔴", "🟠", "🟡"]  # Different severity levels
                                    for i, impact in enumerate(impacts):
                                        icon = impact_icons[i % len(impact_icons)]
                                        st.markdown(f"{icon} {impact}")
                                else:
                                    st.info("No potential impacts were identified.")

                            with rca_tabs[4]:  # Recommendations tab
                                st.markdown("#### 🛠️ Recommended Actions")
                                actions = rca_result.get("recommended_actions", [])
                                if actions:
                                    for i, action in enumerate(actions):
                                        with st.expander(f"Action {i+1}", expanded=True if i < 2 else False):
                                            st.markdown(action)
                                else:
                                    st.info("No recommended actions were provided.")

                        elif isinstance(rca_result, str):
                            # Handle string format (fallback)
                            st.info("RCA results in text format:")
                            st.markdown(rca_result)
                        else:
                            # Fallback for any other format
                            st.warning("RCA results in unexpected format:")
                            st.json(rca_result)

                        try:
                            run_id = find_run_id_by_timestamp(DATABASE_PATH, selected_rca_run_ts)
                            close_thread_local_connection()

                            if run_id:
                                rca_content_str = json.dumps(rca_result) if isinstance(rca_result, dict) else str(rca_result)
                                save_summary(DATABASE_PATH, None, st.session_state.model, 'rca', rca_content_str, run_id=run_id)
                                close_thread_local_connection()
                                st.info("RCA result saved to database, associated with the run.")

                                rca_filename = f"rca_analysis_{selected_rca_run_ts}.txt"
                                mime_type = "application/json" if isinstance(rca_result, dict) else "text/plain"
                                st.download_button(
                                    "Download RCA Report",
                                    data=rca_content_str,
                                    file_name=rca_filename,
                                    mime=mime_type
                                )
                            else:
                                st.error(f"Could not find run ID for timestamp {selected_rca_run_ts} to save RCA.")
                                close_thread_local_connection()

                        except sqlite3.Error as db_err:
                            st.error(f"Database error saving RCA result: {db_err}")
                            close_thread_local_connection()
                        except Exception as save_err:
                            st.error(f"Error saving or creating download button for RCA report: {save_err}")
                            close_thread_local_connection()

    with tab2:  # Failure Investigation
        st.markdown("## Failure Investigation")
        st.markdown("### Select Test Run")

        # --- Add Top-Level Class Filter ---
        all_db_classnames = ["All Runs"] + get_all_unique_classnames(DATABASE_PATH)
        close_thread_local_connection()
        selected_run_class_filter = st.selectbox(
            "Filter runs by class contained:",
            options=all_db_classnames,
            index=0,
            key="select_run_by_class_filter"
        )
        # --- End Top-Level Class Filter ---

        run_history_full = load_test_run_history(DATABASE_PATH, limit=100)  # Load more for filtering
        close_thread_local_connection()

        # --- Filter Run History Based on Class Selection ---
        filtered_run_history = []
        if selected_run_class_filter == "All Runs":
            filtered_run_history = run_history_full
        else:
            with st.spinner(f"Finding runs containing class '{selected_run_class_filter}'..."):
                for run in run_history_full:
                    run_ts = run['timestamp']
                    classes_in_run = get_classnames_for_run(DATABASE_PATH, run_ts)
                    close_thread_local_connection()
                    if selected_run_class_filter in classes_in_run:
                        filtered_run_history.append(run)
            if not filtered_run_history:
                st.warning(f"No recent runs found containing the class '{selected_run_class_filter}'. Showing all runs instead.")
                filtered_run_history = run_history_full  # Fallback to showing all if filter yields nothing

        # Limit the final list after filtering
        run_history = filtered_run_history[:50]
        # --- End Filter Run History ---

        if not run_history:
            st.warning("📂 No test run data available. Execute tests first.")
            return  # Use return instead of st.stop()

        # --- Populate Run Dropdown using filtered history ---
        run_options = {f"{format_timestamp_display(run['timestamp'])} (Total: {run.get('total_tests', 0)}, Failed: {run.get('failed_tests', 0)})": run['timestamp']
                       for run in run_history}
        selected_run_display = st.selectbox(
            "Select run for analysis:",
            options=list(run_options.keys()),
            key="selected_run_display_key"  # Keep the key consistent
        )
        # --- End Populate Run Dropdown ---

        selected_run_timestamp = None
        if selected_run_display:
            selected_run_timestamp = run_options[selected_run_display]
            # Store selected timestamp in session state if needed elsewhere, or just use the variable
            st.session_state.selected_run_timestamp = selected_run_timestamp
        else:
            # Clear session state if no run is selected (e.g., if filtered list becomes empty)
            if "selected_run_timestamp" in st.session_state:
                del st.session_state.selected_run_timestamp

        if selected_run_timestamp:
            # --- The rest of the Failure Investigation logic remains largely the same ---
            # It operates on the selected_run_timestamp
            with st.spinner(f"Loading run data: {format_timestamp_display(selected_run_timestamp)}..."):
                test_entries = build_unified_test_entries_from_db(DATABASE_PATH, selected_run_timestamp)
            close_thread_local_connection()

            if not test_entries:
                st.error("⚠️ Failed to load test data for the selected run.")
                return  # Use return

            df = pd.DataFrame(test_entries)

            total_tests = len(df)
            passed_tests = len(df[df["result"] == "PASSED"])
            failed_tests = len(df[df["result"] == "FAILED"])
            total_duration_df = df["duration"].sum() if 'duration' in df.columns and not df["duration"].isnull().all() else 0

            run_summary = next((run for run in run_history if run['timestamp'] == selected_run_timestamp), None)

            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Total Tests", total_tests)
            with col2:
                st.metric("Passed", passed_tests,
                          delta=f"{round(passed_tests/total_tests*100)}%" if total_tests > 0 else "0%")
            with col3:
                st.metric("Failed", failed_tests,
                          delta=f"{round(failed_tests/total_tests*100)}%" if total_tests > 0 else "0%",
                          delta_color="inverse")
            with col4:
                display_duration = run_summary['duration'] if run_summary and run_summary.get('duration') is not None else total_duration_df
                st.metric("Total Duration", f"{display_duration:.2f}s")

            with st.sidebar:
                st.markdown("### Filter Options (Investigation)")
                available_classes = ["All Classes"] + sorted(df["class_name"].dropna().unique().tolist())
                selected_class_filter = st.selectbox(
                    "Filter by Class (within selected run)",  # Clarify scope
                    options=available_classes,
                    index=0,
                    key="investigation_class_filter"  # Keep key distinct from top-level filter
                )

                status_options = df["result"].dropna().unique().tolist()
                status_filter = st.multiselect(
                    "Filter by Status",
                    options=status_options,
                    default=status_options,
                    key="status_filter"
                )
                search_term = st.text_input("Search Tests", key="search_term",
                                            placeholder="Filter by name or class")
                only_failed = st.checkbox("Show Failures Only", key="only_failed",
                                          help="Isolate failing test cases")

            filtered_df = df.copy()
            if only_failed:
                if "result" in filtered_df.columns:
                    filtered_df = filtered_df[filtered_df["result"] == "FAILED"]
                else:
                    st.warning("Result column not found for filtering failures.")
                    filtered_df = pd.DataFrame()
            elif status_filter:
                if "result" in filtered_df.columns:
                    filtered_df = filtered_df[filtered_df["result"].isin(status_filter)]
                else:
                    st.warning("Result column not found for filtering by status.")
                    filtered_df = pd.DataFrame()

            if selected_class_filter != "All Classes":
                if "class_name" in filtered_df.columns:
                    filtered_df = filtered_df[filtered_df["class_name"] == selected_class_filter]
                else:
                    st.warning("Class name column not found for filtering.")
                    filtered_df = pd.DataFrame()

            if search_term:
                name_col_exists = "test_name" in filtered_df.columns
                class_col_exists = "class_name" in filtered_df.columns
                if name_col_exists and class_col_exists:
                    filtered_df = filtered_df[
                        filtered_df["test_name"].str.contains(search_term, case=False, na=False) |
                        filtered_df["class_name"].str.contains(search_term, case=False, na=False)
                    ]
                elif name_col_exists:
                    filtered_df = filtered_df[filtered_df["test_name"].str.contains(search_term, case=False, na=False)]
                elif class_col_exists:
                    filtered_df = filtered_df[filtered_df["class_name"].str.contains(search_term, case=False, na=False)]
                else:
                    st.warning("Neither test name nor class name columns found for searching.")
                    filtered_df = pd.DataFrame()

            st.markdown("### Run Analysis")
            chart_tabs = st.tabs(["Duration Analysis", "Performance Metrics", "Status Distribution", "Results Grid", "Detailed Inspector"])

            with chart_tabs[0]:
                if not filtered_df.empty and 'duration' in filtered_df.columns and filtered_df['duration'].notna().any():
                    duration_df = filtered_df.dropna(subset=['duration']).sort_values("duration", ascending=False).head(20)
                    if not duration_df.empty:
                        fig_duration = px.bar(
                            duration_df,
                            x="test_name",
                            y="duration",
                            color="result",
                            labels={"test_name": "Test Name", "duration": "Duration (s)"},
                            title=f"Test Execution Time (Top {len(duration_df)} Longest Tests)",
                            color_discrete_map={"PASSED": "#26A69A", "FAILED": "#FF5252", "SKIPPED": "#FFC107", "ERROR": "#FF9800"}
                        )
                        fig_duration.update_layout(xaxis_tickangle=-45)
                        st.plotly_chart(fig_duration, use_container_width=True)
                    else:
                        st.info("No tests with valid duration data found after filtering.")
                else:
                    st.info("No duration data to display (column missing, empty, or filters applied).")

            with chart_tabs[1]:  # Performance Metrics
                st.markdown("#### Test Case Performance Metrics")

                # Check if we have performance metrics in the dataframe
                has_performance_metrics = False
                performance_metrics_df = pd.DataFrame()

                # Extract performance metrics into separate columns if they exist
                if 'performance_metrics' in filtered_df.columns:
                    # Create a copy of the dataframe to work with
                    metrics_df = filtered_df.copy()

                    # Extract key metrics into separate columns
                    metrics_to_extract = ['execution_time', 'page_load_time', 'memory_usage', 'cpu_usage', 'network_requests', 'network_bytes']
                    extracted_metrics = []

                    for metric in metrics_to_extract:
                        # Check if any row has this metric
                        for idx, row in metrics_df.iterrows():
                            perf_metrics = row.get('performance_metrics', {})
                            if isinstance(perf_metrics, dict) and metric in perf_metrics:
                                # Extract this metric for all rows
                                metrics_df[metric] = metrics_df['performance_metrics'].apply(
                                    lambda x: x.get(metric) if isinstance(x, dict) else None)
                                extracted_metrics.append(metric)
                                has_performance_metrics = True
                                break

                    if has_performance_metrics:
                        # Select only the columns we need
                        cols_to_keep = ['test_name', 'class_name', 'result'] + extracted_metrics
                        performance_metrics_df = metrics_df[cols_to_keep].copy()

                if has_performance_metrics and not performance_metrics_df.empty:
                    # Create tabs for different metric types
                    perf_metric_tabs = st.tabs(["Execution Time", "Page Load Time", "Resource Usage", "Network Metrics"])

                    with perf_metric_tabs[0]:  # Execution Time
                        if 'execution_time' in performance_metrics_df.columns and performance_metrics_df['execution_time'].notna().any():
                            # Sort by execution time and get top 20
                            exec_time_df = performance_metrics_df.dropna(subset=['execution_time']).sort_values('execution_time', ascending=False).head(20)

                            if not exec_time_df.empty:
                                fig_exec = px.bar(
                                    exec_time_df,
                                    x="test_name",
                                    y="execution_time",
                                    color="result",
                                    labels={"test_name": "Test Name", "execution_time": "Execution Time (s)"},
                                    title=f"Test Execution Time (Top {len(exec_time_df)} Tests)",
                                    color_discrete_map={"PASSED": "#26A69A", "FAILED": "#FF5252", "SKIPPED": "#FFC107", "ERROR": "#FF9800"}
                                )
                                fig_exec.update_layout(xaxis_tickangle=-45)
                                st.plotly_chart(fig_exec, use_container_width=True)
                            else:
                                st.info("No tests with valid execution time data found after filtering.")
                        else:
                            st.info("No execution time data available for the selected tests.")

                    with perf_metric_tabs[1]:  # Page Load Time
                        if 'page_load_time' in performance_metrics_df.columns and performance_metrics_df['page_load_time'].notna().any():
                            # Sort by page load time and get top 20
                            page_load_df = performance_metrics_df.dropna(subset=['page_load_time']).sort_values('page_load_time', ascending=False).head(20)

                            if not page_load_df.empty:
                                fig_page = px.bar(
                                    page_load_df,
                                    x="test_name",
                                    y="page_load_time",
                                    color="result",
                                    labels={"test_name": "Test Name", "page_load_time": "Page Load Time (ms)"},
                                    title=f"Page Load Time (Top {len(page_load_df)} Tests)",
                                    color_discrete_map={"PASSED": "#26A69A", "FAILED": "#FF5252", "SKIPPED": "#FFC107", "ERROR": "#FF9800"}
                                )
                                fig_page.update_layout(xaxis_tickangle=-45)
                                st.plotly_chart(fig_page, use_container_width=True)
                            else:
                                st.info("No tests with valid page load time data found after filtering.")
                        else:
                            st.info("No page load time data available for the selected tests.")

                    with perf_metric_tabs[2]:  # Resource Usage
                        # Memory Usage
                        if 'memory_usage' in performance_metrics_df.columns and performance_metrics_df['memory_usage'].notna().any():
                            # Sort by memory usage and get top 20
                            memory_df = performance_metrics_df.dropna(subset=['memory_usage']).sort_values('memory_usage', ascending=False).head(20)

                            if not memory_df.empty:
                                fig_mem = px.bar(
                                    memory_df,
                                    x="test_name",
                                    y="memory_usage",
                                    color="result",
                                    labels={"test_name": "Test Name", "memory_usage": "Memory Usage (MB)"},
                                    title=f"Memory Usage (Top {len(memory_df)} Tests)",
                                    color_discrete_map={"PASSED": "#26A69A", "FAILED": "#FF5252", "SKIPPED": "#FFC107", "ERROR": "#FF9800"}
                                )
                                fig_mem.update_layout(xaxis_tickangle=-45)
                                st.plotly_chart(fig_mem, use_container_width=True)
                            else:
                                st.info("No tests with valid memory usage data found after filtering.")
                        else:
                            st.info("No memory usage data available for the selected tests.")

                        # CPU Usage
                        if 'cpu_usage' in performance_metrics_df.columns and performance_metrics_df['cpu_usage'].notna().any():
                            # Sort by CPU usage and get top 20
                            cpu_df = performance_metrics_df.dropna(subset=['cpu_usage']).sort_values('cpu_usage', ascending=False).head(20)

                            if not cpu_df.empty:
                                fig_cpu = px.bar(
                                    cpu_df,
                                    x="test_name",
                                    y="cpu_usage",
                                    color="result",
                                    labels={"test_name": "Test Name", "cpu_usage": "CPU Usage (%)"},
                                    title=f"CPU Usage (Top {len(cpu_df)} Tests)",
                                    color_discrete_map={"PASSED": "#26A69A", "FAILED": "#FF5252", "SKIPPED": "#FFC107", "ERROR": "#FF9800"}
                                )
                                fig_cpu.update_layout(xaxis_tickangle=-45)
                                st.plotly_chart(fig_cpu, use_container_width=True)
                            else:
                                st.info("No tests with valid CPU usage data found after filtering.")
                        else:
                            st.info("No CPU usage data available for the selected tests.")

                    with perf_metric_tabs[3]:  # Network Metrics
                        # Network Requests
                        if 'network_requests' in performance_metrics_df.columns and performance_metrics_df['network_requests'].notna().any():
                            # Sort by network requests and get top 20
                            network_req_df = performance_metrics_df.dropna(subset=['network_requests']).sort_values('network_requests', ascending=False).head(20)

                            if not network_req_df.empty:
                                fig_req = px.bar(
                                    network_req_df,
                                    x="test_name",
                                    y="network_requests",
                                    color="result",
                                    labels={"test_name": "Test Name", "network_requests": "Network Requests"},
                                    title=f"Network Requests (Top {len(network_req_df)} Tests)",
                                    color_discrete_map={"PASSED": "#26A69A", "FAILED": "#FF5252", "SKIPPED": "#FFC107", "ERROR": "#FF9800"}
                                )
                                fig_req.update_layout(xaxis_tickangle=-45)
                                st.plotly_chart(fig_req, use_container_width=True)
                            else:
                                st.info("No tests with valid network requests data found after filtering.")
                        else:
                            st.info("No network requests data available for the selected tests.")

                        # Network Bytes
                        if 'network_bytes' in performance_metrics_df.columns and performance_metrics_df['network_bytes'].notna().any():
                            # Sort by network bytes and get top 20
                            network_bytes_df = performance_metrics_df.dropna(subset=['network_bytes']).sort_values('network_bytes', ascending=False).head(20)

                            if not network_bytes_df.empty:
                                fig_bytes = px.bar(
                                    network_bytes_df,
                                    x="test_name",
                                    y="network_bytes",
                                    color="result",
                                    labels={"test_name": "Test Name", "network_bytes": "Network Data (KB)"},
                                    title=f"Network Data Transfer (Top {len(network_bytes_df)} Tests)",
                                    color_discrete_map={"PASSED": "#26A69A", "FAILED": "#FF5252", "SKIPPED": "#FFC107", "ERROR": "#FF9800"}
                                )
                                fig_bytes.update_layout(xaxis_tickangle=-45)
                                st.plotly_chart(fig_bytes, use_container_width=True)
                            else:
                                st.info("No tests with valid network data transfer data found after filtering.")
                        else:
                            st.info("No network data transfer data available for the selected tests.")
                else:
                    st.info("No performance metrics data available for the selected tests. Run tests with the performance_monitor fixture to collect metrics.")

            with chart_tabs[2]:  # Status Distribution
                if not filtered_df.empty and 'result' in filtered_df.columns:
                    status_counts = filtered_df["result"].value_counts().reset_index()
                    status_counts.columns = ["Status", "Count"]

                    fig_pie = px.pie(
                        status_counts,
                        values="Count",
                        names="Status",
                        title="Test Results Distribution (Filtered)",
                        color="Status",
                        color_discrete_map={"PASSED": "#26A69A", "FAILED": "#FF5252", "SKIPPED": "#FFC107", "ERROR": "#FF9800"}
                    )
                    st.plotly_chart(fig_pie, use_container_width=True)

                    st.dataframe(status_counts)
                else:
                    st.info("No status data to display (column missing, empty, or filters applied).")

            with chart_tabs[3]:  # Results Grid
                if not filtered_df.empty:
                    display_cols = ["test_name", "class_name", "duration", "result"]

                    # Add performance metrics columns if they exist
                    has_performance_metrics = False
                    performance_cols = []

                    # Check if performance metrics exist in the dataframe
                    if 'performance_metrics' in filtered_df.columns:
                        # Extract performance metrics into separate columns
                        for idx, row in filtered_df.iterrows():
                            metrics = row.get('performance_metrics', {})
                            if isinstance(metrics, dict) and metrics:
                                has_performance_metrics = True
                                # Extract key metrics into separate columns
                                if 'execution_time' in metrics and 'execution_time' not in filtered_df.columns:
                                    filtered_df['execution_time'] = filtered_df['performance_metrics'].apply(
                                        lambda x: x.get('execution_time') if isinstance(x, dict) else None)
                                    performance_cols.append('execution_time')

                                if 'page_load_time' in metrics and 'page_load_time' not in filtered_df.columns:
                                    filtered_df['page_load_time'] = filtered_df['performance_metrics'].apply(
                                        lambda x: x.get('page_load_time') if isinstance(x, dict) else None)
                                    performance_cols.append('page_load_time')

                                if 'memory_usage' in metrics and 'memory_usage' not in filtered_df.columns:
                                    filtered_df['memory_usage'] = filtered_df['performance_metrics'].apply(
                                        lambda x: x.get('memory_usage') if isinstance(x, dict) else None)
                                    performance_cols.append('memory_usage')

                                if 'cpu_usage' in metrics and 'cpu_usage' not in filtered_df.columns:
                                    filtered_df['cpu_usage'] = filtered_df['performance_metrics'].apply(
                                        lambda x: x.get('cpu_usage') if isinstance(x, dict) else None)
                                    performance_cols.append('cpu_usage')

                                break  # We only need to check one row with metrics

                    # Add performance columns to display columns if they exist
                    if has_performance_metrics:
                        display_cols.extend(performance_cols)

                    existing_display_cols = [col for col in display_cols if col in filtered_df.columns]
                    if existing_display_cols:
                        display_df = filtered_df[existing_display_cols].copy()
                        sort_cols = []
                        if "result" in display_df.columns: sort_cols.append("result")
                        if "duration" in display_df.columns: sort_cols.append("duration")
                        ascending_order = [True, False]

                        if sort_cols:
                            if "duration" in sort_cols: display_df['duration'] = display_df['duration'].fillna(-1)
                            display_df = display_df.sort_values(sort_cols, ascending=ascending_order[:len(sort_cols)])
                            if "duration" in sort_cols: display_df['duration'] = display_df['duration'].replace(-1, pd.NA)

                        column_config = {
                            "test_name": st.column_config.TextColumn("Test Name", help="Name of the test case"),
                            "class_name": st.column_config.TextColumn("Test Class", help="Class containing the test case"),
                            "result": st.column_config.TextColumn("Result", width="small", help="Test outcome (PASSED/FAILED/...)")
                        }
                        if "duration" in display_df.columns:
                            column_config["duration"] = st.column_config.NumberColumn("Duration (s)", format="%.3f", help="Time taken for the test")

                        # Add performance metrics column config
                        if "execution_time" in display_df.columns:
                            column_config["execution_time"] = st.column_config.NumberColumn("Exec Time (s)", format="%.3f", help="Execution time of the test")
                        if "page_load_time" in display_df.columns:
                            column_config["page_load_time"] = st.column_config.NumberColumn("Page Load (ms)", format="%.0f", help="Page load time in milliseconds")
                        if "memory_usage" in display_df.columns:
                            column_config["memory_usage"] = st.column_config.NumberColumn("Memory (MB)", format="%.2f", help="Memory usage in MB")
                        if "cpu_usage" in display_df.columns:
                            column_config["cpu_usage"] = st.column_config.NumberColumn("CPU (%)", format="%.2f", help="CPU usage percentage")

                        st.dataframe(
                            display_df,
                            column_config=column_config,
                            hide_index=True,
                            use_container_width=True
                        )
                    else:
                        st.info("No columns suitable for display found in the filtered data.")
                else:
                    st.info("No detailed results to display after applying filters.")

            with chart_tabs[4]:  # Detailed Inspector
                st.markdown("#### Detailed Test Inspector")

                # --- Replace single button with two buttons ---
                if not filtered_df.empty:
                    gen_bulk_filt_enabled = True
                    if st.session_state['model_type'] == "Online" and not st.session_state.api_key:
                        gen_bulk_filt_enabled = False
                        st.caption("API Key needed for Cloud analysis.")

                    col_gen_filt_all, col_gen_filt_failed = st.columns(2)

                    # Button for all filtered cases
                    with col_gen_filt_all:
                        if st.button("Generate Missing Summaries (All Filtered)", key="bulk_gen_filtered_all", disabled=not gen_bulk_filt_enabled):
                            progress_bar_filt = st.progress(0)
                            status_placeholder_filt = st.empty()
                            results_summary_filt = {'generated': 0, 'skipped_exists': 0, 'skipped_missing_artifacts': 0, 'error': 0}
                            cases_to_process = filtered_df.to_dict('records')  # Process all in filtered_df
                            total_to_process_filt = len(cases_to_process)

                            if total_to_process_filt == 0:
                                st.info("No tests match the current filter criteria.")
                            else:
                                for i, case_row in enumerate(cases_to_process):
                                    case_id = case_row.get('db_case_id')
                                    if not case_id: continue

                                    # Check based on loaded data first (optimization)
                                    if case_row.get('visual_analysis'):
                                        results_summary_filt['skipped_exists'] += 1
                                    else:
                                        status_placeholder_filt.text(f"Processing case {i+1}/{total_to_process_filt}: {case_row.get('test_name')}...")
                                        status = generate_and_save_summary_if_missing(
                                            case_id, DATABASE_PATH, st.session_state.model, st.session_state.api_key
                                        )
                                        if status == 'generated':
                                            results_summary_filt['generated'] += 1
                                        elif status == 'skipped_exists':
                                            results_summary_filt['skipped_exists'] += 1
                                        elif status == 'skipped_missing_artifacts':
                                            results_summary_filt['skipped_missing_artifacts'] += 1
                                        else:
                                            results_summary_filt['error'] += 1
                                    progress_bar_filt.progress((i + 1) / total_to_process_filt)

                                status_placeholder_filt.empty()
                                progress_bar_filt.empty()
                                st.success(
                                    f"Bulk generation complete: "
                                    f"{results_summary_filt['generated']} generated, "
                                    f"{results_summary_filt['skipped_exists']} skipped (already exist), "
                                    f"{results_summary_filt['skipped_missing_artifacts']} skipped (missing artifacts), "
                                    f"{results_summary_filt['error']} errors."
                                )
                                st.rerun()  # Rerun reloads data via build_unified_test_entries_from_db

                    # Button for failed filtered cases only
                    with col_gen_filt_failed:
                        failed_in_filter = filtered_df[filtered_df['result'] == 'FAILED']
                        if st.button("Generate Missing Summaries (Filtered Failures Only)", key="bulk_gen_filtered_failed", disabled=not gen_bulk_filt_enabled or failed_in_filter.empty):
                            progress_bar_filt = st.progress(0)
                            status_placeholder_filt = st.empty()
                            results_summary_filt = {'generated': 0, 'skipped_exists': 0, 'skipped_missing_artifacts': 0, 'error': 0}
                            cases_to_process = failed_in_filter.to_dict('records')  # Process only failed in filtered_df
                            total_to_process_filt = len(cases_to_process)

                            if total_to_process_filt == 0:
                                st.info("No failed tests match the current filter criteria.")
                            else:
                                for i, case_row in enumerate(cases_to_process):
                                    case_id = case_row.get('db_case_id')
                                    if not case_id: continue

                                    # Check based on loaded data first (optimization)
                                    if case_row.get('visual_analysis'):
                                        results_summary_filt['skipped_exists'] += 1
                                    else:
                                        status_placeholder_filt.text(f"Processing case {i+1}/{total_to_process_filt}: {case_row.get('test_name')}...")
                                        status = generate_and_save_summary_if_missing(
                                            case_id, DATABASE_PATH, st.session_state.model, st.session_state.api_key
                                        )
                                        if status == 'generated':
                                            results_summary_filt['generated'] += 1
                                        elif status == 'skipped_exists':
                                            results_summary_filt['skipped_exists'] += 1
                                        elif status == 'skipped_missing_artifacts':
                                            results_summary_filt['skipped_missing_artifacts'] += 1
                                        else:
                                            results_summary_filt['error'] += 1
                                    progress_bar_filt.progress((i + 1) / total_to_process_filt)

                                status_placeholder_filt.empty()
                                progress_bar_filt.empty()
                                st.success(
                                    f"Bulk generation complete: "
                                    f"{results_summary_filt['generated']} generated, "
                                    f"{results_summary_filt['skipped_exists']} skipped (already exist), "
                                    f"{results_summary_filt['skipped_missing_artifacts']} skipped (missing artifacts), "
                                    f"{results_summary_filt['error']} errors."
                                )
                                st.rerun()  # Rerun reloads data via build_unified_test_entries_from_db
                # --- End button replacement ---

                if not filtered_df.empty:
                    # --- Loop to render individual test case expanders ---
                    for index, entry in filtered_df.iterrows():
                        case_key_prefix = f"case_{entry.get('db_case_id', 'no_id')}_{index}"
                        status_emoji = "✅" if entry.get("result") == "PASSED" else "❌"
                        duration_str = f"{entry['duration']:.3f}s" if pd.notna(entry.get('duration')) else "N/A"
                        expander_title = f"{status_emoji} {entry.get('test_name', 'Unknown Test')} ({duration_str}) - {entry.get('result', 'Unknown')}"

                        with st.expander(expander_title):
                            test_case_data_for_render = {
                                "db_case_id": entry.get('db_case_id'),
                                "test_name": entry.get('test_name'),
                                "class_name": entry.get('class_name'),
                                "result": entry.get('result'),
                                "duration": entry.get('duration'),
                                "failure_message": entry.get('failure_message'),
                                "screenshot_path": entry.get('screenshot_path'),
                                "page_source_path": entry.get('page_source_path'),
                                "log_path": entry.get('log_path'),
                                "visual_analysis": entry.get('visual_analysis'),
                                "performance_metrics": entry.get('performance_metrics')
                            }
                            # Pass a flag indicating this call is nested within an expander
                            render_test_case_details(test_case_data_for_render, case_key_prefix, is_nested_in_expander=True)
                    # --- End loop ---
                else:
                    st.info("No tests match the current filter criteria.")
        else:
            st.info("Select a test run to load analysis data." + (f" (Filtered by class: {selected_run_class_filter})" if selected_run_class_filter != "All Runs" else ""))

        st.markdown("---")
