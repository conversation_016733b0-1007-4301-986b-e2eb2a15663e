# GretahAI ScriptWeaver Documentation

Welcome to the documentation for GretahAI ScriptWeaver. This directory contains technical documentation organized by topic for easy navigation and maintenance.

**Development Status**: This software is under active development and is suitable for development and testing environments. Enterprise deployment requires additional configuration, testing, and validation.

## 📚 Documentation Index

### Core Documentation

| Document | Description | Audience |
|----------|-------------|----------|
| [**Main README**](../README.md) | Project overview, setup, and usage guide | All users |
| [**CHANGELOG**](../CHANGELOG.md) | Version history and release notes | All users |

### User Documentation

| Document | Description | Audience |
|----------|-------------|----------|
| [**User Guide**](USER_GUIDE.md) | Complete step-by-step workflow guide with examples | End Users |

### Technical Documentation

| Document | Description | Audience |
|----------|-------------|----------|
| [**Developer Guide**](DEVELOPER_GUIDE.md) | Architecture, system design, and development patterns | Developers |
| [**API Documentation**](API.md) | Complete API reference and function signatures | Developers |
| [**Development Guidelines**](DEVELOPMENT.md) | Coding standards, patterns, and best practices | Developers |
| [**Contributing Guide**](CONTRIBUTING.md) | How to contribute to the project | Contributors |

### Visual Documentation

| Document | Description | Audience |
|----------|-------------|----------|
| [**Visual Diagrams**](VISUAL_DIAGRAMS.md) | Comprehensive index of all Mermaid diagrams | All Users |

## 🚀 Quick Start

### For Users
1. Start with the [Main README](../README.md) for installation and setup
2. Follow the [User Guide](USER_GUIDE.md) for complete workflow instructions
3. Check the [CHANGELOG](../CHANGELOG.md) for latest features and updates

### For Developers
1. Review the [Developer Guide](DEVELOPER_GUIDE.md) for architecture and patterns
2. Check the [API Documentation](API.md) for function references
3. Follow the [Development Guidelines](DEVELOPMENT.md) for coding standards
4. Use the [Contributing Guide](CONTRIBUTING.md) for contribution workflow

### For Contributors
1. Begin with the [Contributing Guide](CONTRIBUTING.md)
2. Understand the [Developer Guide](DEVELOPER_GUIDE.md) architecture
3. Follow the [Development Guidelines](DEVELOPMENT.md) patterns
4. Reference the [API Documentation](API.md) for implementation details

## 📋 Documentation Standards

### Writing Guidelines

- **Clear and Concise**: Use simple, direct language
- **Code Examples**: Include practical examples for all concepts
- **Cross-References**: Link between related documentation sections
- **Up-to-Date**: Keep documentation synchronized with code changes

### Structure Principles

- **Modular Organization**: Each document covers a specific topic
- **Logical Flow**: Information organized from general to specific
- **Easy Navigation**: Clear table of contents and cross-links
- **Searchable Content**: Use descriptive headings and keywords

## 🔧 Architecture Overview

GretahAI ScriptWeaver follows a modular architecture with these key components:

### Stage-Based Workflow
- **10 Dedicated Stages**: Complete workflow from Excel upload to template-based generation
- **Centralized Imports**: All stages accessible through unified API
- **State Management**: Consistent state handling across all stages with StateStage enum
- **Advanced Features**: Hybrid editing, template-based generation, script browser

### Core Modules
- **AI Integration**: Centralized AI/LLM interactions with Gemini 2.0 Flash
- **Element Detection**: UI element discovery and interactive selection
- **Template Management**: Advanced template-based script generation system
- **Performance Monitoring**: Real-time system performance tracking
- **State Management**: Application state persistence and mutations
- **UI Components**: Extracted UI component library

### Documentation Structure
```
docs/
├── README.md           # This index file
├── USER_GUIDE.md       # Complete user workflow guide
├── DEVELOPER_GUIDE.md  # Architecture and development patterns
├── API.md             # Complete API reference
├── DEVELOPMENT.md      # Coding standards and guidelines
└── CONTRIBUTING.md    # Contribution guidelines and workflow
```

## 📖 Key Concepts

### StateManager Pattern
All application state is managed through a centralized StateManager class that provides:
- Consistent state mutations with logging
- Cross-stage state persistence
- Debug and inspection capabilities

### Modular Stage Architecture
Each application stage is implemented as an independent module:
- Single responsibility for each stage
- Consistent function signatures
- Standardized error handling and logging

### AI Integration
All AI interactions are routed through a centralized system:
- Comprehensive logging of all API calls
- Consistent error handling and retry logic
- Performance monitoring and optimization

## 🛠️ Development Workflow

### 1. Setup Development Environment
```bash
# Clone and setup
git clone <repository>
cd GretahAI_ScriptWeaver
pip install -r requirements.txt

# Configure
cp config.json.example config.json
# Edit config.json with your settings
```

### 2. Follow Development Patterns
- Use StateManager for all state changes
- Follow established logging patterns
- Include comprehensive error handling
- Write tests for new functionality

### 3. Submit Contributions
- Create feature branches
- Follow commit message conventions
- Include documentation updates
- Submit pull requests with detailed descriptions

## 📊 Project Metrics

### Code Organization
- **8 Modular Stages**: Each averaging 300-500 lines
- **62% Code Reduction**: From monolithic to modular architecture
- **Zero Breaking Changes**: Maintained backward compatibility
- **Enhanced Testability**: Individual stages can be tested in isolation

### Documentation Coverage
- **4 Comprehensive Guides**: Covering all aspects of development
- **Complete API Reference**: All functions documented with examples
- **Cross-Referenced**: Easy navigation between related topics
- **Maintainable Structure**: Organized for easy updates

## 🔍 Finding Information

### By Role

**End Users**
- Setup and usage: [Main README](../README.md)
- New features: [CHANGELOG](../CHANGELOG.md)

**Developers**
- Coding standards: [DEVELOPMENT.md](DEVELOPMENT.md)
- Function reference: [API.md](API.md)
- Architecture patterns: [DEVELOPMENT.md](DEVELOPMENT.md)

**Contributors**
- Getting started: [CONTRIBUTING.md](CONTRIBUTING.md)
- Code review process: [CONTRIBUTING.md](CONTRIBUTING.md)
- Development setup: [DEVELOPMENT.md](DEVELOPMENT.md)

### By Topic

**State Management**
- Patterns: [DEVELOPMENT.md](DEVELOPMENT.md#statemanager-patterns)
- API: [API.md](API.md#state-manager-api)

**Stage Development**
- Guidelines: [DEVELOPMENT.md](DEVELOPMENT.md#modifying-individual-stages)
- API: [API.md](API.md#stage-function-imports)

**AI Integration**
- Core functions: [API.md](API.md#core-module-functions)
- Development patterns: [DEVELOPMENT.md](DEVELOPMENT.md)

## 📞 Commercial Support & Contact

### Enterprise Support Services

**Primary Support Contact**: <EMAIL>

**Commercial Services Available**:
- Enterprise licensing and deployment
- Custom feature development and integration
- Professional training and certification programs
- Dedicated technical support with SLA guarantees
- Architecture consulting and best practices guidance

### Enterprise Development

**IMPORTANT**: GretahAI ScriptWeaver is proprietary commercial software. Development and customization services are available exclusively through authorized enterprise partnerships.

**Enterprise Development Services**:
- Custom stage development for specific business needs
- Advanced AI integration and optimization
- Enterprise-grade security and compliance features
- Multi-tenant deployment and configuration

### Contact Information

- **Website**: https://cogniron.com
- **Primary Contact**: <EMAIL>
- **Commercial Licensing**: Contact for pricing and enterprise licensing options
- **Enterprise Support**: Dedicated support packages available for commercial customers

## 📝 Documentation Maintenance

This documentation is actively maintained and updated with each release. For enterprise customers with valid support agreements, documentation updates and customization are available upon request.

For documentation updates and improvements, contact <EMAIL> with your enterprise support agreement details.

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized copying, distribution, modification, or use of this software is strictly prohibited.

**Commercial Licensing**: Contact <EMAIL> for licensing inquiries and enterprise partnership opportunities.

**Website**: https://cogniron.com

**Note**: This software requires a valid commercial license for use. Enterprise evaluation licenses are available upon request for qualified customers.

---

**Last Updated**: Version 2.0.0 - Modular Architecture Release
**Maintainers**: GretahAI ScriptWeaver Development Team
**License**: MIT License
