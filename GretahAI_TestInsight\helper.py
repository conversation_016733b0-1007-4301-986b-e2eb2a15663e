'''
Helper module for the GRETAH-CaseForge Autotest analysis tool.
This module provides a collection of utility functions to support the analysis
of automated test results, interaction with AI models (Ollama and Google AI),
file operations, data formatting, and database interactions.
Key functionalities include:
- Defining supported AI models (offline and online).
- Handling rate limiting for Google AI API calls.
- Formatting timestamps and file sizes.
- Reading log files, parsing JUnit XML reports, and extracting test details.
- Processing and analyzing performance metrics.
- Generating prompts tailored for specific AI analysis tasks:
    - Single log file summarization (structured JSON output).
    - Multi-perspective failure analysis using text, logs, and screenshots.
    - Root Cause Analysis (RCA) based on multiple log summaries.
    - Comparison summaries between different test runs.
- Interacting with AI models (Ollama and Google AI Studio) to:
    - Generate summaries from logs.
    - Perform RCA.
    - Analyze test failures with visual context (screenshots).
    - Generate comparison summaries.
- Tracking Google AI API usage for rate limiting purposes.
- Saving and loading API usage statistics.
- Saving RCA history.
- Reading API keys from configuration files.
- Interacting with an SQLite database to fetch test case details and
  conditionally generate/save AI-driven analysis summaries if they
  are missing, ensuring necessary artifacts (logs, screenshots, page source)
  are present.
'''

import traceback
import os
import time
import glob
import json
import pandas as pd
import xml.etree.ElementTree as ET
from pathlib import Path
from datetime import datetime, timedelta
from ollama import chat, ChatResponse
from google import genai
import threading
import queue
from PIL import Image
from io import StringIO
import re  # Import the regular expression module
import sqlite3
from sql_lite_db_helpers import (  # Import necessary DB functions
    get_single_test_case_details,
    save_summary,
    close_thread_local_connection,
    safe_parse_json,
    get_test_run_details,
    find_log_files,
    find_artifacts
)


# ================================
# Model Definitions
# ================================
OFFLINE_MODELS = ["mistral", "llama3", "llama3.2", "gemma", "phi3"]
ONLINE_MODELS = ["gemini-1.5-flash", "gemini-1.5-flash-8b"]

# ================================
# Rate Limiting Configuration
# ================================
MAX_RPM = 15  # Maximum requests per minute

# ================================
# Request Queue (kept for individual API request processing)
# ================================
request_queue = queue.Queue()
api_lock = threading.Lock()  # Ensure thread safety

def estimate_tokens(text):
    """A rough estimate of token count (assuming ~4 characters per token)."""
    return max(1, len(text) // 1)

# ================================
# Timestamp Formatting
# ================================
def format_timestamp_display(ts_string):
    """Formats a YYYYMMDD_HHMMSS timestamp string into a readable format."""
    if not ts_string or len(ts_string) != 15:  # Basic validation
        return ts_string  # Return original if format is unexpected
    try:
        dt_obj = datetime.strptime(ts_string, "%Y%m%d_%H%M%S")
        return dt_obj.strftime("%b %d, %Y %I:%M:%S %p")
    except ValueError:
        return ts_string  # Return original if parsing fails

# ================================
# File Operations
# ================================
def get_log_files(directory):
    """Retrieve all log files from the specified directory."""
    return [file for file in Path(directory).glob('*') if file.is_file()]

def is_binary_file(file_path):
    """Check if a file is binary."""
    with open(file_path, 'rb') as file:
        return b'\0' in file.read(1024)

def read_log_file(file_path):
    """Read and return the content of a log file."""
    if is_binary_file(file_path):
        raise ValueError(f"File appears to be binary: {file_path}")
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
        return file.read()

def save_summary_to_file(file_path, summary):
    """Save the summary to a file."""
    output_path = file_path.with_suffix('.summary.txt')
    with open(output_path, 'w', encoding='utf-8') as file:
        file.write(summary)
    return output_path

def check_directory_contents(log_dir):
    """Debug function to check directory contents"""
    result = {
        "current_directory": os.getcwd(),
        "log_directory": log_dir,
        "exists": os.path.exists(log_dir),
        "files": []
    }

    if os.path.exists(log_dir):
        result["files"] = os.listdir(log_dir)

    return result

def list_latest_logs(directory, since=None):
    """List logs created or modified since a specific time."""
    if since is None:
        return []
    return [
        os.path.basename(log) for log in glob.glob(os.path.join(directory, "*.log"))
        if os.path.getmtime(log) >= since
    ]

def get_log_status(content):
    """Determine log status based on content."""
    content_lower = content.lower()
    if "failed" in content_lower or "error" in content_lower or "failures" in content_lower:
        return "❌ Failed"
    elif "passed" in content_lower:
        return "✅ Passed"
    else:
        return "⚠️ Unknown"

def format_file_size(size_bytes):
    """Format file size in a human-readable format."""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024**2:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024**2):.1f} MB"

def generate_csv(data):
    output = StringIO()
    pd.DataFrame(data).to_csv(output, index=False)
    return output.getvalue()

def get_log_info(log_dir, log_file):
    """Return log details such as file path, content, size, status, and modified time."""
    log_path = os.path.join(log_dir, log_file)
    modified_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(os.path.getmtime(log_path)))
    file_size = os.path.getsize(log_path)
    content = read_log_file(log_path) if file_size > 0 else None
    status = get_log_status(content) if content else "⚠️ Unknown"
    return {
        "path": log_path,
        "modified_time": modified_time,
        "size_bytes": file_size,
        "content": content,
        "status": status,
    }

def parse_junit_xml(xml_file_path):
    """Parses a JUnit XML file and extracts test results and artifact paths."""
    try:
        print(f"DEBUG [helper]: Parsing XML file: {xml_file_path}") # Debug print
        tree = ET.parse(xml_file_path)
        root = tree.getroot()

        results = {"summary": {}, "test_suites": []}
        total_tests = 0
        total_failures = 0
        total_skipped = 0
        total_errors = 0

        for testsuite in root.findall('testsuite'):
            suite_data = {
                "name": testsuite.get("name"),
                "tests": int(testsuite.get("tests", 0)),
                "failures": int(testsuite.get("failures", 0)),
                "skipped": int(testsuite.get("skipped", 0)),
                "errors": int(testsuite.get("errors", 0)),
                "time": float(testsuite.get("time", 0)),
                "testcases": []
            }
            total_tests += suite_data["tests"]
            total_failures += suite_data["failures"]
            total_skipped += suite_data["skipped"]
            total_errors += suite_data["errors"]

            for testcase in testsuite.findall('testcase'):
                case_data = {
                    "name": testcase.get("name"),
                    "classname": testcase.get("classname"),
                    "duration": float(testcase.get("time", 0)),
                    "result": "PASSED",
                    "failure_message": None,
                    "log_path": None,
                    "screenshot_path": None,
                    "page_source_path": None
                }

                failure = testcase.find('failure')
                error = testcase.find('error')
                skipped = testcase.find('skipped')

                if failure is not None:
                    case_data["result"] = "FAILED"
                    case_data["failure_message"] = failure.get("message", "") + "\n" + (failure.text or "")
                elif error is not None:
                    case_data["result"] = "ERROR"
                    case_data["failure_message"] = error.get("message", "") + "\n" + (error.text or "")
                elif skipped is not None:
                    case_data["result"] = "SKIPPED"
                    case_data["failure_message"] = skipped.get("message", "")

                properties = testcase.find('properties')
                if properties is not None:
                    print(f"DEBUG [helper]: Found properties for case: {case_data['classname']}::{case_data['name']}") # Debug print
                    for prop in properties.findall('property'):
                        prop_name = prop.get('name')
                        prop_value = prop.get('value')
                        print(f"DEBUG [helper]:   Property: name='{prop_name}', value='{prop_value}'") # Debug print
                        if prop_name == 'artifact_log':
                            case_data['log_path'] = prop_value
                        elif prop_name == 'artifact_screenshot':
                            case_data['screenshot_path'] = prop_value
                        elif prop_name == 'artifact_page_source':
                            case_data['page_source_path'] = prop_value
                        elif prop_name == 'performance_metrics':
                            try:
                                # Try to parse the performance metrics as JSON
                                if prop_value:
                                    case_data['performance_metrics'] = json.loads(prop_value)
                                    print(f"DEBUG [helper]: Found performance metrics for case: {case_data['classname']}::{case_data['name']}")
                            except Exception as e:
                                print(f"DEBUG [helper]: Error parsing performance metrics for case {case_data['classname']}::{case_data['name']}: {e}")
                else:
                    print(f"DEBUG [helper]: No properties found for case: {case_data['classname']}::{case_data['name']}") # Debug print

                suite_data["testcases"].append(case_data)
            results["test_suites"].append(suite_data)

        # Extract performance metrics from the XML if available
        performance_metrics = None
        for property_elem in root.findall('.//property'):
            if property_elem.get('name') == 'performance_metrics':
                try:
                    # Try to parse the performance metrics as JSON
                    performance_metrics_str = property_elem.get('value')
                    if performance_metrics_str:
                        performance_metrics = json.loads(performance_metrics_str)
                        print(f"DEBUG [helper]: Found performance metrics in XML")
                except Exception as e:
                    print(f"DEBUG [helper]: Error parsing performance metrics: {e}")

        results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": total_tests - total_failures - total_skipped - total_errors,
            "failed_tests": total_failures + total_errors,
            "skipped_tests": total_skipped,
            "duration": float(root.get("time", sum(s["time"] for s in results["test_suites"]))),
            "performance_metrics": performance_metrics
        }

        return results
    except ET.ParseError as e:
        print(f"Error parsing XML file {xml_file_path}: {e}")
        return None
    except FileNotFoundError:
        print(f"Error: XML file not found at {xml_file_path}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred during XML parsing: {e}")
        import traceback
        print(traceback.format_exc())
        return None

# ================================
# Google AI Studio Usage Tracking
# ================================
def track_google_request(token_count=0, timestamps_list=None, token_usage_list=None):
    """
    Track a Google AI Studio API request by adding the timestamp and token count.
    This is used for rate limiting purposes.
    """
    now = datetime.now()

    if timestamps_list is not None:
        timestamps_list.append(now)

    if token_usage_list is not None and token_count is not None:
        token_usage_list.append((now, token_count))

    return now

def calculate_usage_metrics(timestamps_list=None, token_usage_list=None):
    """Calculate Google AI Studio usage metrics for rate limiting."""
    if not timestamps_list or not token_usage_list:
        return 0, 0, 0

    now = datetime.now()
    one_minute_ago = now - timedelta(minutes=1)
    one_day_ago = now - timedelta(days=1)

    # Filter timestamps for the relevant periods
    requests_last_minute = [ts for ts in timestamps_list if ts > one_minute_ago]
    requests_last_day = [ts for ts in timestamps_list if ts > one_day_ago]

    # Filter token usage for the last minute
    tokens_last_minute_data = [tup for tup in token_usage_list if tup[0] > one_minute_ago]
    tokens_last_minute_sum = sum(count for ts, count in tokens_last_minute_data)

    # Calculate metrics
    current_rpm = len(requests_last_minute)
    current_tpm = tokens_last_minute_sum
    current_rpd = len(requests_last_day)

    return current_rpm, current_tpm, current_rpd

def save_usage_data(timestamps_list=None, token_usage_list=None, file_path="google_usage_stats.json"):
    """Save Google AI Studio usage stats to a file."""
    if timestamps_list is None and token_usage_list is None:
        # If both are None, write an empty file (for clearing stats)
        with open(file_path, "w") as f:
            json.dump({"timestamps": [], "token_usage": []}, f)
        return

    # Convert timestamps to strings for JSON serialization
    timestamps_str = [ts.isoformat() for ts in timestamps_list] if timestamps_list else []
    token_usage_str = [(ts.isoformat(), count) for ts, count in token_usage_list] if token_usage_list else []

    with open(file_path, "w") as f:
        json.dump({"timestamps": timestamps_str, "token_usage": token_usage_str}, f)

def load_usage_data(file_path="google_usage_stats.json"):
    """Load Google AI Studio usage stats from a file."""
    try:
        with open(file_path, "r") as f:
            data = json.load(f)

        # Convert string timestamps back to datetime objects
        timestamps = [datetime.fromisoformat(ts) for ts in data.get("timestamps", [])]
        token_usage = [(datetime.fromisoformat(ts), count) for ts, count in data.get("token_usage", [])]

        return timestamps, token_usage
    except (FileNotFoundError, json.JSONDecodeError):
        # Return empty lists if file doesn't exist or is invalid
        return [], []

# ================================
# API Functions
# ================================
def run_google_ai_studio(prompt: str, api_key: str, model: str = "gemini-2.0-flash",
                         timestamps_list=None, token_usage_list=None) -> str:
    """
    Run Google AI Studio to generate content.
    Note: For Gemini 1.5 Flash or 1.5 Flash-8B, the rate limit is about 15 requests/min and
    1 million tokens/month. Ensure your usage does not exceed these quotas.
    """
    try:
        client = genai.Client(api_key=api_key)
        response = client.models.generate_content(
            model=model, contents=prompt
        )
        token_count = None

        if hasattr(response, 'usage_metadata') and response.usage_metadata:
             token_count = response.usage_metadata.total_token_count

        # Track this request for rate limiting
        track_google_request(
            token_count=token_count,
            timestamps_list=timestamps_list,
            token_usage_list=token_usage_list
        )

        return response.text

    except Exception as e:
        return f"Error from Google AI Studio: {str(e)}"

def generate_ai_failure_summary(
    screenshot_path: str,
    page_source_html: str,
    log_content: str,
    test_name: str,
    model_name: str = "gemini-1.5-flash",
    api_key: str = None,
    timestamps_list=None,
    token_usage_list=None,
    perspectives=None  # new param to customize perspectives
) -> dict:
    """
    Generates multiple-perspective summaries for detailed AI-driven RCA.
    """
    if not api_key:
        return {"error": "API key required for AI failure analysis."}

    if perspectives is None:
        perspectives = ["Technical", "User Experience", "Timing & Synchronization", "Environmental"]

    try:
        image = Image.open(screenshot_path)

        # Building multi-perspective prompt
        prompt = f"""
        You are an expert QA analyst. Provide concise summaries for the following test failure from multiple perspectives.

        Use the following JSON schema exactly:

        Summary = {{
            "Technical": str,
            "User Experience": str,
            "Timing & Synchronization": str,
            "Environmental": str
        }}

        Return: Summary

        Test Case: {test_name}

        Page Source (HTML): {page_source_html}

        Log Output: {log_content}

        Screenshot provided below to analyze visual/UI issues.
        """
        client = genai.Client(api_key=api_key)
        response = client.models.generate_content(
            model=model_name,
            contents=[image, prompt]
        )

        token_count = None
        if hasattr(response, 'usage_metadata') and response.usage_metadata:
            token_count = response.usage_metadata.total_token_count

        track_google_request(
            token_count=token_count,
            timestamps_list=timestamps_list,
            token_usage_list=token_usage_list
        )

        # --- Process Response ---
        raw_response_text = ""
        if model_name in ONLINE_MODELS:
            if hasattr(response, 'text'):
                raw_response_text = response.text
            elif isinstance(response, str):
                raw_response_text = response
            else:
                print(f"Unexpected response type from Gemini: {type(response)}")
                return {"error": "Unexpected response type from AI", "raw_response": str(response)}
        else:
            if isinstance(response, dict) and 'message' in response and 'content' in response['message']:
                raw_response_text = response['message']['content']
            elif isinstance(response, str):
                raw_response_text = response
            else:
                print(f"Unexpected response type from Ollama: {type(response)}")
                return {"error": "Unexpected response type from AI (Ollama)", "raw_response": str(response)}

        # --- Clean and Parse Response ---
        cleaned_text = raw_response_text.strip()

        match = re.search(r'```json\s*(\{.*?\})\s*```', cleaned_text, re.DOTALL)
        if match:
            json_string = match.group(1)
            print("DEBUG: Extracted JSON from markdown block.")
        else:
            start_index = cleaned_text.find('{')
            end_index = cleaned_text.rfind('}')
            if start_index != -1 and end_index != -1 and start_index < end_index:
                json_string = cleaned_text[start_index : end_index + 1]
                print("DEBUG: Extracted JSON using first '{' and last '}'.")
            else:
                json_string = cleaned_text
                print("DEBUG: No clear JSON structure found, attempting to parse cleaned text directly.")

        print(f"DEBUG: Attempting to parse JSON string: {json_string[:500]}...")

        try:
            parsed_response = json.loads(json_string)
            print("DEBUG: Successfully parsed JSON response.")
            return parsed_response
        except json.JSONDecodeError as e:
            error_msg = f"Failed to parse JSON response from AI after cleaning. Error: {e}"
            print(error_msg)
            print(f"--- Failing JSON String ---")
            print(json_string)
            print(f"--- End Failing JSON String ---")
            return {"error": error_msg, "raw_response": raw_response_text}

    except Exception as e:
        print(f"Error during AI summary generation: {e}")
        import traceback
        print(traceback.format_exc())
        return {"error": f"Error during AI summary generation: {str(e)}"}


def perform_rca(log_summaries, model_name, api_key=None,
                timestamps_list=None, token_usage_list=None):
    """
    Perform RCA using AI model based on summarized log data.
    """
    full_prompt = generate_rca_prompt(log_summaries)

    if model_name in OFFLINE_MODELS:
        response = chat(model=model_name, messages=[
            {'role': 'user', 'content': full_prompt}
        ])
        return response.message.content

    elif model_name in ONLINE_MODELS:
        if not api_key:
            return "API key required for online RCA."
        response = run_google_ai_studio(
            full_prompt, api_key, model=model_name,
            timestamps_list=timestamps_list,
            token_usage_list=token_usage_list
        )
        return response

    else:
        return f"Unsupported model: {model_name}"


def summarize_log(log_content, model_name, api_key=None, timestamps_list=None, token_usage_list=None):
    """
    Generate a summary of the log content using the specified AI model.
    The function returns a JSON string following the exact structure.
    """
    full_prompt = generate_log_summary_prompt(log_content)

    if model_name in OFFLINE_MODELS:
        response = chat(model=model_name, messages=[
            {'role': 'user', 'content': full_prompt}
        ])
        return response.message.content  # Expecting a valid JSON string
    elif model_name in ONLINE_MODELS:
        if not api_key:
            return json.dumps({"error": "API key is required for Google AI Studio."})
        response = run_google_ai_studio(full_prompt, api_key, model=model_name,
                                        timestamps_list=timestamps_list, token_usage_list=token_usage_list)
        return response  # Expecting a valid JSON string
    else:
        return json.dumps({"error": f"Unsupported model: {model_name}"})


def generate_log_summary_prompt(log_content):
    """
    Generates a prompt optimized for single-test log files to produce a strict JSON object
    that includes:
      - "errors_found": list of any detected SEVERE errors,
      - "system_health": overall verdict,
      - "test_passed": boolean true/false
    """
    default_prompt = (
        "You are an expert QA engineer reviewing the log of a single automated Selenium test.\n"
        "This log file represents exactly one test case.\n"
        "Your task is to output exactly a JSON object in the following format:\n\n"
        "{\n"
        "  \"errors_found\": [\n"
        "    {\n"
        "      \"level\": \"<Error level, e.g., 'SEVERE'>\",\n"
        "      \"message\": \"<Error message details>\",\n"
        "      \"source\": \"<Error source, e.g., 'network'>\",\n"
        "      \"timestamp\": <Timestamp as an integer>\n"
        "    }\n"
        "    /* Additional error objects if applicable */\n"
        "  ],\n"
        "  \"system_health\": \"<Concise summary like 'Passed without errors' or 'Failed due to network issues'>\",\n"
        "  \"test_passed\": <true or false>\n"
        "}\n\n"
        "GUIDELINES:\n"
        "- If the log contains SEVERE-level errors or stack traces, the test is likely failed.\n"
        "- If the log ends cleanly with 'Finishing test:' and no SEVERE errors, assume the test passed.\n"
        "- Output MUST be valid JSON, no extra commentary or markdown.\n\n"
        "LOG CONTENT:\n"
    )
    return default_prompt + log_content


def generate_rca_prompt(log_summaries):
    """
    Create a detailed prompt for Root Cause Analysis from summarized logs, requesting JSON output.
    """
    prompt = (
        "You are an expert system analyst performing a detailed Root Cause Analysis (RCA).  "
        "Analyze the following summarized log reports, identify patterns, correlate errors, and then "
        "**return your entire answer as a JSON object** with exactly these four keys (no extra text, no Markdown):\n\n"
        "  • identified_issues — an array of strings\n"
        "  • root_causes      — an array of strings\n"
        "  • potential_impact — an array of strings\n"
        "  • recommended_actions — an array of strings\n\n"
        "Here are the log summaries:\n"
    )

    for idx, summary in enumerate(log_summaries, 1):
        # Ensure summary is a dictionary before dumping
        if isinstance(summary, dict):
            summary_str = json.dumps(summary, indent=2)
        else:
            # Handle cases where summary might be a string (e.g., error message)
            summary_str = str(summary)
        prompt += f"\nLog {idx}:\n{summary_str}\n"

    prompt += "\nNow output **ONLY** a single valid JSON object (no prose) that matches the schema described."
    return prompt


def generate_comparison_summary_prompt(latest_run_summary, prev_run_summary, comparison_changes):
    """
    Generates a prompt asking for a concise summary of test run comparison changes,
    and requests the output in a structured JSON format.
    """
    import json

    def format_test_details(test_list, include_failure=False, include_ai_summary=False, limit=5):
        details = []
        for t in test_list[:limit]:  # Limit the number of detailed items
            detail = {
                "name": t.get('name', 'Unknown'),
                "class": t.get('classname', 'N/A'),
                "duration": f"{t.get('duration', 0):.2f}s"
            }
            if include_failure and t.get('failure_message'):
                snippet = (t.get('failure_message') or '').strip()
                snippet_limit = 3000
                detail["failure_snippet"] = snippet[:snippet_limit] + ('...' if len(snippet) > snippet_limit else '')
            if include_ai_summary and isinstance(t.get('ai_summary'), dict):
                ai_summary = t['ai_summary']
                summary_snippet = ai_summary.get('Technical', ai_summary.get('summary', ai_summary.get('error', 'AI Summary Available')))
                ai_snippet_limit = 1500
                detail["ai_analysis_snippet"] = str(summary_snippet)[:ai_snippet_limit] + ('...' if len(str(summary_snippet)) > ai_snippet_limit else '')
            details.append(detail)
        return json.dumps(details, indent=2)

    prompt = f"""
        Analyze the following test run comparison and provide a concise summary highlighting key changes and potential areas of concern.

        **Latest Run Stats:**
        - Total Tests: {latest_run_summary.get('total_tests', 'N/A')}
        - Passed: {latest_run_summary.get('passed_tests', 'N/A')}
        - Failed: {latest_run_summary.get('failed_tests', 'N/A')}
        - Pass Rate: {latest_run_summary.get('pass_rate', 'N/A')}%

        **Previous Run Stats:**
        - Total Tests: {prev_run_summary.get('total_tests', 'N/A')}
        - Passed: {prev_run_summary.get('passed_tests', 'N/A')}
        - Failed: {prev_run_summary.get('failed_tests', 'N/A')}
        - Pass Rate: {prev_run_summary.get('pass_rate', 'N/A')}%

        **Changes Detected:**
        - Fixed Tests (Passed now, Failed before): {len(comparison_changes.get('fixed_tests', []))}
        - Regressions (Failed now, Passed before): {len(comparison_changes.get('regression_tests', []))}
        - Still Failing (Failed in both): {len(comparison_changes.get('still_failing', []))}
        - New Tests Added: {len(comparison_changes.get('new_tests', []))}

        **Detailed Changes (Examples with Failure/AI Snippets where applicable):**
        Fixed: {format_test_details(comparison_changes.get('fixed_tests', []), include_failure=False, include_ai_summary=False)}
        Regressions: {format_test_details(comparison_changes.get('regression_tests', []), include_failure=True, include_ai_summary=True)}
        Still Failing: {format_test_details(comparison_changes.get('still_failing', []), include_failure=True, include_ai_summary=True)}
        New Tests: {format_test_details(comparison_changes.get('new_tests', []), include_failure=False, include_ai_summary=False)}

        **Instructions:**
        - Carefully analyze the provided test run comparison.
        - Return your summary as a JSON object with the following fields:
            {{
            "trend": "Overall trend (e.g., improvement, degradation, stability)",
            "highlights": ["List of key points and numbers, e.g., regressions, fixed tests, etc."],
            "regressions": [
                {{
                "name": "Test name",
                "class": "Test class",
                "failure_reason": "Short reason or snippet from failure_message or AI analysis"
                }},
                ...
            ],
            "still_failing_patterns": "Describe any patterns in still-failing tests, referencing failure messages or AI snippets if available.",
            "new_tests_impact": "Briefly comment on the impact of new tests.",
            "action_items": ["List of actionable recommendations based on the analysis."]
            }}
        - Do not include any text outside the JSON object.
        - Use concise, clear language.
    """
    return prompt


def generate_comparison_summary(latest_run_stats, prev_run_stats, changes, model_name, api_key,
                                timestamps_list=None, token_usage_list=None):
    """
    Generates an AI-powered summary comparing two test runs based on stats and changes.

    Args:
        latest_run_stats (dict): Summary statistics for the latest run.
        prev_run_stats (dict): Summary statistics for the previous run.
        changes (dict): Dictionary containing lists of fixed, regression, still_failing, new_tests.
        model_name (str): The name of the AI model to use.
        api_key (str): The API key for the AI service (if required).
        timestamps_list (list, optional): List to track request timestamps for rate limiting (Google). Defaults to None.
        token_usage_list (list, optional): List to track token usage (Google). Defaults to None.

    Returns:
        str: The generated comparison summary text, or an error message starting with "Error:".
    """
    prompt = generate_comparison_summary_prompt(latest_run_stats, prev_run_stats, changes)

    try:
        if model_name in OFFLINE_MODELS:
            response = chat(model=model_name, messages=[{'role': 'user', 'content': prompt}])
            # Ensure response structure is handled correctly for Ollama
            if isinstance(response, dict) and 'message' in response and 'content' in response['message']:
                 return response['message']['content']
            elif isinstance(response, ChatResponse) and hasattr(response, 'message') and hasattr(response.message, 'content'):
                 return response.message.content # Handle potential ChatResponse object
            else:
                 return f"Error: Unexpected response format from Ollama model '{model_name}'"
        elif model_name in ONLINE_MODELS:
            if not api_key:
                return "Error: API Key required for online models."
            # Use run_google_ai_studio helper function
            response = run_google_ai_studio(
                prompt=prompt,
                api_key=api_key,
                model=model_name,
                timestamps_list=timestamps_list,
                token_usage_list=token_usage_list
            )
            return response # run_google_ai_studio returns the text or an error string
        else:
            return f"Error: Unknown model type for '{model_name}'"
    except Exception as e:
        # Add traceback for better debugging
        import traceback
        print(f"Error generating comparison summary: {e}\n{traceback.format_exc()}")
        return f"Error generating comparison summary: {e}"


def prepare_rca_data(db_path, run_timestamp):
    """
    Fetches and prepares data for Root Cause Analysis (RCA) for a specific test run.

    Args:
        db_path (str): Path to the SQLite database.
        run_timestamp (str): The timestamp identifier of the test run.

    Returns:
        list: A list of dictionaries, each containing details for a failed test case.
              Returns an empty list if no failed tests are found or an error occurs.
        str: An error message if an error occurred during data preparation, otherwise None.
    """
    failed_test_details = []
    error_message = None
    try:
        all_test_cases = get_test_run_details(db_path, run_timestamp)
        close_thread_local_connection() # Close connection after fetching run details

        if not all_test_cases:
            return [], f"Could not retrieve test details for run {run_timestamp} from database."

        for case in all_test_cases:
            if case["result"] == "FAILED":
                details = {
                    "db_case_id": case["case_id"],
                    "test_name": case["name"],
                    "class_name": case["classname"],
                    "failure_message": case.get("failure_message", ""),
                    "log_snippet": None,
                    "ai_analysis": None
                }

                # Fetch Log Snippet
                log_artifact = find_log_files(db_path, case["case_id"])
                close_thread_local_connection() # Close connection after finding log
                log_path = log_artifact['path'] if log_artifact else None

                if log_path and os.path.exists(log_path):
                    try:
                        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                            details["log_snippet"] = f.read(2000) # Read first 2000 chars
                    except Exception as log_read_err:
                        details["log_snippet"] = f"Error reading log: {log_read_err}"
                else:
                    details["log_snippet"] = "Log file not found or not linked."

                # Fetch Existing AI Analysis
                analysis_artifacts = find_artifacts(db_path, case["case_id"], artifact_type='visual_analysis')
                close_thread_local_connection() # Close connection after finding analysis

                latest_analysis_artifact = analysis_artifacts[0] if analysis_artifacts else None

                if latest_analysis_artifact:
                    summary_content = latest_analysis_artifact.get('summary_content')
                    if summary_content:
                        try:
                            # Attempt to parse, store parsed or raw with error flag
                            summary_data = safe_parse_json(summary_content)
                            details['ai_analysis'] = summary_data if summary_data else {"raw_text": summary_content, "parse_error": True}
                        except Exception as parse_err:
                             details['ai_analysis'] = {"raw_text": summary_content, "parse_error": str(parse_err)}
                    else:
                        details['ai_analysis'] = {"error": "AI analysis artifact found but content is missing."}
                else:
                    # Explicitly set to None if no analysis artifact exists
                    details['ai_analysis'] = None

                failed_test_details.append(details)

    except sqlite3.Error as db_err:
        error_message = f"Database error fetching failed test details: {db_err}"
        close_thread_local_connection() # Ensure connection closed on error
    except Exception as general_err:
        error_message = f"An unexpected error occurred while preparing RCA data: {general_err}\n{traceback.format_exc()}"
        close_thread_local_connection() # Ensure connection closed on error

    return failed_test_details, error_message


def generate_and_save_comparison_summary(
    db_path, current_run_id, current_run_ts, prev_run_ts,
    latest_run_stats, prev_run_stats, changes,
    model_name, api_key, timestamps_list=None, token_usage_list=None
):
    """
    Generates an AI comparison summary, saves it to the database, and returns the result.

    Args:
        db_path (str): Path to the database.
        current_run_id (int): The database ID of the current run.
        current_run_ts (str): Timestamp of the current run.
        prev_run_ts (str): Timestamp of the previous run being compared against.
        latest_run_stats (dict): Statistics for the latest run.
        prev_run_stats (dict): Statistics for the previous run.
        changes (dict): Dictionary of changes between the runs.
        model_name (str): Name of the AI model.
        api_key (str): API key for the AI model (if applicable).
        timestamps_list (list, optional): List for Google rate limiting. Defaults to None.
        token_usage_list (list, optional): List for Google token tracking. Defaults to None.

    Returns:
        tuple: (summary_text, error_message)
               summary_text (str): The generated summary text, or None if generation failed.
               error_message (str): An error message string if any step failed, otherwise None.
    """
    summary_text = None
    error_message = None

    try:
        summary_text = generate_comparison_summary(
            latest_run_stats,
            prev_run_stats,
            changes,
            model_name=model_name,
            api_key=api_key,
            timestamps_list=timestamps_list,
            token_usage_list=token_usage_list
        )

        if not summary_text or summary_text.startswith("Error"):
            error_message = f"AI summary generation failed: {summary_text}"
            return None, error_message

        # Prepare content for saving (add metadata)
        summary_to_save = {
            "metadata": {
                "current_run_timestamp": current_run_ts,
                "compared_run_timestamp": prev_run_ts,
                "model_name": model_name,
                "generated_at": datetime.now().isoformat()
            },
            "summary": summary_text
        }
        summary_content_str = json.dumps(summary_to_save)

        # Save the summary
        save_success = save_summary(
            db_path,
            case_id=None,  # Not linked to a specific case
            run_id=current_run_id,
            model_name=model_name,
            summary_type='comparison_summary',
            summary_content=summary_content_str
        )
        close_thread_local_connection()

        if not save_success:
            error_message = "Generated summary but failed to save it to the database."
            # Return the summary text anyway, but with an error message
            return summary_text, error_message

        # Success case
        return summary_text, None

    except Exception as e:
        error_message = f"Error during comparison summary generation or saving: {e}\n{traceback.format_exc()}"
        close_thread_local_connection() # Ensure connection closed on error
        return None, error_message

def save_rca_history(rca_content, file_path="rca_history.json"):
    try:
        history = []
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                history = json.load(f)

        entry = {
            "timestamp": datetime.now().isoformat(),
            "rca_content": rca_content
        }

        history.append(entry)

        with open(file_path, 'w') as f:
            json.dump(history, f, indent=2)
        return True
    except Exception as e:
        return f"Failed to save RCA history: {e}"


def read_api_key_from_config(config_path):
    """Reads the Google API key from the specified config.json file."""
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
                # --- Read 'google_api_key' ---
                return config_data.get("google_api_key")
                # --- End read ---
        except json.JSONDecodeError:
            print(f"Error: Invalid JSON format in config file: {config_path}")
            return None
        except Exception as e:
            print(f"Error reading config file {config_path}: {e}")
            return None
    else:
        print(f"Config file not found at path: {config_path}")
        return None

def generate_and_save_summary_if_missing(db_case_id, database_path, model_name, api_key):
    """
    Checks if a visual_analysis summary exists for a case_id, generates and saves it if not.

    Args:
        db_case_id (int): The database ID of the test case.
        database_path (str): Path to the SQLite database.
        model_name (str): The AI model to use for generation.
        api_key (str): The API key (if required).

    Returns:
        str: Status ('generated', 'skipped_exists', 'skipped_missing_artifacts', 'error_fetching', 'error_generating', 'error_saving').
    """
    try:
        # 1. Fetch current details, including existing summary and artifact paths
        details = get_single_test_case_details(database_path, db_case_id)
        close_thread_local_connection()

        if not details:
            print(f"Error fetching details for case {db_case_id}")
            return 'error_fetching'

        # 2. Check if summary already exists
        if details.get('visual_analysis'):
            # print(f"Skipping case {db_case_id}: Summary already exists.")
            return 'skipped_exists'

        # 3. Check if required artifacts exist
        screenshot_path = details.get("screenshot_path")
        page_source_path = details.get("page_source_path")
        log_path = details.get("log_path")

        screenshot_exists = screenshot_path and os.path.exists(screenshot_path)
        page_source_exists = page_source_path and os.path.exists(page_source_path)
        log_exists = log_path and os.path.exists(log_path)

        if not (screenshot_exists and page_source_exists and log_exists):
            print(f"Skipping case {db_case_id}: Missing required artifacts.")
            missing = []
            if not screenshot_exists: missing.append("Screenshot")
            if not page_source_exists: missing.append("Page Source")
            if not log_exists: missing.append("Log File")
            print(f"  Missing: {', '.join(missing)}")
            return 'skipped_missing_artifacts'

        # 4. Generate Summary
        print(f"Generating summary for case {db_case_id}...")
        try:
            with open(page_source_path, 'r', encoding='utf-8', errors='ignore') as f: page_source = f.read()
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f: log_content = f.read()

            analysis_result = generate_ai_failure_summary(
                screenshot_path, page_source, log_content, details.get("test_name", "Unknown Test"),
                model_name=model_name, api_key=api_key
            )
        except FileNotFoundError as fnf_err:
             print(f"Error reading resource file during generation for case {db_case_id}: {fnf_err}")
             return 'error_generating' # Treat as generation error
        except Exception as gen_err:
            print(f"Error during AI analysis generation for case {db_case_id}: {gen_err}")
            # Optionally save the error state as the summary
            try:
                error_content_str = json.dumps({"error": str(gen_err)})
                save_summary(database_path, db_case_id, model_name, 'visual_analysis', error_content_str)
                close_thread_local_connection()
            except Exception: pass # Ignore error during error saving
            return 'error_generating'

        # 5. Save Summary
        analysis_content_str = None
        try:
            if isinstance(analysis_result, str):
                # Attempt to parse if it looks like JSON, otherwise save as raw text
                if analysis_result.strip().startswith('{') and analysis_result.strip().endswith('}'):
                    parsed = safe_parse_json(analysis_result)
                    analysis_content_str = json.dumps(parsed if parsed else {"raw_text": analysis_result, "parse_error": True})
                else:
                    analysis_content_str = json.dumps({"raw_text": analysis_result})
            elif isinstance(analysis_result, dict):
                analysis_content_str = json.dumps(analysis_result)
            else:
                analysis_content_str = json.dumps({"raw_text": str(analysis_result)})

            if save_summary(database_path, db_case_id, model_name, 'visual_analysis', analysis_content_str):
                close_thread_local_connection()
                print(f"Successfully generated and saved summary for case {db_case_id}.")
                return 'generated'
            else:
                close_thread_local_connection()
                print(f"Failed to save generated summary for case {db_case_id}.")
                return 'error_saving'
        except Exception as save_err:
            print(f"Error processing or saving AI analysis for case {db_case_id}: {save_err}")
            close_thread_local_connection()
            return 'error_saving'

    except Exception as outer_err:
        print(f"Unexpected error processing case {db_case_id}: {outer_err}")
        close_thread_local_connection()
        return 'error_fetching' # Treat unexpected errors as fetching issues initially

def extract_json_dict(raw: str) -> dict:
    """
    Extracts and parses a JSON object from a raw string that may include
    markdown fences or extra prose. Returns a Python dict on success,
    or raises ValueError if parsing fails.
    """
    # 1) Strip code fences (``` or ```json)
    s = raw.strip()
    # remove leading ```... and trailing ```
    if s.startswith("```"):
        # drop any leading fence line
        s = re.sub(r"^```(?:json)?\s*", "", s)
        # drop any trailing fence line
        s = re.sub(r"\s*```$", "", s)

    # 2) Find the first '{' and last '}' and crop
    first = s.find("{")
    last  = s.rfind("}")
    if first == -1 or last == -1 or last <= first:
        raise ValueError("No JSON object found in input")
    s = s[first:last+1]

    # 3) Parse
    try:
        return json.loads(s)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON: {e.msg}") from e