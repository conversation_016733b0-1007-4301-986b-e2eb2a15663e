"""
Core functionality for the test script generator.
This module provides the main functionality for detecting elements, matching them with test steps,
and generating test scripts.
"""

import os
import time
import json
import datetime
import re
import subprocess
import logging
import streamlit as st
from selenium import webdriver

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.core")
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException, StaleElementReferenceException
from webdriver_manager.chrome import ChromeDriverManager
import pandas as pd
import google.generativeai as genai

# Import utility functions - using absolute imports with importlib
import sys
import os
import importlib.util

# Get the path to utils.py
current_dir = os.path.dirname(os.path.abspath(__file__))
utils_path = os.path.join(current_dir, "utils.py")

# Import utils.py with importlib
if os.path.exists(utils_path):
    spec = importlib.util.spec_from_file_location("auto_test_generator_utils", utils_path)
    auto_test_generator_utils = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(auto_test_generator_utils)

    # Get functions from utils
    parse_excel = auto_test_generator_utils.parse_excel
    save_json = auto_test_generator_utils.save_json
    load_json = auto_test_generator_utils.load_json
    get_config = auto_test_generator_utils.get_config

#------------------------------------------------------------------------------
# Element Detection
#------------------------------------------------------------------------------

def setup_driver(headless=False):
    """
    Set up a Chrome WebDriver with anti-detection measures.

    Args:
        headless (bool): Whether to run in headless mode

    Returns:
        WebDriver: A configured WebDriver instance
    """
    chrome_options = Options()

    if headless:
        chrome_options.add_argument("--headless")

    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")

    # Enhanced anti-detection measures
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Add a realistic user agent
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    try:
        # Create the driver with ChromeDriverManager
        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=chrome_options
        )

        # Additional anti-detection measures using JavaScript
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Set CDP to disable automation flags
        driver.execute_cdp_cmd('Network.setUserAgentOverride', {
            "userAgent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

        # Set navigator properties to make detection harder
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': '''
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });
            '''
        })

        return driver
    except WebDriverException as e:
        logger.error(f"Error setting up WebDriver: {e}")
        return None

def generate_element_name(element):
    """
    Generate a descriptive name for an element based on its attributes.

    Args:
        element: WebElement or dictionary of element attributes

    Returns:
        str: A descriptive name for the element
    """
    # Extract attributes from WebElement or use provided dictionary
    if not isinstance(element, dict):
        attrs = {
            'id': element.get_attribute('id') or '',
            'name': element.get_attribute('name') or '',
            'class': element.get_attribute('class') or '',
            'type': element.get_attribute('type') or '',
            'tag': element.tag_name,
            'text': element.text.strip() if element.text else '',
            'placeholder': element.get_attribute('placeholder') or '',
            'value': element.get_attribute('value') or '',
            'role': element.get_attribute('role') or ''
        }
    else:
        attrs = element

    # Clean text function to create valid variable names
    def clean_text(text, max_length=30):
        if not text:
            return ""
        # Keep only alphanumeric and spaces
        cleaned = ''.join(c for c in text if c.isalnum() or c.isspace())
        # Truncate to max length and remove trailing spaces
        cleaned = cleaned[:max_length].strip()
        # Replace spaces with underscores and convert to lowercase
        return cleaned.lower().replace(' ', '_')

    # Try different attributes to generate a name
    name_parts = []

    # Use id as primary identifier
    if attrs.get('id'):
        name_parts.append(clean_text(attrs['id']))

    # Use name as secondary identifier
    elif attrs.get('name'):
        name_parts.append(clean_text(attrs['name']))

    # Use text content for buttons, links, etc.
    elif attrs.get('text') and len(attrs.get('text', '')) < 50:
        name_parts.append(clean_text(attrs['text']))

    # Use placeholder for input fields
    elif attrs.get('placeholder'):
        name_parts.append(clean_text(attrs['placeholder']))

    # Use value for inputs with values
    elif attrs.get('value') and attrs.get('tag') == 'input':
        name_parts.append(clean_text(attrs['value']))

    # If no good identifier, use tag and role
    if not name_parts:
        if attrs.get('tag'):
            name_parts.append(attrs.get('tag').lower())
        if attrs.get('role'):
            name_parts.append(attrs.get('role').lower())

    # Add type suffix for inputs
    if attrs.get('tag') == 'input' and attrs.get('type') and attrs.get('type') not in ''.join(name_parts).lower():
        name_parts.append(attrs.get('type').lower())

    # If we have a name, use it
    if name_parts:
        return '_'.join(name_parts)

    # Last resort: use tag with a hash
    tag = attrs.get('tag', 'element').lower()
    return f"{tag}_{hash(str(attrs)) % 10000}"

def generate_unique_selector(driver, element):
    """
    Generate a unique CSS or XPath selector for an element.

    Args:
        driver: WebDriver instance
        element: WebElement

    Returns:
        dict: {'type': 'css|xpath', 'selector': selector_string}
    """
    # Try ID selector (most reliable)
    element_id = element.get_attribute('id')
    if element_id:
        selector = f"#{element_id}"
        if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
            return {'type': 'css', 'selector': selector}

    # Try name attribute
    element_name = element.get_attribute('name')
    if element_name:
        selector = f"[name='{element_name}']"
        if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
            return {'type': 'css', 'selector': selector}

    # Try data-testid or other data attributes
    for attr in ['data-testid', 'data-test', 'data-automation', 'data-cy', 'data-qa']:
        attr_value = element.get_attribute(attr)
        if attr_value:
            selector = f"[{attr}='{attr_value}']"
            if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
                return {'type': 'css', 'selector': selector}

    # Try tag with class
    element_class = element.get_attribute('class')
    tag_name = element.tag_name
    if element_class:
        classes = element_class.split()
        for cls in classes:
            if cls and not cls.isspace():
                selector = f"{tag_name}.{cls}"
                if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
                    return {'type': 'css', 'selector': selector}

    # Try XPath with text content
    element_text = element.text.strip()
    if element_text:
        # Escape quotes in text
        text = element_text.replace("'", "\\'")
        selector = f"//{tag_name}[contains(text(), '{text}')]"
        if len(driver.find_elements(By.XPATH, selector)) == 1:
            return {'type': 'xpath', 'selector': selector}

    # Generate a full CSS path as last resort
    try:
        js_path = driver.execute_script("""
            function getPathTo(element) {
                if (element.id !== '')
                    return '#' + element.id;
                if (element === document.body)
                    return 'body';

                var ix = 0;
                var siblings = element.parentNode.childNodes;
                for (var i = 0; i < siblings.length; i++) {
                    var sibling = siblings[i];
                    if (sibling === element)
                        return getPathTo(element.parentNode) + ' > ' + element.tagName.toLowerCase() + ':nth-child(' + (ix + 1) + ')';
                    if (sibling.nodeType === 1 && sibling.tagName === element.tagName)
                        ix++;
                }
            }
            return getPathTo(arguments[0]);
        """, element)

        return {'type': 'css', 'selector': js_path}
    except:
        # Absolute fallback: XPath with position
        try:
            js_xpath = driver.execute_script("""
                function getXPathTo(element) {
                    if (element.id !== '')
                        return "//*[@id='" + element.id + "']";
                    if (element === document.body)
                        return "/html/body";

                    var ix = 0;
                    var siblings = element.parentNode.childNodes;
                    for (var i = 0; i < siblings.length; i++) {
                        var sibling = siblings[i];
                        if (sibling === element)
                            return getXPathTo(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                        if (sibling.nodeType === 1 && sibling.tagName === element.tagName)
                            ix++;
                    }
                }
                return getXPathTo(arguments[0]);
            """, element)

            return {'type': 'xpath', 'selector': js_xpath}
        except:
            # Last resort
            return {'type': 'css', 'selector': tag_name}

def find_elements_by_css(driver):
    """
    Find important interactive elements using focused CSS selectors.

    Args:
        driver: WebDriver instance

    Returns:
        list: List of detected elements
    """
    elements = []

    # Focused list of selectors for key interactive elements
    selectors = [
        # Primary interactive elements
        "input[type='text']", "input[type='email']", "input[type='password']",
        "input[type='submit']", "input[type='button']", "input[type='checkbox']",
        "input[type='radio']",

        # Essential UI controls
        "button", "a[href]", "select", "textarea",

        # Interactive elements with role attributes
        "[role]", "[role='button']", "[role='link']", "[role='checkbox']", "[role='radio']",
        "[role='tab']", "[role='menuitem']", "[role='combobox']", "[role='listbox']",
        "[role='menu']", "[role='menubar']", "[role='dialog']", "[role='alert']",
        "[role='search']", "[role='form']", "[role='navigation']", "[role='banner']",

        # Elements with event handlers
        "[onclick]", "[onchange]", "[onsubmit]", "[onmouseover]", "[onmouseout]",
        "[onkeydown]", "[onkeyup]", "[onfocus]", "[onblur]", "[oninput]",

        # Form elements
        "form", "label", "fieldset", "legend", "optgroup", "datalist",

        # Elements that might be clickable
        ".btn", ".button", "[class*='btn']", "[class*='button']",
        "[id*='btn']", "[id*='button']", "[class*='link']", "[id*='link']",
        "[class*='submit']", "[id*='submit']", "[class*='cancel']", "[id*='cancel']",

        # Common UI elements
        "header", "footer", "nav", "aside", "main", "section", "article",
        "div[id]", "div[class]", "span[id]", "span[class]", "p[id]", "p[class]",

        # Common UI components
        "table", "tr", "td", "th", "thead", "tbody", "tfoot",
        "ul", "ol", "li", "dl", "dt", "dd",
        "img", "iframe", "video", "audio", "canvas",
        "h1", "h2", "h3", "h4", "h5", "h6",

        # Elements with common attributes
        "[id]", "[name]", "[title]", "[aria-label]", "[aria-labelledby]",
        "[placeholder]", "[value]", "[for]", "[href]", "[src]",

        # Elements with tabindex
        "[tabindex]", "[tabindex='0']",

        # Generic elements that might be interactive
        "div", "span"
    ]

    # Process each selector
    for selector in selectors:
        try:
            found_elements = driver.find_elements(By.CSS_SELECTOR, selector)
            for element in found_elements:
                try:
                    # Check if element is displayed
                    if element.is_displayed():
                        # Generate a unique selector
                        selector_info = generate_unique_selector(driver, element)

                        # Generate a name
                        name = generate_element_name(element)

                        # Get element position and size
                        rect = element.rect

                        # Create element object
                        element_obj = {
                            'name': name,
                            'selector_type': selector_info['type'],
                            'selector': selector_info['selector'],
                            'attributes': {
                                'id': element.get_attribute('id') or '',
                                'name': element.get_attribute('name') or '',
                                'class': element.get_attribute('class') or '',
                                'type': element.get_attribute('type') or '',
                                'tag': element.tag_name,
                                'text': element.text.strip() if element.text else '',
                                'placeholder': element.get_attribute('placeholder') or '',
                                'value': element.get_attribute('value') or '',
                                'role': element.get_attribute('role') or '',
                                'visible': element.is_displayed(),
                                'x': rect['x'],
                                'y': rect['y'],
                                'width': rect['width'],
                                'height': rect['height']
                            }
                        }

                        elements.append(element_obj)
                except StaleElementReferenceException:
                    continue
                except Exception as e:
                    logger.warning(f"Error processing element with selector {selector}: {e}")
        except Exception as e:
            logger.warning(f"Error finding elements with selector {selector}: {e}")

    return elements

def find_elements_by_js(driver):
    """
    Find elements using JavaScript to detect all interactive elements.

    Args:
        driver: WebDriver instance

    Returns:
        list: List of detected elements
    """
    script = """
    function getAllInteractiveElements() {
        // Get all elements in the document
        const allElements = document.querySelectorAll('*');
        const interactiveElements = [];

        // Check each element
        for (let element of allElements) {
            // Skip hidden elements but include those that might be conditionally visible
            if (element.offsetParent === null && element.tagName !== 'BODY' &&
                getComputedStyle(element).display === 'none' &&
                getComputedStyle(element).visibility === 'hidden') continue;

            const tagName = element.tagName.toLowerCase();
            const hasClickHandler = element.onclick || element.getAttribute('onclick');
            const hasChangeHandler = element.onchange || element.getAttribute('onchange');
            const hasInputHandler = element.oninput || element.getAttribute('oninput');
            const hasKeyHandler = element.onkeydown || element.getAttribute('onkeydown') ||
                                element.onkeyup || element.getAttribute('onkeyup');
            const hasMouseHandler = element.onmouseover || element.getAttribute('onmouseover') ||
                                  element.onmouseout || element.getAttribute('onmouseout');
            const role = element.getAttribute('role');
            const id = element.id || '';
            const className = element.className || '';
            const name = element.name || '';
            const type = element.type || '';

            // Check if element has any attributes that suggest interactivity
            const hasInteractiveAttributes = element.hasAttribute('tabindex') ||
                                           element.hasAttribute('aria-label') ||
                                           element.hasAttribute('aria-labelledby') ||
                                           element.hasAttribute('aria-describedby') ||
                                           element.hasAttribute('aria-controls') ||
                                           element.hasAttribute('aria-expanded') ||
                                           element.hasAttribute('aria-haspopup') ||
                                           element.hasAttribute('aria-selected') ||
                                           element.hasAttribute('aria-checked');

            // Check if element has classes or IDs suggesting interactivity
            const hasInteractiveClassOrId =
                /btn|button|link|submit|cancel|menu|tab|dropdown|select|checkbox|radio|toggle|switch|slider|scroll|drag|click|tap|swipe/i.test(id) ||
                /btn|button|link|submit|cancel|menu|tab|dropdown|select|checkbox|radio|toggle|switch|slider|scroll|drag|click|tap|swipe/i.test(className);

            const isInteractive =
                // Standard interactive elements
                tagName === 'a' ||
                tagName === 'button' ||
                tagName === 'input' ||
                tagName === 'select' ||
                tagName === 'textarea' ||
                tagName === 'label' ||
                tagName === 'option' ||
                tagName === 'form' ||
                tagName === 'fieldset' ||
                tagName === 'legend' ||
                tagName === 'datalist' ||
                tagName === 'optgroup' ||
                // Elements with interactive roles
                role === 'button' ||
                role === 'link' ||
                role === 'checkbox' ||
                role === 'radio' ||
                role === 'tab' ||
                role === 'menuitem' ||
                role === 'menu' ||
                role === 'menubar' ||
                role === 'listbox' ||
                role === 'option' ||
                role === 'switch' ||
                role === 'combobox' ||
                role === 'slider' ||
                role === 'spinbutton' ||
                role === 'textbox' ||
                role === 'searchbox' ||
                // Elements with event handlers
                hasClickHandler ||
                hasChangeHandler ||
                hasInputHandler ||
                hasKeyHandler ||
                hasMouseHandler ||
                // Elements with interactive attributes
                hasInteractiveAttributes ||
                // Elements with interactive classes or IDs
                hasInteractiveClassOrId ||
                // Special cases for common UI elements
                (tagName === 'div' && (id || className)) ||
                (tagName === 'span' && (id || className)) ||
                (tagName === 'img' && (element.hasAttribute('onclick') || element.hasAttribute('alt'))) ||
                (tagName === 'svg' && (element.hasAttribute('onclick') || element.parentElement.hasAttribute('onclick')));

            // Include all elements with id or name attributes as they might be important
            const isIdentifiable = id || name;

            if (isInteractive || isIdentifiable) {
                // Get element info
                const rect = element.getBoundingClientRect();

                // Include elements that might be conditionally visible
                if (rect.width > 0 || rect.height > 0 ||
                    getComputedStyle(element).display !== 'none' ||
                    getComputedStyle(element).visibility !== 'hidden') {

                    // Get computed style for more accurate visibility check
                    const style = getComputedStyle(element);
                    const isVisible = style.display !== 'none' && style.visibility !== 'hidden' &&
                                    style.opacity !== '0' && rect.width > 0 && rect.height > 0;

                    interactiveElements.push({
                        element: element,
                        tagName: tagName,
                        id: id,
                        name: name,
                        className: className,
                        type: type,
                        value: element.value || '',
                        text: element.textContent.trim() || '',
                        placeholder: element.placeholder || '',
                        role: role || '',
                        x: rect.x,
                        y: rect.y,
                        width: rect.width,
                        height: rect.height,
                        isVisible: isVisible
                    });
                }
            }
        }

        return interactiveElements;
    }

    return getAllInteractiveElements();
    """

    js_elements = []

    try:
        # Execute JavaScript to find interactive elements
        elements_data = driver.execute_script(script)

        # Process each element
        for data in elements_data:
            try:
                # Get the actual WebElement
                element = data['element']

                # Generate a unique selector
                selector_info = generate_unique_selector(driver, element)

                # Generate a name
                name = generate_element_name({
                    'id': data['id'],
                    'name': data['name'],
                    'tag': data['tagName'],
                    'text': data['text'],
                    'placeholder': data['placeholder'],
                    'value': data['value'],
                    'role': data['role']
                })

                # Create element object
                element_obj = {
                    'name': name,
                    'selector_type': selector_info['type'],
                    'selector': selector_info['selector'],
                    'attributes': {
                        'id': data['id'],
                        'name': data['name'],
                        'class': data['className'],
                        'type': data['type'],
                        'tag': data['tagName'],
                        'text': data['text'],
                        'placeholder': data['placeholder'],
                        'value': data['value'],
                        'role': data['role'],
                        'visible': data.get('isVisible', True),
                        'x': data.get('x', 0),
                        'y': data.get('y', 0),
                        'width': data.get('width', 0),
                        'height': data.get('height', 0)
                    }
                }

                js_elements.append(element_obj)
            except StaleElementReferenceException:
                continue
            except Exception as e:
                logger.warning(f"Error processing JS element: {e}")
    except Exception as e:
        logger.error(f"Error executing JavaScript to find elements: {e}")

    return js_elements

def filter_important_elements(elements):
    """
    Filter and prioritize important UI elements while removing duplicates.

    Args:
        elements (list): List of detected elements

    Returns:
        list: Filtered list of important elements
    """
    # Define high-priority interactive elements only
    important_tags = {
        'input': 10,
        'button': 10,
        'a': 9,
        'select': 9,
        'textarea': 8
    }

    # Focus on key identifying attributes only
    important_attributes = [
        'id', 'name', 'type', 'role',
        'placeholder', 'value', 'href'
    ]

    filtered_elements = []
    seen_selectors = set()

    for element in elements:
        # Skip if we've seen this selector before
        if element['selector'] in seen_selectors:
            continue

        attrs = element['attributes']
        tag = attrs.get('tag', '').lower()

        # Calculate element importance score
        score = 0

        # Base score from tag type
        score += important_tags.get(tag, 0)

        # Boost score for interactive elements
        if attrs.get('type') in ['submit', 'button', 'text', 'password', 'email', 'checkbox', 'radio']:
            score += 5

        # Boost score for elements with important attributes
        for attr in important_attributes:
            if attrs.get(attr):
                score += 2

        # Boost score for visible elements
        if attrs.get('visible', False):
            score += 3

        # Boost score for elements with text content
        if attrs.get('text'):
            score += 2

        # Only include elements with a minimum importance score
        if score >= 5:
            element['importance_score'] = score
            filtered_elements.append(element)
            seen_selectors.add(element['selector'])

    # Sort by importance score
    filtered_elements.sort(key=lambda x: x.get('importance_score', 0), reverse=True)

    return filtered_elements

def detect_elements(url, wait_time=5, scroll=True, headless=True):
    """
    Optimized function to detect important interactive UI elements on a webpage.

    Args:
        url (str): URL of the webpage to analyze
        wait_time (int): Time to wait for page to load in seconds
        scroll (bool): Whether to scroll the page to find more elements
        headless (bool): Whether to run in headless mode

    Returns:
        list: List of detected elements
    """
    logger.info(f"Starting element detection for: {url}")

    # Setup driver in headless mode by default for better performance
    driver = setup_driver(headless=headless)
    if not driver:
        logger.error("Failed to set up WebDriver")
        return []

    elements = []
    try:
        # Navigate to URL with reduced wait time
        logger.info(f"Navigating to {url}")
        driver.get(url)

        # Use WebDriverWait instead of sleep for better performance
        WebDriverWait(driver, wait_time).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )

        # Single smooth scroll to handle dynamic loading
        if scroll:
            logger.info("Quick scroll to load dynamic content...")
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)  # Brief wait for dynamic content

            # Scroll back to top
            driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)

        # Try multiple methods to find elements
        logger.info("Finding elements using CSS selectors...")
        css_elements = find_elements_by_css(driver)

        logger.info("Finding elements using JavaScript...")
        js_elements = find_elements_by_js(driver)

        # Combine and deduplicate elements
        all_elements = css_elements + js_elements

        # Filter and deduplicate
        logger.info("Filtering and deduplicating elements...")
        filtered_elements = filter_important_elements(all_elements)

        logger.info(f"Found {len(filtered_elements)} important unique elements out of {len(all_elements)} total elements")
        return filtered_elements

    except Exception as e:
        logger.error(f"Error during element detection: {e}")
        return []

    finally:
        try:
            driver.quit()
        except:
            pass

#------------------------------------------------------------------------------
# AI Integration
#------------------------------------------------------------------------------

def load_config():
    """
    Load configuration from config.json file.

    Returns:
        dict: Configuration dictionary
    """
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        return config
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def initialize_ai():
    """
    Initialize the Google AI client.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        config = load_config()
        api_key = config.get('google_api_key')

        if not api_key:
            logger.warning("Google API key not found in config.json")
            return False

        genai.configure(api_key=api_key)
        logger.info("Google AI client initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Error initializing Google AI client: {e}")
        return False

def generate_ai_response(prompt, model_name="gemini-2.0-flash"):
    """
    Generate a response from the AI model based on the provided prompt.

    This function is a wrapper around generate_llm_response from core.ai module
    to maintain backward compatibility with existing code.

    Args:
        prompt (str): The prompt to send to the AI.
        model_name (str): The AI model to use.

    Returns:
        str: The AI response.
    """
    logger.info("=== ENTERING generate_ai_response ===")
    logger.info(f"Model name: {model_name}")
    logger.info(f"Prompt type: {type(prompt)}")
    logger.info(f"Prompt length: {len(prompt) if isinstance(prompt, str) else 'N/A'}")

    try:
        # Import generate_llm_response from core.ai
        try:
            logger.info("Attempting to import generate_llm_response from core.ai")
            from core.ai import generate_llm_response
            logger.info("Successfully imported generate_llm_response")

            # Use generate_llm_response to handle the API call
            # This ensures all API calls go through a single function
            logger.info("Calling generate_llm_response")
            response_text = generate_llm_response(
                prompt=prompt,
                model_name=model_name
            )
            logger.info(f"Response received from generate_llm_response, type: {type(response_text)}")
            logger.info(f"Response length: {len(response_text) if isinstance(response_text, str) else 'N/A'}")

            # Log the first 100 characters of the response for debugging
            if isinstance(response_text, str) and response_text:
                logger.info(f"Response (first 100 chars): {response_text[:100]}...")
            else:
                logger.info(f"Response is empty or not a string: {response_text}")

            # Additional logging specific to this function (optional)
            try:
                logger.info("Attempting to log AI interaction")
                from core.ai import log_ai_interaction
                log_ai_interaction(
                    function_name="generate_ai_response",
                    prompt=prompt,
                    response=response_text,
                    model_name=model_name
                )
                logger.info("Successfully logged AI interaction")
            except Exception as log_error:
                logger.warning(f"Error logging AI interaction: {log_error}")
                logger.warning(f"Error details: {str(log_error)}")

            logger.info("=== EXITING generate_ai_response with success ===")
            return response_text

        except ImportError as import_error:
            logger.error(f"Error importing generate_llm_response from core.ai: {import_error}")
            logger.error(f"Import error details: {str(import_error)}")

            # Fall back to initialize_ai if import fails
            logger.info("Falling back to direct API call")
            initialize_ai()

            # Configure the model
            logger.info(f"Configuring model: {model_name}")
            model = genai.GenerativeModel(model_name)

            # Generate the response
            logger.info("Generating content with direct API call")
            response = model.generate_content(prompt)
            logger.info(f"Response received from direct API call, type: {type(response)}")

            # Log warning about fallback
            logger.warning("Using fallback direct API call in generate_ai_response")

            # Return the text
            logger.info("=== EXITING generate_ai_response with fallback success ===")
            return response.text
    except Exception as e:
        logger.error(f"Error generating AI response: {e}")
        logger.error(f"Exception type: {type(e)}")
        logger.error(f"Exception details: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Log the failed interaction
        try:
            logger.info("Attempting to log failed AI interaction")
            from core.ai import log_ai_interaction
            log_ai_interaction(
                function_name="generate_ai_response_failed",
                prompt=prompt,
                response=f"ERROR: {str(e)}",
                model_name=model_name
            )
            logger.info("Successfully logged failed AI interaction")
        except Exception as log_error:
            logger.error(f"Error logging failed AI interaction: {log_error}")
            logger.error(f"Error details: {str(log_error)}")

        logger.info("=== EXITING generate_ai_response with error ===")
        return ""

def analyze_test_cases(test_cases, elements):
    """
    Analyze test cases using LLM to intelligently understand test steps and match UI elements.
    Can process either a list of all test cases or a single test case.

    Args:
        test_cases (list or dict): Either a list of test cases or a single test case dictionary
        elements (list): List of UI elements to check against

    Returns:
        dict: Analysis results with matched elements and metadata
    """
    if not test_cases:
        return {
            'valid_test_cases': [],
            'errors': [{'Test Case ID': 'N/A', 'Errors': ['No test cases provided']}],
            'warnings': []
        }

    valid_test_cases = []
    errors = []
    warnings = []

    # Handle single test case passed as dictionary
    if isinstance(test_cases, dict):
        test_cases = [test_cases]
    elif not isinstance(test_cases, list):
        return {
            'valid_test_cases': [],
            'errors': [{'Test Case ID': 'N/A', 'Errors': ['Invalid test case format']}],
            'warnings': []
        }

    # Process each test case
    for test_case in test_cases:
        try:
            if not isinstance(test_case, dict):
                continue

            tc_id = test_case.get('Test Case ID', '')
            if not tc_id:
                continue

            # Create standardized test case structure
            processed_test_case = {
                'Test Case ID': tc_id,
                'Test Case Objective': test_case.get('Test Case Objective', ''),
                'Prerequisite': test_case.get('Prerequisite', ''),
                'Priority': test_case.get('Priority', ''),
                'Test Type': test_case.get('Test Type', ''),
                'Test Group': test_case.get('Test Group', ''),
                'Steps': []
            }

            # Process steps if they exist
            steps = test_case.get('Steps', [])
            if isinstance(steps, list):
                for step in steps:
                    if not isinstance(step, dict):
                        continue

                    # Create standardized step structure
                    processed_step = {
                        'Step No': int(step.get('Step No', 0)),
                        'Test Steps': step.get('Test Steps', ''),
                        'Expected Result': step.get('Expected Result', ''),
                        '_metadata': {
                            'matched_elements': [],
                            'requires_page_load': 'page' in step.get('Test Steps', '').lower() or 'navigate' in step.get('Test Steps', '').lower(),
                            'dynamic_content': any(word in step.get('Test Steps', '').lower() for word in ['load', 'wait', 'appear']),
                            'validation_required': any(word in step.get('Expected Result', '').lower() for word in ['verify', 'check', 'confirm']),
                            'confidence': 0.0,
                            'state_changes': [],
                            'dependencies': []
                        }
                    }
                    processed_test_case['Steps'].append(processed_step)

            if processed_test_case['Steps']:
                valid_test_cases.append(processed_test_case)
            else:
                warnings.append(f"Test case {tc_id} has no valid steps")

        except Exception as e:
            tc_id = test_case.get('Test Case ID', 'Unknown') if isinstance(test_case, dict) else 'Unknown'
            errors.append({
                'Test Case ID': tc_id,
                'Errors': [f'Error processing test case: {str(e)}']
            })

    # Add warning if no valid test cases found
    if not valid_test_cases:
        warnings.append("No valid test cases could be processed")

    return {
        'valid_test_cases': valid_test_cases,
        'errors': errors,
        'warnings': warnings
    }

def process_single_test_case(test_case, elements):
    """
    Process a single test case for better LLM context management.

    Args:
        test_case (dict): Single test case with its steps
        elements (list): List of detected UI elements

    Returns:
        dict: Processed test case with matched elements
    """
    # First analyze the test case
    analysis = analyze_test_cases(test_case, elements)
    if not analysis['valid_test_cases']:
        return None

    # Get the processed test case
    processed_test_case = analysis['valid_test_cases'][0]

    # Match elements for each step with focused context
    for step in processed_test_case['Steps']:
        # Create focused prompt for this specific step
        prompt = f"""
        Analyzing a single test step to find matching UI elements.

        Test Case ID: {processed_test_case['Test Case ID']}
        Test Step Number: {step['Step No']}
        Action: {step['Test Steps']}
        Expected Result: {step['Expected Result']}

        Available UI Elements:
        {json.dumps(elements, indent=2)}

        Please identify:
        1. The most relevant UI elements needed for this step
        2. Confidence level for each match (0-1)
        3. Required actions for each element
        4. Any state changes or dependencies
        """

        # Get AI response for this specific step
        response = generate_ai_response(prompt)

        try:
            # Process AI response and update step metadata
            element_matches = []
            for element in elements:
                # Check if element matches this step's requirements
                match_prompt = f"""
                Does this UI element match the requirements for:
                Action: {step['Test Steps']}
                Expected: {step['Expected Result']}

                Element: {json.dumps(element, indent=2)}

                Return a JSON with: {{"matches": boolean, "confidence": float, "reason": string}}
                """
                match_response = generate_ai_response(match_prompt)

                try:
                    match_data = json.loads(match_response)
                    if match_data.get('matches', False):
                        element['confidence'] = match_data.get('confidence', 0.0)
                        element['match_reason'] = match_data.get('reason', '')
                        element_matches.append(element)
                except:
                    continue

            # Update step metadata with matched elements
            step['_metadata']['matched_elements'] = sorted(
                element_matches,
                key=lambda x: x.get('confidence', 0),
                reverse=True
            )

        except Exception as e:
            logger.error(f"Error processing step {step['Step No']}: {e}")
            step['_metadata']['error'] = str(e)

    return processed_test_case

def fallback_analyze_test_cases(test_cases, elements):
    """
    Fallback analysis using basic validation when AI analysis fails.
    // ...existing code for basic validation...
    """

def filter_elements_for_step(elements, action, expected, top_n=5):
    """
    Generalized: Filter UI elements to those most relevant for the given test step action/expected.
    Uses a scoring system based on keyword/attribute overlap and similarity.
    Returns a list of up to top_n elements.
    """
    import re
    from difflib import SequenceMatcher

    def tokenize(text):
        # Lowercase, remove punctuation, split into words
        return re.findall(r'\w+', (text or '').lower())

    action_tokens = set(tokenize(action))
    expected_tokens = set(tokenize(expected))
    all_tokens = action_tokens | expected_tokens

    def element_score(element):
        attrs = element.get('attributes', {})
        score = 0
        # Check tag, type, name, placeholder, value, text, role
        for key in ['tag', 'type', 'name', 'placeholder', 'value', 'text', 'role', 'id', 'class']:
            val = (attrs.get(key) or '').lower()
            if not val:
                continue
            val_tokens = set(tokenize(val))
            # Overlap with action/expected tokens
            overlap = all_tokens & val_tokens
            score += len(overlap) * 3
            # Fuzzy match (partial string similarity)
            for token in all_tokens:
                if token and val:
                    ratio = SequenceMatcher(None, token, val).ratio()
                    if ratio > 0.7:
                        score += 2 * ratio
        # Boost for visible elements
        if attrs.get('visible', False):
            score += 2
        # Boost for elements with importance_score
        score += element.get('importance_score', 0) * 0.5
        return score

    # Score all elements
    scored = [(element_score(el), el) for el in elements]
    # Sort by score descending
    scored.sort(reverse=True, key=lambda x: x[0])
    # Take top N with score > 0, or fallback to top N
    filtered = [el for s, el in scored if s > 0][:top_n]
    if not filtered:
        filtered = [el for s, el in scored][:top_n]
    return filtered

def match_elements_with_ai(test_cases, elements, api_key=None):
    if isinstance(test_cases, dict):
        test_cases = [test_cases]
    elif not isinstance(test_cases, list):
        return {}

    if not test_cases or not elements:
        return {}

    if api_key:
        genai.configure(api_key=api_key)
    elif not initialize_ai():
        return {}

    matches = {}
    for test_case in test_cases:
        try:
            if not isinstance(test_case, dict):
                continue
            tc_id = test_case.get('Test Case ID') or test_case.get('id')
            if not tc_id:
                continue
            matches[tc_id] = {}
            steps = test_case.get('Steps', [])
            if not isinstance(steps, list):
                continue
            for i, step in enumerate(steps):
                if not isinstance(step, dict):
                    continue
                step_num = step.get('Step No') or step.get('step_no') or str(i+1)
                action = step.get('Test Steps') or step.get('action', '')
                expected = step.get('Expected Result') or step.get('expected', '')
                prompt = f"""
                Analyze this test step and find matching UI elements if any are needed. If no UI element is required (e.g., navigation or page presence), return an empty matches list.
                Test Case: {tc_id}
                Step {step_num}:
                Action: {action}
                Expected: {expected}
                Available UI Elements (JSON array):
                {json.dumps(elements, indent=2)}
                For each relevant UI element, determine:
                1. How well it matches the step requirements (confidence score 0-1)
                2. What action should be performed (click, input, verify, clear, etc.)
                3. Any specific data or values needed
                Return ONLY valid JSON in this format (no explanation, no markdown):
                {
                    "matches": [
                        {
                            "element": {"element object"},
                            "score": float,
                            "action": string,
                            "data": string or null
                        }
                    ]
                }
                """
                try:
                    response = generate_ai_response(prompt)
                    if not response or not response.strip():
                        matches[tc_id][str(step_num)] = []
                        continue
                    try:
                        cleaned_response = response.strip()
                        if cleaned_response.startswith("```"):
                            cleaned_response = cleaned_response.lstrip('`')
                            if cleaned_response.lower().startswith("json"):
                                cleaned_response = cleaned_response[4:].strip()
                            elif cleaned_response.lower().startswith("python"):
                                cleaned_response = cleaned_response[6:].strip()
                            if cleaned_response.endswith("```"):
                                cleaned_response = cleaned_response[:-3].strip()
                        matches_data = json.loads(cleaned_response)
                    except Exception:
                        matches[tc_id][str(step_num)] = []
                        continue
                    step_matches = []
                    for match in matches_data.get('matches', []):
                        step_matches.append(match)
                    step_matches.sort(key=lambda x: x.get('score', 0), reverse=True)
                    matches[tc_id][str(step_num)] = step_matches
                except Exception:
                    pass
        except Exception:
            continue
    return matches

def get_test_cases_for_dropdown(test_cases):
    """
    Extract test case information for populating the dropdown.

    Args:
        test_cases (list): List of parsed test cases

    Returns:
        list: List of dicts with test case ID and objective for dropdown
    """
    dropdown_items = []

    # First analyze the test cases to ensure proper structure
    analysis = analyze_test_cases(test_cases, [])  # Empty elements list since we don't need matching yet

    for test_case in analysis['valid_test_cases']:
        dropdown_items.append({
            'id': test_case['Test Case ID'],
            'objective': test_case['Test Case Objective'],
            'step_count': len(test_case['Steps']),
            'priority': test_case.get('Priority', 'Medium'),
            'test_type': test_case.get('Test Type', 'Functional')
        })

    return dropdown_items

def analyze_selected_test_case(test_case_id, all_test_cases, elements):
    """
    Analyze a single selected test case against detected UI elements.

    Args:
        test_case_id (str): ID of the selected test case
        all_test_cases (list): List of all parsed test cases
        elements (list): List of detected UI elements

    Returns:
        dict: Analysis results for the selected test case with matched elements
    """
    # Find the selected test case
    selected_test_case = None
    for tc in all_test_cases:
        if isinstance(tc, dict) and tc.get('Test Case ID') == test_case_id:
            selected_test_case = tc
            break

    if not selected_test_case:
        return {
            'error': f'Test case {test_case_id} not found',
            'matched_elements': []
        }

    # Process just this test case
    processed = process_single_test_case(selected_test_case, elements)
    if not processed:
        return {
            'error': 'Failed to process test case',
            'matched_elements': []
        }

    # Format results for easy consumption by the UI
    results = {
        'test_case': {
            'id': processed['Test Case ID'],
            'objective': processed['Test Case Objective'],
            'priority': processed.get('Priority', 'Medium'),
            'test_type': processed.get('Test Type', 'Functional'),
            'prerequisite': processed.get('Prerequisite', '')
        },
        'steps': []
    }

    # Process each step's matches
    for step in processed['Steps']:
        step_result = {
            'step_no': step['Step No'],
            'action': step['Test Steps'],
            'expected': step['Expected Result'],
            'matched_elements': [],
            'metadata': step['_metadata']
        }

        # Include matched elements with confidence scores
        for element in step['_metadata']['matched_elements']:
            element_info = {
                'name': element.get('name', ''),
                'selector': element.get('selector', ''),
                'selector_type': element.get('selector_type', ''),
                'confidence': element.get('confidence', 0.0),
                'match_reason': element.get('match_reason', ''),
                'attributes': element.get('attributes', {})
            }
            step_result['matched_elements'].append(element_info)

        results['steps'].append(step_result)

    return results
def filter_elements_for_step(elements, action, expected, top_n=10):
    """
    Filter UI elements to those most relevant for the given test step action/expected.
    Uses a scoring system based on keyword/attribute overlap and similarity.
    Returns a list of up to top_n elements.

    Args:
        elements (list): List of UI elements
        action (str): Test step action text
        expected (str): Test step expected result text
        top_n (int): Maximum number of elements to return

    Returns:
        list: Filtered list of elements most relevant to the step
    """
    import re
    from difflib import SequenceMatcher

    def tokenize(text):
        # Lowercase, remove punctuation, split into words
        return re.findall(r'\w+', (text or '').lower())

    # Extract common action keywords
    action_keywords = {
        'click': ['click', 'press', 'select', 'choose', 'tap'],
        'input': ['enter', 'type', 'input', 'fill', 'write'],
        'verify': ['verify', 'check', 'confirm', 'validate', 'ensure', 'assert'],
        'navigate': ['navigate', 'go', 'visit', 'open', 'browse'],
        'wait': ['wait', 'pause', 'delay'],
        'select': ['select', 'choose', 'pick'],
        'hover': ['hover', 'mouseover', 'move'],
        'drag': ['drag', 'move', 'shift'],
        'upload': ['upload', 'attach', 'file']
    }

    # Identify action types in the step
    action_lower = action.lower()
    action_types = []
    for action_type, keywords in action_keywords.items():
        if any(keyword in action_lower for keyword in keywords):
            action_types.append(action_type)

    # Tokenize action and expected text
    action_tokens = set(tokenize(action))
    expected_tokens = set(tokenize(expected))
    all_tokens = action_tokens | expected_tokens

    def element_score(element):
        attrs = element.get('attributes', {})
        score = 0

        # Check tag, type, name, placeholder, value, text, role
        for key in ['tag', 'type', 'name', 'placeholder', 'value', 'text', 'role', 'id', 'class']:
            val = (attrs.get(key) or '').lower()
            if not val:
                continue

            val_tokens = set(tokenize(val))

            # Overlap with action/expected tokens
            overlap = all_tokens & val_tokens
            score += len(overlap) * 3

            # Fuzzy match (partial string similarity)
            for token in all_tokens:
                if token and val:
                    ratio = SequenceMatcher(None, token, val).ratio()
                    if ratio > 0.7:
                        score += 2 * ratio

        # Boost score based on element type and action types
        element_type = attrs.get('tag', '').lower()

        # Boost for input elements when action is input-related
        if 'input' in action_types and element_type in ['input', 'textarea', 'select']:
            score += 5

        # Boost for clickable elements when action is click-related
        if 'click' in action_types and element_type in ['button', 'a', 'input']:
            if attrs.get('type') in ['submit', 'button', 'checkbox', 'radio']:
                score += 5

        # Boost for visible elements
        if attrs.get('visible', False):
            score += 3

        # Boost for elements with importance_score
        score += element.get('importance_score', 0) * 0.5

        return score

    # Score all elements
    scored = [(element_score(el), el) for el in elements]

    # Sort by score descending
    scored.sort(reverse=True, key=lambda x: x[0])

    # Take top N with score > 0, or fallback to top N
    filtered = [el for s, el in scored if s > 0][:top_n]
    if not filtered:
        filtered = [el for s, el in scored][:top_n]

    # Add scores to elements for debugging
    for i, el in enumerate(filtered):
        el['relevance_score'] = scored[i][0]

    return filtered