"""
Stage Monitor Utility

This module provides comprehensive monitoring and debugging capabilities for stage transitions
to help identify and prevent unexpected stage reversions.

CRITICAL FIX: Added to help diagnose and prevent the Stage 1 reversion issue.
"""

import logging
import streamlit as st
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field

logger = logging.getLogger("ScriptWeaver.stage_monitor")


@dataclass
class StageTransition:
    """Record of a stage transition event."""
    timestamp: datetime
    from_stage: str
    to_stage: str
    reason: str
    stack_trace: str
    session_id: str
    state_snapshot: Dict[str, Any]


class StageMonitor:
    """
    Monitor and track stage transitions to identify problematic patterns.

    This class helps diagnose the root cause of unexpected Stage 1 reversions
    by maintaining a detailed history of all stage changes.
    """

    def __init__(self):
        self.transitions: List[StageTransition] = []
        self.max_history = 100  # Keep last 100 transitions
        self.session_id = self._generate_session_id()

    def _generate_session_id(self) -> str:
        """Generate a unique session identifier."""
        return f"session_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

    def record_transition(self, from_stage, to_stage, reason: str = "", state_obj=None):
        """
        Record a stage transition with full context.

        Args:
            from_stage: The stage being transitioned from
            to_stage: The stage being transitioned to
            reason: The reason for the transition
            state_obj: The state object for snapshot
        """
        import traceback

        # Create state snapshot
        state_snapshot = {}
        if state_obj:
            state_snapshot = {
                'uploaded_excel': bool(getattr(state_obj, 'uploaded_excel', None)),
                'uploaded_file': bool(getattr(state_obj, 'uploaded_file', None)),
                'test_cases': bool(getattr(state_obj, 'test_cases', None)),
                'website_url': getattr(state_obj, 'website_url', None),
                'selected_test_case': bool(getattr(state_obj, 'selected_test_case', None)),
                'conversion_done': bool(getattr(state_obj, 'conversion_done', None)),
                'step_table_json': bool(getattr(state_obj, 'step_table_json', None)),
                'selected_step': bool(getattr(state_obj, 'selected_step', None)),
                'step_matches': bool(getattr(state_obj, 'step_matches', None)),
                'element_matches': bool(getattr(state_obj, 'element_matches', None)),
                'test_data': bool(getattr(state_obj, 'test_data', None)),
                'test_data_skipped': bool(getattr(state_obj, 'test_data_skipped', None)),
                'generated_script_path': bool(getattr(state_obj, 'generated_script_path', None)),
                'all_steps_done': bool(getattr(state_obj, 'all_steps_done', None)),
            }

        # CRITICAL FIX: Normalize stage representations to avoid string/enum mismatches
        from_stage_str = self._normalize_stage_string(from_stage)
        to_stage_str = self._normalize_stage_string(to_stage)

        # Skip recording if it's the same stage (reduces noise)
        if from_stage_str == to_stage_str:
            logger.debug(f"Skipping same-stage transition: {from_stage_str}")
            return

        # Record the transition
        transition = StageTransition(
            timestamp=datetime.now(),
            from_stage=from_stage_str,
            to_stage=to_stage_str,
            reason=reason,
            stack_trace=''.join(traceback.format_stack()[-5:]),  # Last 5 stack frames
            session_id=self.session_id,
            state_snapshot=state_snapshot
        )

        self.transitions.append(transition)

        # Maintain history limit
        if len(self.transitions) > self.max_history:
            self.transitions = self.transitions[-self.max_history:]

        # Log the transition
        logger.info(f"STAGE TRANSITION: {from_stage} -> {to_stage} | Reason: {reason}")

        # Check for problematic patterns
        self._check_for_problems()

    def _normalize_stage_string(self, stage) -> str:
        """
        Normalize stage representation to a consistent string format.

        This handles both StateStage enums and string representations,
        ensuring consistent stage tracking regardless of input format.
        """
        if hasattr(stage, 'value'):
            # StateStage enum - use the enum value
            return stage.value
        elif hasattr(stage, 'get_display_name'):
            # StateStage enum with display name method
            return stage.value
        elif isinstance(stage, str):
            # String representation - normalize to enum value format
            stage_lower = stage.lower()
            if "stage 1" in stage_lower or "upload" in stage_lower:
                return "stage1_upload"
            elif "stage 2" in stage_lower or "website" in stage_lower:
                return "stage2_website"
            elif "stage 3" in stage_lower or "convert" in stage_lower:
                return "stage3_convert"
            elif "stage 4" in stage_lower or "detect" in stage_lower:
                return "stage4_detect"
            elif "stage 5" in stage_lower or "data" in stage_lower:
                return "stage5_data"
            elif "stage 6" in stage_lower or "generate" in stage_lower:
                return "stage6_generate"
            elif "stage 7" in stage_lower or "execute" in stage_lower:
                return "stage7_execute"
            elif "stage 8" in stage_lower or "optimize" in stage_lower:
                return "stage8_optimize"
            elif "stage 9" in stage_lower or "browse" in stage_lower:
                return "stage9_browse"
            else:
                return stage  # Return as-is if no match
        else:
            # Unknown type - convert to string
            return str(stage)

    def _check_for_problems(self):
        """Check for problematic stage transition patterns."""
        if len(self.transitions) < 2:
            return

        recent_transitions = self.transitions[-10:]  # Last 10 transitions

        # Check for rapid Stage 1 reversions
        stage1_reversions = [
            t for t in recent_transitions
            if t.to_stage.endswith('STAGE1_UPLOAD') and not t.from_stage.endswith('STAGE1_UPLOAD')
        ]

        if len(stage1_reversions) >= 2:
            logger.warning("CRITICAL ISSUE: Multiple Stage 1 reversions detected!")
            for reversion in stage1_reversions[-2:]:
                logger.warning(f"  → {reversion.from_stage} -> {reversion.to_stage} at {reversion.timestamp}")
                logger.warning(f"    Reason: {reversion.reason}")

        # Check for rapid back-and-forth transitions (improved logic)
        if len(recent_transitions) >= 6:  # Increased threshold to reduce false positives
            last_6 = recent_transitions[-6:]
            stages = [t.to_stage for t in last_6]
            unique_stages = set(stages)

            # Only flag if we have exactly 2 stages alternating rapidly
            if len(unique_stages) == 2:
                # Check if transitions happened within a short time window (30 seconds)
                time_window = timedelta(seconds=30)
                first_time = last_6[0].timestamp
                last_time = last_6[-1].timestamp

                if (last_time - first_time) < time_window:
                    # Check for actual back-and-forth pattern (A->B->A->B)
                    stage_list = list(unique_stages)
                    pattern_detected = False

                    for i in range(len(stages) - 1):
                        if stages[i] != stages[i + 1]:  # Different consecutive stages
                            pattern_detected = True
                        else:
                            pattern_detected = False
                            break

                    if pattern_detected:
                        logger.warning("CRITICAL ISSUE: Rapid back-and-forth stage transitions detected!")
                        logger.warning(f"  → Stages: {' -> '.join(stages)}")
                        logger.warning(f"  → Time window: {(last_time - first_time).total_seconds():.1f} seconds")

    def get_recent_transitions(self, minutes: int = 5) -> List[StageTransition]:
        """Get transitions from the last N minutes."""
        cutoff = datetime.now() - timedelta(minutes=minutes)
        return [t for t in self.transitions if t.timestamp > cutoff]

    def get_stage1_reversions(self, minutes: int = 10) -> List[StageTransition]:
        """Get all Stage 1 reversions in the last N minutes."""
        recent = self.get_recent_transitions(minutes)
        return [
            t for t in recent
            if t.to_stage.endswith('STAGE1_UPLOAD') and not t.from_stage.endswith('STAGE1_UPLOAD')
        ]

    def display_debug_info(self):
        """Display debug information in Streamlit."""
        if not self.transitions:
            return

        with st.expander("🔍 Stage Transition Debug Info", expanded=False):
            st.markdown("**Recent Stage Transitions:**")

            recent = self.get_recent_transitions(10)  # Last 10 minutes
            if recent:
                for transition in recent[-5:]:  # Show last 5
                    time_str = transition.timestamp.strftime("%H:%M:%S")
                    st.text(f"{time_str}: {transition.from_stage} → {transition.to_stage}")
                    if transition.reason:
                        st.text(f"  Reason: {transition.reason}")
            else:
                st.text("No recent transitions")

            # Show Stage 1 reversions
            reversions = self.get_stage1_reversions(10)
            if reversions:
                st.markdown("**⚠️ Stage 1 Reversions (Last 10 min):**")
                for reversion in reversions:
                    time_str = reversion.timestamp.strftime("%H:%M:%S")
                    st.error(f"{time_str}: {reversion.from_stage} → {reversion.to_stage}")
                    st.text(f"  Reason: {reversion.reason}")


# Global monitor instance
_stage_monitor = None


def get_stage_monitor() -> StageMonitor:
    """Get the global stage monitor instance."""
    global _stage_monitor
    if _stage_monitor is None:
        _stage_monitor = StageMonitor()
    return _stage_monitor


def record_stage_transition(from_stage, to_stage, reason: str = "", state_obj=None):
    """Convenience function to record a stage transition."""
    monitor = get_stage_monitor()
    monitor.record_transition(from_stage, to_stage, reason, state_obj)


def display_stage_debug_info():
    """Convenience function to display debug info."""
    monitor = get_stage_monitor()
    monitor.display_debug_info()
