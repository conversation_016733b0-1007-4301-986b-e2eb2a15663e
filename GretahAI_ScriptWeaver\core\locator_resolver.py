"""
Locator Conflict Resolution for GretahAI ScriptWeaver

This module provides heuristic preprocessing to resolve conflicts between
step data locators and element matches data before sending to AI generation.

Key Features:
- Prioritizes manually selected elements over automated matches
- Uses reliability-based locator strategy ranking (ID > CSS > NAME > CLASS > XPATH)
- Resolves conflicts by choosing highest confidence score
- Creates unified locator recommendations for AI consumption
- Comprehensive logging for debugging and transparency

The resolver eliminates AI confusion by preprocessing conflicting locator
information and providing a single, authoritative locator recommendation.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from enum import Enum
from dataclasses import dataclass
from debug_utils import debug

# Set up logging
logger = logging.getLogger(__name__)

class LocatorStrategy(Enum):
    """Enum representing different locator strategies with their string values."""
    ID = "id"
    CSS = "css"
    CSS_SELECTOR = "css_selector"
    NAME = "name"
    CLASS = "class"
    XPATH = "xpath"
    TAG = "tag"
    TEXT = "text"
    URL = "url" 
    NONE = "none"
    
    @classmethod
    def from_string(cls, value: str) -> 'LocatorStrategy':
        """Convert string to enum member, defaulting to NONE for unknown values."""
        try:
            return cls(value.lower()) if value else cls.NONE
        except ValueError:
            return cls.NONE
            
    def __str__(self) -> str:
        return self.value

# Locator strategy reliability ranking (higher is better)
# Aligned with priority order: ID > CSS > NAME > CLASS > XPATH
LOCATOR_STRATEGY_RELIABILITY = {
    LocatorStrategy.ID: 100,
    LocatorStrategy.CSS: 80, 
    LocatorStrategy.CSS_SELECTOR: 80,
    LocatorStrategy.NAME: 70,
    LocatorStrategy.CLASS: 50,
    LocatorStrategy.XPATH: 60,
    LocatorStrategy.TAG: 30,
    LocatorStrategy.TEXT: 40,
    LocatorStrategy.URL: 90,  # For navigation steps
    LocatorStrategy.NONE: 10
}

@dataclass
class ElementMatch:
    """Represents a matched element with its score and selection status."""
    element: Dict[str, Any]
    score: float
    manually_selected: bool = False
    
@dataclass
class ResolutionResult:
    """Represents the result of locator conflict resolution."""
    resolved_locator_strategy: str
    resolved_locator: str
    resolution_reason: str
    confidence_score: float
    original_step_locator: Dict[str, str]
    original_element_matches: List[Dict[str, Any]]
    conflict_detected: bool


def resolve_locator_conflicts(
    step_data: Dict[str, Any],
    element_matches: List[Dict[str, Any]],
    step_no: str,
    test_case_id: str = "unknown"
) -> Dict[str, Any]:
    """
    Resolve conflicts between step data locators and element matches.
    
    This function implements heuristic preprocessing to eliminate conflicting
    locator information before sending data to AI generation prompts.
    
    Args:
        step_data: Step data from JSON storage with locator_strategy and locator
        element_matches: List of element matches with selector, xpath, score, manually_selected
        step_no: Step number for logging context
        test_case_id: Test case ID for logging context
        
    Returns:
        Dict containing resolved locator information with:
        - resolved_locator_strategy: The chosen locator strategy
        - resolved_locator: The chosen locator value
        - resolution_reason: Explanation of why this locator was chosen
        - confidence_score: Confidence in the resolution (0.0-1.0)
        - original_step_locator: Original step data locator for reference
        - original_element_matches: Original element matches for reference
        - conflict_detected: Boolean indicating if conflicts were found
    """
    try:
        debug(f"Resolving locator conflicts for test case {test_case_id}, step {step_no}")

        # Defensive check for None step_data
        if step_data is None:
            debug(f"Step data is None - using default values")
            step_data = {}

        # Extract step data locator information
        step_locator_strategy = step_data.get('locator_strategy', '').lower()
        step_locator = step_data.get('locator', '')
        
        debug(f"Step data locator: strategy='{step_locator_strategy}', locator='{step_locator}'")
        debug(f"Element matches count: {len(element_matches)}")
        
        # Initialize result structure
        result = ResolutionResult(
            resolved_locator_strategy=step_locator_strategy,
            resolved_locator=step_locator,
            resolution_reason='No conflicts detected - using step data locator',
            confidence_score=0.8,  # Default confidence for step data
            original_step_locator={'strategy': step_locator_strategy, 'locator': step_locator},
            original_element_matches=element_matches,
            conflict_detected=False
        )
        
        # If no element matches, use step data as-is
        if not element_matches:
            debug(f"No element matches found - using step data locator")
            return vars(result)
            
        # Check for conflicts by examining element matches
        conflicts_found = _detect_locator_conflicts(step_data, element_matches)
        
        if not conflicts_found:
            debug(f"No locator conflicts detected")
            return vars(result)
            
        # Conflicts detected - apply resolution heuristics
        debug(f"Locator conflicts detected - applying resolution heuristics")
        result.conflict_detected = True
        
        # Apply resolution heuristics
        resolved_locator = _apply_resolution_heuristics(
            step_data, element_matches, step_no, test_case_id
        )
        
        # Update result with resolved information
        for key, value in resolved_locator.items():
            setattr(result, key, value)
        
        # Log resolution decision
        logger.info(f"Locator conflict resolved for {test_case_id} step {step_no}: "
                   f"{result.resolution_reason}")
        debug(f"Final resolved locator: strategy='{result.resolved_locator_strategy}', "
              f"locator='{result.resolved_locator}', confidence={result.confidence_score}")
        
        return vars(result)
        
    except Exception as e:
        logger.exception(f"Error resolving locator conflicts for {test_case_id} step {step_no}")
        # Return safe fallback with defensive handling for None step_data
        safe_step_data = step_data if step_data is not None else {}
        
        # Use original strategy if available, otherwise NONE
        original_strategy = safe_step_data.get('locator_strategy', '')
        
        return {
            'resolved_locator_strategy': original_strategy if original_strategy else LocatorStrategy.NONE.value,
            'resolved_locator': safe_step_data.get('locator', ''),
            'resolution_reason': f'Error during resolution - fallback to step data: {str(e)}',
            'confidence_score': 0.5,
            'original_step_locator': {'strategy': safe_step_data.get('locator_strategy', ''),
                                    'locator': safe_step_data.get('locator', '')},
            'original_element_matches': element_matches if element_matches is not None else [],
            'conflict_detected': True
        }


def _detect_locator_conflicts(step_data: Dict[str, Any], element_matches: List[Dict[str, Any]]) -> bool:
    """
    Detect if there are conflicts between step data and element matches.
    
    Args:
        step_data: Step data with locator information
        element_matches: List of element matches
        
    Returns:
        bool: True if conflicts are detected, False otherwise
    """
    try:
        step_locator = step_data.get('locator', '').strip()
        step_strategy = step_data.get('locator_strategy', '').lower().strip()
        
        # No conflict if step has no locator or is navigation
        if not step_locator or step_strategy in ['url', 'none', '']:
            return False
            
        # No conflict if no element matches
        if not element_matches:
            return False
            
        # Check if any element match has different selector information
        for match in element_matches:
            element = match.get('element', {})
            element_selector = element.get('selector', '')
            element_xpath = element.get('xpath', '')
            
            # Determine element's implied strategy
            element_strategy = 'css' if element_selector else 'xpath' if element_xpath else 'none'
            
            # Check for strategy mismatch (NEW)
            if element_strategy != step_strategy and step_strategy not in ['', 'none']:
                debug(f"Strategy conflict detected: step strategy '{step_strategy}' vs element strategy '{element_strategy}'")
                return True
            
            # If element has selector/xpath that differs from step locator, it's a conflict
            if element_selector and element_selector != step_locator:
                debug(f"Conflict detected: step locator '{step_locator}' vs element selector '{element_selector}'")
                return True
                
            if element_xpath and element_xpath != step_locator:
                debug(f"Conflict detected: step locator '{step_locator}' vs element xpath '{element_xpath}'")
                return True
                
        return False

    except Exception as e:
        logger.exception(f"Error detecting locator conflicts")
        return False


def _apply_resolution_heuristics(
    step_data: Dict[str, Any],
    element_matches: List[Dict[str, Any]],
    step_no: str,
    test_case_id: str
) -> Dict[str, Any]:
    """
    Apply heuristic rules to resolve locator conflicts.

    Priority order:
    1. Manually selected elements (manually_selected: true)
    2. Highest confidence score element
    3. Most reliable locator strategy (ID > CSS > NAME > CLASS > XPath)
    4. Step data as fallback

    Args:
        step_data: Step data with locator information
        element_matches: List of element matches
        step_no: Step number for logging
        test_case_id: Test case ID for logging

    Returns:
        Dict with resolved locator information
    """
    try:
        # Convert to ElementMatch objects for easier handling
        typed_matches = [
            ElementMatch(
                element=match.get('element', {}),
                score=match.get('score', 0),
                manually_selected=match.get('manually_selected', False)
            )
            for match in element_matches
        ]
        
        # Rule 1: Check for manually selected elements - get highest scoring one if multiple
        manually_selected = [match for match in typed_matches if match.manually_selected]

        if manually_selected:
            debug(f"Found {len(manually_selected)} manually selected elements")
            
            # If multiple manual selections, pick highest score
            if len(manually_selected) > 1:
                best_match = max(manually_selected, key=lambda m: m.score)
                debug(f"Selected highest scoring manually selected element (score: {best_match.score})")
            else:
                best_match = manually_selected[0]
                
            return _create_resolution_result(
                best_match,
                "Manually selected element (highest priority)",
                1.0  # Perfect confidence for manual selection
            )

        # Rule 2 & 3: Single scan to find highest score and most reliable locator
        if typed_matches:
            highest_score = max(match.score for match in typed_matches)
            high_score_threshold = max(0.7, highest_score * 0.9)  # Within 10% of highest or 0.7 minimum
            
            # Filter to high-scoring matches
            high_score_matches = [
                match for match in typed_matches
                if match.score >= high_score_threshold
            ]
            
            debug(f"Found {len(high_score_matches)} high-scoring elements (threshold: {high_score_threshold})")
            
            # Evaluate locator strategy reliability for high-scoring matches in one pass
            best_match = None
            best_reliability = -1
            
            for match in high_score_matches:
                reliability = _calculate_locator_reliability(match.element)
                if reliability > best_reliability:
                    best_reliability = reliability
                    best_match = match
            
            if best_match:
                confidence = min(0.95, best_match.score + 0.1)  # Boost confidence slightly
                return _create_resolution_result(
                    best_match,
                    f"Highest confidence element with reliable locator strategy (score: {best_match.score:.2f})",
                    confidence
                )

        # Rule 4: Fallback to step data
        debug(f"No suitable element matches found - falling back to step data")
        step_locator_strategy = step_data.get('locator_strategy', LocatorStrategy.NONE.value)
        step_locator = step_data.get('locator', '')

        return {
            'resolved_locator_strategy': step_locator_strategy,
            'resolved_locator': step_locator,
            'resolution_reason': 'Fallback to step data - no suitable element matches found',
            'confidence_score': 0.6
        }

    except Exception as e:
        logger.exception(f"Error applying resolution heuristics")
        # Safe fallback - use original strategy or NONE
        original_strategy = step_data.get('locator_strategy', '')
        return {
            'resolved_locator_strategy': original_strategy if original_strategy else LocatorStrategy.NONE.value,
            'resolved_locator': step_data.get('locator', ''),
            'resolution_reason': f'Error in heuristics - fallback to step data: {str(e)}',
            'confidence_score': 0.5
        }


def _calculate_locator_reliability(element: Dict[str, Any]) -> int:
    """
    Calculate reliability score for an element's locator strategies.

    Args:
        element: Element data with selector, xpath, and attributes

    Returns:
        int: Reliability score (higher is better)
    """
    try:
        max_score = 0

        # Fast path: Check for ID attribute first
        attributes = element.get('attributes', {})
        if attributes.get('id'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID]
            
        # Fast path: Check for data-test* attributes
        if any(k.startswith('data-test') for k in attributes.keys()):
            return 90  # Very high reliability for test attributes
        
        # Check CSS selector reliability
        selector = element.get('selector', '')
        if selector:
            css_score = _evaluate_css_selector_reliability(selector)
            max_score = max(max_score, css_score)

        # Check XPath reliability
        xpath = element.get('xpath', '')
        if xpath:
            xpath_score = _evaluate_xpath_reliability(xpath)
            max_score = max(max_score, xpath_score)

        return max_score

    except Exception as e:
        logger.exception(f"Error calculating locator reliability")
        return 0


def _evaluate_css_selector_reliability(selector: str) -> int:
    """
    Evaluate the reliability of a CSS selector.

    Args:
        selector: CSS selector string

    Returns:
        int: Reliability score
    """
    try:
        if not selector:
            return 0
            
        # Fast path: ID selectors are most reliable
        if selector.startswith('#'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID]
            
        # Fast path: data-test* attributes are highly reliable
        if '[data-testid=' in selector or '[data-test=' in selector:
            return 90
            
        # Fast path: ID-based attribute selectors
        if '[id=' in selector:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID] - 5

        if '[name=' in selector:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]

        # Class selectors are moderately reliable
        if '.' in selector and not selector.startswith('.'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] - 10

        if selector.startswith('.'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CLASS]

        # Tag selectors are less reliable
        if selector.isalpha():
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.TAG]

        # Complex selectors get moderate score
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] - 20

    except Exception as e:
        logger.exception(f"Error evaluating CSS selector reliability")
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] // 2


def _evaluate_xpath_reliability(xpath: str) -> int:
    """
    Evaluate the reliability of an XPath selector.

    Args:
        xpath: XPath selector string

    Returns:
        int: Reliability score
    """
    try:
        if not xpath:
            return 0
            
        # Fast path: ID-based XPath is highly reliable
        if '@id=' in xpath:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID] - 10
            
        # Fast path: data-test* attributes in XPath
        if '@data-testid=' in xpath or '@data-test=' in xpath:
            return 85

        if '@name=' in xpath:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] - 10

        # Text-based XPath is moderately reliable
        if 'text()=' in xpath:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.TEXT]

        # Position-based XPath is less reliable
        if '[' in xpath and ']' in xpath and any(char.isdigit() for char in xpath):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH] - 20

        # Generic XPath
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH]

    except Exception as e:
        logger.exception(f"Error evaluating XPath reliability")
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH] // 2


def _create_resolution_result(
    element_match: Union[ElementMatch, Dict[str, Any]],
    reason: str,
    confidence: float
) -> Dict[str, Any]:
    """
    Create a standardized resolution result from an element match.

    Args:
        element_match: The selected element match
        reason: Reason for selection
        confidence: Confidence score (0.0-1.0)

    Returns:
        Dict with resolved locator information
    """
    try:
        # Handle both ElementMatch and dictionary inputs
        if isinstance(element_match, ElementMatch):
            element = element_match.element
        else:
            element = element_match.get('element', {})

        # Determine best locator strategy and value
        selector = element.get('selector', '')
        xpath = element.get('xpath', '')

        # Prefer CSS selector over XPath if both exist
        if selector:
            if selector.startswith('#'):
                strategy = LocatorStrategy.ID.value
                locator = selector[1:]  # Remove # prefix for ID
            else:
                strategy = LocatorStrategy.CSS.value
                locator = selector
        elif xpath:
            strategy = LocatorStrategy.XPATH.value
            locator = xpath
        else:
            # Fallback to element attributes
            attributes = element.get('attributes', {})
            if attributes.get('id'):
                strategy = LocatorStrategy.ID.value
                locator = attributes['id']
            elif attributes.get('name'):
                strategy = LocatorStrategy.NAME.value
                locator = attributes['name']
            else:
                strategy = LocatorStrategy.NONE.value
                locator = ''  # Empty locator to force error

        return {
            'resolved_locator_strategy': strategy,
            'resolved_locator': locator,
            'resolution_reason': reason,
            'confidence_score': confidence
        }

    except Exception as e:
        logger.exception(f"Error creating resolution result")
        return {
            'resolved_locator_strategy': LocatorStrategy.NONE.value,
            'resolved_locator': '',
            'resolution_reason': f'Error creating result - using fallback: {str(e)}',
            'confidence_score': 0.3
        }
