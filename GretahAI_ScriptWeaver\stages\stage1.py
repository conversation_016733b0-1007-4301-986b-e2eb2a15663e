"""
Stage 1: Upload Excel File

This module handles the Excel file upload and parsing functionality.
Maintains the StateManager pattern and follows the established architectural patterns.
"""

import os
import logging
import tempfile
import streamlit as st
import pandas as pd
from pathlib import Path
from state_manager import StateStage

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage1")

# Import helper functions from other modules
from core.excel_parser import parse_excel
from debug_utils import debug

def validate_uploaded_file(uploaded_file, file_content):
    """
    Validate uploaded file before processing.

    Args:
        uploaded_file: Streamlit uploaded file object
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of validation errors (empty if valid)
    """
    errors = []

    # File size validation (50MB limit)
    if len(file_content) == 0:
        errors.append("File is empty")
    elif len(file_content) > 50 * 1024 * 1024:
        errors.append("File too large (max 50MB)")

    # File extension validation
    if not uploaded_file.name.lower().endswith('.xlsx'):
        errors.append("Invalid file extension (must be .xlsx)")

    # Basic Excel file signature check
    if len(file_content) >= 2 and not file_content.startswith(b'PK'):
        errors.append("File does not appear to be a valid Excel file")

    return errors

def safe_get_test_case_count(test_cases):
    """
    Safely get test case count with validation.

    Args:
        test_cases: Test cases data structure

    Returns:
        int: Number of test cases (0 if invalid)
    """
    if not test_cases:
        return 0
    if not isinstance(test_cases, list):
        debug(f"Warning: test_cases is not a list, type: {type(test_cases)}")
        return 0
    return len(test_cases)

def validate_stage1_completion(state):
    """
    Validate Stage 1 completion criteria.

    Args:
        state: StateManager instance

    Returns:
        tuple: (is_valid: bool, message: str)
    """
    if not hasattr(state, 'test_cases') or not state.test_cases:
        return False, "No test cases loaded"

    if not hasattr(state, 'uploaded_excel') or not state.uploaded_excel:
        return False, "No Excel file uploaded"

    if not os.path.exists(state.uploaded_excel):
        return False, "Uploaded file no longer exists"

    return True, "Stage 1 completed successfully"

@st.cache_data
def parse_excel_cached(file_content):
    """
    Cached version of parse_excel function with improved resource management.

    Args:
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of test cases
    """
    debug("Using cached parse_excel function")
    temp_file_path = None

    try:
        # Create a temporary file to pass to parse_excel
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        # Parse the Excel file using the existing function
        test_cases = parse_excel(temp_file_path)
        return test_cases

    except Exception as e:
        debug(f"Error in cached parse_excel: {e}")
        logger.error(f"Error in cached parse_excel: {e}")
        raise
    finally:
        # Ensure cleanup even if parsing fails
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except Exception as cleanup_error:
                debug(f"Warning: Failed to cleanup temp file {temp_file_path}: {cleanup_error}")

def stage1_upload_excel(state):
    """Phase 1: Upload Excel File."""
    st.markdown("<h2 class='stage-header'>Phase 1: Upload Test Case Excel</h2>", unsafe_allow_html=True)

    # Help text in an expander to reduce visual clutter
    with st.expander("About Excel Format", expanded=False):
        st.markdown("""
        Upload an Excel file with the following columns:
        - **Test Case ID**: Unique identifier
        - **Test Case Objective**: Description of what is being tested
        - **Step No**: Step number
        - **Test Steps**: Action to perform
        - **Expected Result**: Expected outcome
        """)

    # Simplified file uploader with clearer label
    uploaded_file = st.file_uploader("Select Excel file (.xlsx)", type=["xlsx"], key="excel_uploader")

    if uploaded_file is not None:
        try:
            # Get the file content
            file_content = uploaded_file.getvalue()

            # Validate uploaded file before processing
            validation_errors = validate_uploaded_file(uploaded_file, file_content)
            if validation_errors:
                for error in validation_errors:
                    st.error(f"❌ {error}")
                return

            # Check if this is the same file we've already processed
            current_hash = hash(file_content)
            if hasattr(state, 'last_file_content_hash') and state.last_file_content_hash == current_hash:
                debug("File content unchanged - skipping reprocessing")
                logger.info("File content unchanged - skipping reprocessing")
                # Still show success message and preview for user feedback
                st.success(f"✅ File already processed: {uploaded_file.name}")
            else:
                debug("New or changed file detected - processing")
                logger.info("New or changed file detected - processing")
                # Update the content hash in state
                old_hash = getattr(state, 'last_file_content_hash', None)
                state.last_file_content_hash = current_hash
                debug(f"State change: last_file_content_hash = {current_hash} (was: {old_hash})")

                # Save the uploaded file to a temporary location
                temp_dir = Path("temp_uploads")
                temp_dir.mkdir(exist_ok=True)

                # Use a consistent filename based on the uploaded file name instead of timestamp
                safe_filename = ''.join(c if c.isalnum() else '_' for c in uploaded_file.name)
                temp_file_path = temp_dir / f"test_cases_{safe_filename}"

                with open(temp_file_path, "wb") as f:
                    f.write(file_content)

                # Update state with file information
                old_excel_path = getattr(state, 'uploaded_excel', None)
                state.uploaded_excel = str(temp_file_path)
                state.uploaded_file = str(temp_file_path)  # Backward compatibility
                debug(f"State change: uploaded_excel = {state.uploaded_excel} (was: {old_excel_path})")

                # Process file in a collapsible section
                with st.expander("Processing Results", expanded=True):
                    # Verify the file was saved correctly
                    if os.path.exists(temp_file_path) and os.path.getsize(temp_file_path) > 0:
                        st.success(f"✅ File uploaded: {uploaded_file.name}")

                        # Parse the excel file using the cached function
                        if parse_excel:
                            try:
                                old_test_cases_count = safe_get_test_case_count(getattr(state, 'test_cases', None))
                                state.test_cases = parse_excel_cached(file_content)
                                new_test_cases_count = safe_get_test_case_count(state.test_cases)
                                debug(f"State change: test_cases count = {new_test_cases_count} (was: {old_test_cases_count})")

                                if new_test_cases_count == 0:
                                    st.warning("⚠️ No test cases found. Check file format.")
                                else:
                                    st.success(f"✅ Parsed {new_test_cases_count} test cases")

                                    # Only advance to Stage 2 if we're currently in Stage 1
                                    if state.current_stage == StateStage.STAGE1_UPLOAD:
                                        # Validate completion before advancing
                                        is_valid, validation_message = validate_stage1_completion(state)

                                        if is_valid:
                                            # Use centralized stage advancement with proper state persistence
                                            success = state.advance_to(StateStage.STAGE2_WEBSITE, f"Successfully loaded {new_test_cases_count} test cases")

                                            if success:
                                                # Force state update in session state
                                                st.session_state['state'] = state
                                                st.session_state['stage_progression_message'] = f"✅ {validation_message}. Proceeding to Website Configuration."

                                                # Log successful transition for debugging
                                                debug(f"Stage 1 → Stage 2 transition successful")
                                                logger.info(f"Stage 1 -> Stage 2 transition successful, test_cases count: {new_test_cases_count}")

                                                # Rerun to show Stage 2 immediately
                                                st.rerun()
                                                return  # Exit early since rerun will restart the function
                                            else:
                                                debug("Stage transition failed")
                                                logger.error("Failed to advance from Stage 1 to Stage 2")
                                                st.error("❌ Failed to advance to next stage. Please try again.")
                                        else:
                                            st.warning(f"⚠️ Cannot proceed: {validation_message}")
                            except Exception as e:
                                debug(f"Error parsing file: {e}")
                                st.error(f"❌ Error parsing file: {e}")
                                state.test_cases = None # Ensure it's reset on error
                        else:
                            st.warning("⚠️ Excel parsing function not available")
                    else:
                        st.error("❌ Failed to save file")

            # Always display a preview of the Excel file (using the cached file if available)
            if hasattr(state, 'uploaded_excel') and os.path.exists(state.uploaded_excel):
                try:
                    df = pd.read_excel(state.uploaded_excel) # Read from the saved temp file
                    with st.expander("📊 File Preview", expanded=True):
                        # Show essential metric prominently
                        if hasattr(state, 'test_cases'):
                            test_case_count = safe_get_test_case_count(state.test_cases)
                            if test_case_count > 0:
                                st.success(f"✅ **{test_case_count} test cases** successfully parsed")
                            else:
                                st.warning("⚠️ No test cases found in file")

                        # Clean, focused data preview
                        st.caption("First 10 rows:")
                        st.dataframe(df.head(10), use_container_width=True, hide_index=True)
                except Exception as e:
                    debug(f"Error reading file for preview: {e}")
                    st.error(f"❌ Error reading file: {e}")
        except Exception as e:
            debug(f"Error processing file: {e}")
            st.error(f"❌ Error processing file: {e}")
