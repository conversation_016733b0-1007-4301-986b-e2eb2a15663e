#!/usr/bin/env python3
"""
Debug Script for Hybrid Editing Data Flow Investigation

This script helps investigate the data flow issue where hybrid-edited test case steps
are not being properly used as the single source of truth in subsequent step-level stages.

Usage:
    python debug_hybrid_data_flow.py

This script will:
1. Set up logging to capture all data flow operations
2. Simulate the hybrid editing workflow
3. Track data flow through stages 3, 4, 5, and 6
4. Identify where the data flow breaks down
5. Generate a comprehensive report
"""

import sys
import os
import logging
from datetime import datetime
from typing import Dict, List, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_comprehensive_logging():
    """Set up comprehensive logging for data flow investigation."""
    
    # Create logs directory if it doesn't exist
    logs_dir = "debug_logs"
    os.makedirs(logs_dir, exist_ok=True)
    
    # Create timestamped log file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(logs_dir, f"hybrid_data_flow_debug_{timestamp}.log")
    
    # Configure root logger
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set specific loggers to DEBUG level
    loggers_to_debug = [
        "ScriptWeaver.state_manager",
        "ScriptWeaver.stage3",
        "ScriptWeaver.stage4", 
        "ScriptWeaver.stage5",
        "ScriptWeaver.stage6",
        "ScriptWeaver.hybrid_editing"
    ]
    
    for logger_name in loggers_to_debug:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)
    
    print(f"Comprehensive logging set up. Log file: {log_file}")
    return log_file

def create_test_state_with_hybrid_editing():
    """Create a test state manager with hybrid editing enabled."""
    from state_manager import StateManager
    
    logger = logging.getLogger("debug_hybrid_data_flow")
    logger.info("=== Creating test state with hybrid editing ===")
    
    # Create state manager
    state = StateManager()
    
    # Set up basic test case data
    state.selected_test_case = {
        'Test Case ID': 'TC001',
        'Test Case Name': 'Login Test',
        'Steps': [
            {
                'Step No': '1',
                'Test Steps': 'Navigate to login page',
                'Expected Result': 'Login page loads'
            },
            {
                'Step No': '2', 
                'Test Steps': 'Enter username',
                'Expected Result': 'Username entered'
            }
        ]
    }
    
    # Set up original step table (AI-generated)
    state.step_table_json = [
        {
            'step_no': '1',
            'action': 'Navigate to login page',
            'locator': 'https://example.com/login',
            'locator_strategy': 'url',
            'expected_result': 'Login page loads',
            '_is_ai_generated': True
        },
        {
            'step_no': '2',
            'action': 'Enter username',
            'locator': '#username',
            'locator_strategy': 'css_selector',
            'expected_result': 'Username entered',
            '_is_ai_generated': True
        }
    ]
    
    # Enable hybrid editing
    state.enable_hybrid_editing()
    
    # Add manual step insertion (simulating hybrid editing)
    manual_step = {
        'action': 'Wait for page to fully load',
        'locator': '',
        'locator_strategy': 'none',
        'expected_result': 'Page fully loaded',
        '_is_manual': True
    }
    
    # Insert manual step before step 1
    state.step_insertion_points = {
        'before_1': [manual_step]
    }
    
    # Create combined step table (simulating "Apply to Test Case" action)
    state.combined_step_table = [
        {
            'step_no': '1',
            'action': 'Wait for page to fully load',
            'locator': '',
            'locator_strategy': 'none', 
            'expected_result': 'Page fully loaded',
            '_is_manual': True
        },
        {
            'step_no': '2',
            'action': 'Navigate to login page',
            'locator': 'https://example.com/login',
            'locator_strategy': 'url',
            'expected_result': 'Login page loads',
            '_is_ai_generated': True
        },
        {
            'step_no': '3',
            'action': 'Enter username',
            'locator': '#username',
            'locator_strategy': 'css_selector',
            'expected_result': 'Username entered',
            '_is_ai_generated': True
        }
    ]
    
    # Update step table to use combined steps
    state.step_table_json = state.combined_step_table.copy()
    state.total_steps = len(state.combined_step_table)
    
    logger.info(f"Test state created with {len(state.step_table_json)} steps")
    logger.info(f"Hybrid editing enabled: {state.hybrid_editing_enabled}")
    logger.info(f"Combined step table: {len(state.combined_step_table)} steps")
    
    return state

def test_get_effective_step_table(state):
    """Test the get_effective_step_table function."""
    logger = logging.getLogger("debug_hybrid_data_flow")
    logger.info("=== Testing get_effective_step_table() ===")
    
    effective_steps = state.get_effective_step_table()
    
    logger.info(f"Effective step table returned {len(effective_steps)} steps")
    for i, step in enumerate(effective_steps):
        logger.info(f"  Step {i+1}: {step.get('step_no')} - {step.get('action')} (manual: {step.get('_is_manual', False)})")
    
    return effective_steps

def simulate_stage4_step_selection(state, step_no='1'):
    """Simulate Stage 4 step selection process."""
    logger = logging.getLogger("debug_hybrid_data_flow")
    logger.info(f"=== Simulating Stage 4 step selection for step {step_no} ===")
    
    # Get effective step table (this is what Stage 4 should use)
    step_table_json = state.get_effective_step_table()
    
    # Find the selected step in the step table
    selected_step_table_entry = next(
        (step for step in step_table_json if str(step.get('step_no')) == step_no),
        None
    )
    
    # Find original step (for comparison)
    original_steps = state.selected_test_case.get('Steps', [])
    selected_original_step = next(
        (step for step in original_steps if str(step.get('Step No')) == step_no),
        None
    )
    
    # If no original step found, create synthetic one (hybrid editing case)
    if not selected_original_step and selected_step_table_entry:
        selected_original_step = {
            'Step No': step_no,
            'Test Steps': selected_step_table_entry.get('action', 'Manual step'),
            'Expected Result': selected_step_table_entry.get('expected_result', 'Step completed'),
            '_is_synthetic': True,
            '_source': 'hybrid_editing'
        }
    
    # Store in state (simulating Stage 4 behavior)
    if selected_step_table_entry and selected_original_step:
        state.selected_step_table_entry = selected_step_table_entry
        state.selected_step = selected_original_step
        
        logger.info(f"Stage 4 simulation: Stored step data for step {step_no}")
        logger.info(f"  - selected_step_table_entry.action: '{selected_step_table_entry.get('action')}'")
        logger.info(f"  - selected_step.Test Steps: '{selected_original_step.get('Test Steps')}'")
        
        # Validate data consistency
        if hasattr(state, 'validate_step_data_consistency'):
            validation_results = state.validate_step_data_consistency("Stage 4 Simulation")
    
    return selected_step_table_entry, selected_original_step

def simulate_stage6_script_generation(state):
    """Simulate Stage 6 script generation process."""
    logger = logging.getLogger("debug_hybrid_data_flow")
    logger.info("=== Simulating Stage 6 script generation ===")
    
    # Get step data (this is what Stage 6 should use)
    selected_step = state.selected_step
    selected_step_table_entry = state.selected_step_table_entry
    
    if not selected_step or not selected_step_table_entry:
        logger.error("Stage 6 simulation: Missing step data!")
        return None
    
    # Log what data would be sent to AI
    step_no = selected_step.get('Step No')
    step_table_action = selected_step_table_entry.get('action', '')
    original_action = selected_step.get('Test Steps', '')
    
    logger.info(f"Stage 6 simulation: Data that would be sent to AI for step {step_no}:")
    logger.info(f"  - step_table_entry.action: '{step_table_action}'")
    logger.info(f"  - step_table_entry.locator: '{selected_step_table_entry.get('locator', '')}'")
    logger.info(f"  - step_table_entry.expected_result: '{selected_step_table_entry.get('expected_result', '')}'")
    
    # Check for hybrid editing
    actions_match = step_table_action.lower() == original_action.lower()
    if not actions_match:
        logger.warning(f"Stage 6 simulation: HYBRID EDITING DETECTED!")
        logger.warning(f"  - Original: '{original_action}'")
        logger.warning(f"  - Hybrid: '{step_table_action}'")
        logger.warning(f"  - AI should receive: '{step_table_action}'")
    
    # Validate data consistency
    if hasattr(state, 'validate_step_data_consistency'):
        validation_results = state.validate_step_data_consistency("Stage 6 Simulation")
        
        if not validation_results["data_sources_consistent"]:
            logger.error("Stage 6 simulation: DATA INCONSISTENCY DETECTED!")
            return validation_results
    
    return {
        "step_no": step_no,
        "ai_would_receive": step_table_action,
        "original_action": original_action,
        "hybrid_detected": not actions_match,
        "validation_results": validation_results if 'validation_results' in locals() else None
    }

def main():
    """Main debug function."""
    print("🔍 Starting Hybrid Editing Data Flow Investigation")
    print("=" * 60)
    
    # Set up logging
    log_file = setup_comprehensive_logging()
    logger = logging.getLogger("debug_hybrid_data_flow")
    
    try:
        # Create test state with hybrid editing
        state = create_test_state_with_hybrid_editing()
        
        # Test get_effective_step_table
        effective_steps = test_get_effective_step_table(state)
        
        # Simulate Stage 4 step selection for step 1 (should be manual step)
        step_table_entry, original_step = simulate_stage4_step_selection(state, '1')
        
        # Simulate Stage 6 script generation
        script_gen_results = simulate_stage6_script_generation(state)
        
        # Generate summary report
        print("\n" + "=" * 60)
        print("🔍 INVESTIGATION SUMMARY")
        print("=" * 60)
        
        if script_gen_results and isinstance(script_gen_results, dict):
            if script_gen_results.get("hybrid_detected"):
                print("✅ HYBRID EDITING CORRECTLY DETECTED")
                print(f"   AI would receive: '{script_gen_results['ai_would_receive']}'")
                print(f"   Original action: '{script_gen_results['original_action']}'")
            else:
                print("❌ HYBRID EDITING NOT DETECTED (potential issue)")
        
        print(f"\n📋 Full debug log saved to: {log_file}")
        print("\n🔍 Check the log file for detailed data flow analysis")
        
    except Exception as e:
        logger.error(f"Error during investigation: {e}")
        import traceback
        logger.error(traceback.format_exc())
        print(f"❌ Investigation failed. Check log file: {log_file}")

if __name__ == "__main__":
    main()
