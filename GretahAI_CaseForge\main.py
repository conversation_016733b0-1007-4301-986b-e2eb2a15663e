"""
DEPRECATED: This file is no longer the main entry point.

Please use: streamlit run gui/app.py

This file is kept for backward compatibility and contains database
initialization utilities that have been moved to gui/app.py.
"""

import sys

def main():
    """
    Display deprecation notice and redirect users to the correct entry point.
    """
    print("=" * 60)
    print("DEPRECATED ENTRY POINT")
    print("=" * 60)
    print("This entry point (main.py) has been deprecated.")
    print("Please use the following command instead:")
    print()
    print("    streamlit run gui/app.py")
    print()
    print("The application has been consolidated to have a single")
    print("entry point for better maintainability.")
    print("=" * 60)

    # Exit with error code to indicate this shouldn't be used
    sys.exit(1)

if __name__ == "__main__":
    main()
